<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Heritage Explorer - Database Viewer</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #e63946, #f77f00);
      color: white;
      padding: 30px;
      text-align: center;
    }

    .header h1 {
      font-size: 2.5rem;
      margin-bottom: 10px;
    }

    .header p {
      opacity: 0.9;
      font-size: 1.1rem;
    }

    .content {
      padding: 30px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .stat-card {
      background: #f8f9fa;
      border-radius: 10px;
      padding: 20px;
      text-align: center;
      border-left: 4px solid #e63946;
    }

    .stat-card h3 {
      color: #e63946;
      font-size: 2rem;
      margin-bottom: 10px;
    }

    .stat-card p {
      color: #666;
      font-weight: 500;
    }

    .section {
      margin-bottom: 30px;
      background: #f8f9fa;
      border-radius: 10px;
      padding: 20px;
    }

    .section h2 {
      color: #e63946;
      margin-bottom: 20px;
      font-size: 1.5rem;
      border-bottom: 2px solid #e63946;
      padding-bottom: 10px;
    }

    .user-card, .site-card {
      background: white;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 15px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      border-left: 4px solid #f77f00;
    }

    .user-card h4, .site-card h4 {
      color: #333;
      margin-bottom: 10px;
    }

    .user-info, .site-info {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 10px;
      font-size: 0.9rem;
      color: #666;
    }

    .refresh-btn {
      background: linear-gradient(135deg, #e63946, #f77f00);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 1rem;
      font-weight: 600;
      margin-bottom: 20px;
      transition: transform 0.2s ease;
    }

    .refresh-btn:hover {
      transform: translateY(-2px);
    }

    .loading {
      text-align: center;
      padding: 40px;
      color: #666;
    }

    .spinner {
      border: 3px solid #f3f3f3;
      border-top: 3px solid #e63946;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .error {
      background: #f8d7da;
      color: #721c24;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
      border-left: 4px solid #dc3545;
    }

    .back-link {
      display: inline-block;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      text-decoration: none;
      padding: 10px 20px;
      border-radius: 8px;
      margin-top: 15px;
      transition: background 0.3s ease;
    }

    .back-link:hover {
      background: rgba(255, 255, 255, 0.3);
      color: white;
      text-decoration: none;
    }

    @media (max-width: 768px) {
      .container {
        margin: 10px;
      }
      
      .header {
        padding: 20px;
      }
      
      .header h1 {
        font-size: 2rem;
      }
      
      .content {
        padding: 20px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🗄️ Database Viewer</h1>
      <p>Heritage Explorer - MongoDB Database Overview</p>
      <a href="website.html" class="back-link">← Back to Heritage Explorer</a>
    </div>

    <div class="content">
      <button class="refresh-btn" onclick="loadDatabaseData()">🔄 Refresh Data</button>

      <div id="loading" class="loading" style="display: none;">
        <div class="spinner"></div>
        <p>Loading database information...</p>
      </div>

      <div id="error" class="error" style="display: none;"></div>

      <div id="stats" class="stats-grid" style="display: none;">
        <div class="stat-card">
          <h3 id="userCount">0</h3>
          <p>Total Users</p>
        </div>
        <div class="stat-card">
          <h3 id="siteCount">0</h3>
          <p>Heritage Sites</p>
        </div>
        <div class="stat-card">
          <h3 id="favoriteCount">0</h3>
          <p>Total Favorites</p>
        </div>
        <div class="stat-card">
          <h3 id="visitCount">0</h3>
          <p>Site Visits</p>
        </div>
      </div>

      <div id="usersSection" class="section" style="display: none;">
        <h2>👥 Registered Users</h2>
        <div id="usersList"></div>
      </div>

      <div id="sitesSection" class="section" style="display: none;">
        <h2>🏛️ Heritage Sites</h2>
        <div id="sitesList"></div>
      </div>
    </div>
  </div>

  <script>
    async function loadDatabaseData() {
      const loading = document.getElementById('loading');
      const error = document.getElementById('error');
      const stats = document.getElementById('stats');
      const usersSection = document.getElementById('usersSection');
      const sitesSection = document.getElementById('sitesSection');

      // Show loading
      loading.style.display = 'block';
      error.style.display = 'none';
      stats.style.display = 'none';
      usersSection.style.display = 'none';
      sitesSection.style.display = 'none';

      try {
        // Fetch users data
        const usersResponse = await fetch('/api/admin/users');
        const sitesResponse = await fetch('/api/admin/sites');

        if (!usersResponse.ok || !sitesResponse.ok) {
          throw new Error('Failed to fetch data from server');
        }

        const usersData = await usersResponse.json();
        const sitesData = await sitesResponse.json();

        // Update statistics
        updateStats(usersData, sitesData);
        
        // Update users list
        updateUsersList(usersData.users || []);
        
        // Update sites list
        updateSitesList(sitesData.sites || []);

        // Show sections
        stats.style.display = 'grid';
        usersSection.style.display = 'block';
        sitesSection.style.display = 'block';

      } catch (err) {
        console.error('Error loading database data:', err);
        error.textContent = `Error: ${err.message}. Make sure the server is running and you have admin access.`;
        error.style.display = 'block';
      } finally {
        loading.style.display = 'none';
      }
    }

    function updateStats(usersData, sitesData) {
      const users = usersData.users || [];
      const sites = sitesData.sites || [];
      
      let totalFavorites = 0;
      let totalVisits = 0;
      
      users.forEach(user => {
        totalFavorites += (user.favorites || []).length;
        totalVisits += (user.visitedSites || []).length;
      });

      document.getElementById('userCount').textContent = users.length;
      document.getElementById('siteCount').textContent = sites.length;
      document.getElementById('favoriteCount').textContent = totalFavorites;
      document.getElementById('visitCount').textContent = totalVisits;
    }

    function updateUsersList(users) {
      const usersList = document.getElementById('usersList');
      
      if (users.length === 0) {
        usersList.innerHTML = '<p>No users found.</p>';
        return;
      }

      usersList.innerHTML = users.map(user => `
        <div class="user-card">
          <h4>${user.firstName} ${user.lastName} (@${user.username})</h4>
          <div class="user-info">
            <div><strong>Email:</strong> ${user.email}</div>
            <div><strong>Status:</strong> ${user.isActive ? 'Active' : 'Inactive'}</div>
            <div><strong>Language:</strong> ${user.preferences?.language || 'en'}</div>
            <div><strong>Joined:</strong> ${new Date(user.createdAt).toLocaleDateString()}</div>
            <div><strong>Last Login:</strong> ${user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}</div>
            <div><strong>Favorites:</strong> ${(user.favorites || []).length}</div>
            <div><strong>Visited:</strong> ${(user.visitedSites || []).length}</div>
          </div>
        </div>
      `).join('');
    }

    function updateSitesList(sites) {
      const sitesList = document.getElementById('sitesList');
      
      if (sites.length === 0) {
        sitesList.innerHTML = '<p>No heritage sites found.</p>';
        return;
      }

      sitesList.innerHTML = sites.map(site => `
        <div class="site-card">
          <h4>${site.name}</h4>
          <div class="site-info">
            <div><strong>Location:</strong> ${site.location}</div>
            <div><strong>Category:</strong> ${site.category}</div>
            <div><strong>Added:</strong> ${site.createdAt ? new Date(site.createdAt).toLocaleDateString() : 'N/A'}</div>
          </div>
        </div>
      `).join('');
    }

    // Load data when page loads
    document.addEventListener('DOMContentLoaded', loadDatabaseData);
  </script>
</body>
</html>
