<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mahabalipuram - Heritage Explorer</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
  <style>
    :root {
      --primary-color: #1a237e;
      --secondary-color: #457b9d;
      --accent-color: #e63946;
      --light-color: #f1faee;
      --dark-color: #1d3557;
      --success-color: #2e7d32;
      --warning-color: #ff9800;
      --danger-color: #c62828;
      --gray-100: #f8f9fa;
      --gray-200: #e9ecef;
      --gray-300: #dee2e6;
      --gray-400: #ced4da;
      --gray-500: #adb5bd;
      --gray-600: #6c757d;
      --gray-700: #495057;
      --gray-800: #343a40;
      --gray-900: #212529;
      --border-radius: 0.5rem;
      --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
      --transition: all 0.3s ease-in-out;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: var(--gray-800);
      background-color: var(--gray-100);
    }

    header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      text-align: center;
    }

    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      background-color: white;
      box-shadow: var(--box-shadow);
    }

    nav a {
      color: var(--gray-700);
      text-decoration: none;
      margin: 0 1rem;
      font-weight: 500;
      transition: var(--transition);
    }

    nav a:hover {
      color: var(--primary-color);
    }

    main {
      max-width: 1200px;
      margin: 2rem auto;
      padding: 0 1rem;
    }

    .site-details {
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    @media (min-width: 768px) {
      .site-details {
        grid-template-columns: 1fr 1fr;
      }
    }

    .site-image {
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
    }

    .site-image img {
      width: 100%;
      height: auto;
      display: block;
    }

    .site-info h1 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .site-info p {
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }

    .site-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .meta-item {
      display: flex;
      align-items: center;
      background-color: var(--gray-200);
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
    }

    .meta-item i {
      margin-right: 0.5rem;
      color: var(--secondary-color);
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      margin-top: 1.5rem;
    }

    .action-buttons button {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      background-color: var(--secondary-color);
      color: white;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
      display: flex;
      align-items: center;
    }

    .action-buttons button i {
      margin-right: 0.5rem;
    }

    .action-buttons button:hover {
      background-color: var(--primary-color);
    }

    .site-description {
      margin-bottom: 2rem;
    }

    .site-description h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .map-container {
      height: 400px;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      margin-bottom: 2rem;
    }

    .nearby-attractions {
      margin-bottom: 2rem;
    }

    .nearby-attractions h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .attractions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .attraction-card {
      background-color: white;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
    }

    .attraction-card:hover {
      transform: translateY(-5px);
    }

    .attraction-card img {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }

    .attraction-card-content {
      padding: 1rem;
    }

    .attraction-card-content h3 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    footer {
      background-color: var(--primary-color);
      color: white;
      text-align: center;
      padding: 1rem;
      margin-top: 2rem;
    }

    #backToTop {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      width: 3rem;
      height: 3rem;
      background-color: var(--primary-color);
      color: white;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
      display: none;
    }

    #backToTop:hover {
      background-color: var(--secondary-color);
    }

    /* Travel Information Section */
    .travel-info {
      margin-bottom: 2rem;
      background-color: white;
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      overflow: hidden;
    }

    .travel-info-header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .travel-info-header h2 {
      margin: 0;
      font-size: 1.5rem;
    }

    .travel-info-tabs {
      display: flex;
      background-color: var(--gray-200);
    }

    .travel-tab {
      padding: 1rem;
      cursor: pointer;
      flex: 1;
      text-align: center;
      font-weight: 500;
      transition: var(--transition);
      border-bottom: 3px solid transparent;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .travel-tab i {
      margin-right: 0.5rem;
    }

    .travel-tab.active {
      background-color: white;
      border-bottom-color: var(--accent-color);
    }

    .travel-tab:hover:not(.active) {
      background-color: var(--gray-300);
    }

    .travel-content {
      padding: 1.5rem;
    }

    .travel-panel {
      display: none;
    }

    .travel-panel.active {
      display: block;
    }

    /* Weather Panel */
    .weather-info {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      align-items: center;
    }

    .weather-current {
      display: flex;
      align-items: center;
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      flex: 1;
      min-width: 250px;
    }

    .weather-icon {
      font-size: 3rem;
      margin-right: 1rem;
      color: var(--secondary-color);
    }

    .weather-details h3 {
      margin: 0;
      color: var(--primary-color);
    }

    .weather-temp {
      font-size: 2rem;
      font-weight: bold;
    }

    .weather-desc {
      color: var(--gray-700);
    }

    .weather-forecast {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      flex: 2;
      min-width: 300px;
    }

    .forecast-day {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      text-align: center;
      flex: 1;
      min-width: 100px;
    }

    .forecast-day h4 {
      margin: 0 0 0.5rem 0;
      color: var(--primary-color);
    }

    .forecast-icon {
      font-size: 1.5rem;
      margin: 0.5rem 0;
      color: var(--secondary-color);
    }

    /* Transportation Panel */
    .transport-options {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .transport-option {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
      display: flex;
      flex-direction: column;
    }

    .transport-option h3 {
      display: flex;
      align-items: center;
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .transport-option h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .transport-option p {
      margin-bottom: 0.5rem;
    }

    .transport-option .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    /* Hours & Tickets Panel */
    .hours-container {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .opening-hours, .ticket-info {
      flex: 1;
      min-width: 250px;
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .opening-hours h3, .ticket-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .opening-hours h3 i, .ticket-info h3 i {
      margin-right: 0.5rem;
    }

    .hours-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .hours-list li {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .hours-list li:last-child {
      border-bottom: none;
    }

    .day {
      font-weight: 500;
    }

    .ticket-type {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .ticket-type:last-child {
      border-bottom: none;
    }

    .ticket-price {
      font-weight: bold;
    }

    /* Amenities Panel */
    .amenities-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .amenity-card {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .amenity-card h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .amenity-card h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .amenity-card p {
      margin-bottom: 0.5rem;
    }

    .amenity-card .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    .amenity-card .rating {
      color: var(--warning-color);
      margin-bottom: 0.5rem;
    }

    .weather-tips h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .local-transport h3, .best-time h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .monument-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .monument-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .monument-item {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      margin-bottom: 1rem;
    }

    .monument-item h4 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
    }

    .monument-item h4 i {
      margin-right: 0.5rem;
    }

    .monument-details {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 0.5rem;
    }

    .monument-detail {
      display: flex;
      align-items: center;
      font-size: 0.9rem;
      color: var(--gray-700);
    }

    .monument-detail i {
      margin-right: 0.25rem;
      font-size: 0.8rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>Heritage Explorer</h1>
    <p>Discover Tamil Nadu's rich heritage and culture</p>
  </header>

  <nav>
    <div>
      <a href="website.html"><i class="fas fa-home"></i> Home</a>
      <a href="#"><i class="fas fa-map-marked-alt"></i> Explore</a>
      <a href="#"><i class="fas fa-info-circle"></i> About</a>
    </div>
    <div>
      <a href="#" id="toggleLang">தமிழ்</a>
    </div>
  </nav>

  <main>
    <div class="site-details">
      <div class="site-image">
        <img src="https://tse3.mm.bing.net/th?id=OIP.fOvloesqytzzB8BpKC0eAAHaE5&pid=Api&P=0&h=220" alt="Mahabalipuram">
      </div>
      <div class="site-info">
        <h1>Mahabalipuram</h1>
        <div class="site-meta">
          <div class="meta-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>Chennai, Tamil Nadu</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-monument"></i>
            <span>Monument</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-university"></i>
            <span>UNESCO World Heritage Site</span>
          </div>
        </div>
        <p>Famous for its rock-cut temples and sculptures, Mahabalipuram is an ancient port city that showcases the architectural brilliance of the Pallava dynasty, featuring magnificent monuments carved from single rocks.</p>
        <div class="action-buttons">
          <button id="playAudioButton"><i class="fas fa-volume-up"></i> Listen</button>
          <button id="favoriteButton"><i class="far fa-heart"></i> Favorite</button>
          <button id="shareButton"><i class="fas fa-share-alt"></i> Share</button>
        </div>
      </div>
    </div>

    <div class="site-description">
      <h2>About Mahabalipuram</h2>
      <p>Mahabalipuram, also known as Mamallapuram, is a historic coastal town located about 60 kilometers south of Chennai. It was a bustling seaport during the 7th and 8th centuries under the Pallava dynasty and is now a UNESCO World Heritage Site. The town is renowned for its extraordinary rock-cut monuments, monolithic rathas (temple chariots), cave sanctuaries, and giant open-air bas-reliefs that showcase the pinnacle of Pallava art and architecture.</p>
      <p>The most famous monument in Mahabalipuram is the Shore Temple, which stands on the shores of the Bay of Bengal. Built with blocks of granite during the 8th century, it's one of the oldest structural stone temples of South India. Another remarkable feature is the massive open-air bas-relief known as "Arjuna's Penance" or "Descent of the Ganges," which is carved on two huge adjacent boulders and depicts scenes from Hindu mythology. The Five Rathas, a set of monolithic temples shaped like chariots, demonstrate the evolution of Dravidian style architecture.</p>
      <p>Mahabalipuram is not just a historical site but also a living cultural center. The town continues to be a center for traditional stone carving, and visitors can watch artisans practicing this ancient craft. The annual Mamallapuram Dance Festival held in December-January celebrates classical Indian dance forms against the backdrop of these ancient monuments. The site also offers beautiful beaches, making it a perfect blend of historical exploration and coastal relaxation.</p>
    </div>

    <div class="map-container">
      <!-- Map will be embedded here -->
      <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d31099.31977465023!2d80.17192005!3d12.626504749999999!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3a53abe7eafbf793%3A0x130c6d6c1d8dd6d0!2sMahabalipuram%2C%20Tamil%20Nadu!5e0!3m2!1sen!2sin!4v1652345678901!5m2!1sen!2sin" width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy" title="Map of Mahabalipuram"></iframe>
    </div>

    <!-- Travel Information Section -->
    <div class="travel-info">
      <div class="travel-info-header">
        <h2>Practical Travel Information</h2>
        <div class="last-updated">Last updated: <span id="lastUpdated">Today</span></div>
      </div>

      <div class="travel-info-tabs">
        <div class="travel-tab active" data-tab="weather">
          <i class="fas fa-cloud-sun"></i> Weather
        </div>
        <div class="travel-tab" data-tab="transport">
          <i class="fas fa-bus"></i> Transportation
        </div>
        <div class="travel-tab" data-tab="hours">
          <i class="far fa-clock"></i> Hours & Monuments
        </div>
        <div class="travel-tab" data-tab="amenities">
          <i class="fas fa-concierge-bell"></i> Nearby Amenities
        </div>
      </div>

      <div class="travel-content">
        <!-- Weather Panel -->
        <div class="travel-panel active" id="weatherPanel">
          <div class="weather-info">
            <div class="weather-current">
              <div class="weather-icon">
                <i class="fas fa-sun"></i>
              </div>
              <div class="weather-details">
                <h3>Current Weather</h3>
                <div class="weather-temp">31°C</div>
                <div class="weather-desc">Sunny</div>
                <div class="weather-extra">Humidity: 75% | Wind: 12 km/h</div>
              </div>
            </div>

            <div class="weather-forecast">
              <div class="forecast-day">
                <h4>Tomorrow</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun"></i></div>
                <div class="forecast-temp">30°C</div>
                <div class="forecast-desc">Partly Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Wed</h4>
                <div class="forecast-icon"><i class="fas fa-cloud"></i></div>
                <div class="forecast-temp">29°C</div>
                <div class="forecast-desc">Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Thu</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun-rain"></i></div>
                <div class="forecast-temp">28°C</div>
                <div class="forecast-desc">Light Rain</div>
              </div>
            </div>
          </div>

          <div class="weather-tips">
            <h3>Weather Tips</h3>
            <p>Mahabalipuram has a tropical climate with hot and humid conditions throughout most of the year. The best time to visit is from November to February when the weather is relatively cooler with temperatures ranging from 20°C to 30°C. Summer (March-June) can be extremely hot with temperatures often exceeding 35°C.</p>
            <p>The coastal location means that sea breezes provide some relief from the heat, especially in the evenings. However, the humidity remains high year-round. The monsoon season (July-September) brings moderate rainfall to the region, which can occasionally disrupt outdoor sightseeing. Post-monsoon (October-November) offers lush green surroundings and is ideal for photography.</p>
            <p>When visiting the monuments, it's advisable to carry water, wear light cotton clothing, and use sun protection. An umbrella can be useful both for sun protection and during unexpected rain showers. Early morning (6:00 AM - 9:00 AM) or late afternoon (4:00 PM - 6:00 PM) visits are recommended to avoid the midday heat and to capture the monuments in the best light for photography. The Shore Temple is particularly beautiful during sunrise and sunset.</p>
          </div>
        </div>

        <!-- Transportation Panel -->
        <div class="travel-panel" id="transportPanel">
          <div class="transport-options">
            <div class="transport-option">
              <h3><i class="fas fa-plane"></i> By Air</h3>
              <p>Chennai International Airport is the nearest airport.</p>
              <p class="distance">60 km from Mahabalipuram (approx. 1.5-2 hours by car)</p>
              <p>Regular flights from all major Indian cities and international destinations.</p>
              <p>Pre-paid taxis available from the airport (₹1,500-2,000).</p>
              <p>App-based cabs like Ola and Uber also operate from the airport.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-train"></i> By Train</h3>
              <p>Chengalpattu Junction is the nearest railway station.</p>
              <p class="distance">30 km from Mahabalipuram (approx. 45 minutes by car)</p>
              <p>Well-connected to Chennai, Bangalore, and other major cities.</p>
              <p>Auto-rickshaws and taxis available from the station (₹600-800).</p>
              <p>Local buses also available from Chengalpattu to Mahabalipuram.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-bus"></i> By Bus</h3>
              <p>Regular state-run and private buses operate to Mahabalipuram.</p>
              <p>Direct buses from Chennai (CMBT) every 30 minutes.</p>
              <p>Journey time from Chennai: Approximately 2-2.5 hours.</p>
              <p>AC and non-AC buses available (₹80-150).</p>
              <p>The bus stand is located in the center of Mahabalipuram.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-car"></i> By Car</h3>
              <p>Well-connected via East Coast Road (ECR) from Chennai.</p>
              <p class="distance">60 km from Chennai (approx. 1.5-2 hours)</p>
              <p>Scenic coastal drive with good road conditions.</p>
              <p>Parking available near major monuments (₹30-50 for cars).</p>
              <p>Car rentals available in Chennai (₹2,000-3,000 per day).</p>
            </div>
          </div>

          <div class="local-transport">
            <h3>Local Transportation</h3>
            <p>Mahabalipuram is a relatively small town, and most of the major monuments are within walking distance of each other. The town center, where most hotels and restaurants are located, is about 1-2 km from the main monument complex. Walking is the best way to explore the monuments and enjoy the artistic details.</p>
            <p>For those who prefer not to walk, auto-rickshaws are readily available for short distances within the town (₹30-100 depending on the distance). Bicycle rentals (₹100-150 per day) are also popular among tourists and provide a pleasant way to explore the town and nearby areas. Some hotels offer bicycle rentals or can arrange them for you.</p>
            <p>For visiting attractions that are further away, such as Tiger Cave (5 km north) or Crocodile Bank (15 km north), hiring an auto-rickshaw for a few hours (₹500-700) or a taxi (₹1,000-1,500) is recommended. Many hotels can arrange guided tours of the monuments with transportation included. These typically cost ₹1,500-2,500 depending on the duration and places covered.</p>
          </div>
        </div>

        <!-- Hours & Monuments Panel -->
        <div class="travel-panel" id="hoursPanel">
          <div class="hours-container">
            <div class="opening-hours">
              <h3><i class="far fa-clock"></i> Opening Hours</h3>
              <ul class="hours-list">
                <li><span class="day">Shore Temple Complex:</span> <span class="time">6:00 AM - 6:00 PM</span></li>
                <li><span class="day">Pancha Rathas:</span> <span class="time">6:00 AM - 6:00 PM</span></li>
                <li><span class="day">Arjuna's Penance:</span> <span class="time">6:00 AM - 6:00 PM</span></li>
                <li><span class="day">Krishna's Butter Ball:</span> <span class="time">24 hours (open area)</span></li>
                <li><span class="day">Tiger Cave:</span> <span class="time">6:00 AM - 6:00 PM</span></li>
              </ul>
              <p class="hours-note">All ASI (Archaeological Survey of India) protected monuments are closed on national holidays. Photography is allowed in all monuments, but tripods may require special permission. The monuments are illuminated in the evenings during weekends and holidays, offering a different perspective of the ancient structures.</p>
            </div>

            <div class="ticket-info">
              <h3><i class="fas fa-ticket-alt"></i> Ticket Information</h3>
              <div class="ticket-type">
                <span>Indian Nationals:</span>
                <span class="ticket-price">₹40 per person</span>
              </div>
              <div class="ticket-type">
                <span>Foreign Nationals:</span>
                <span class="ticket-price">₹600 per person</span>
              </div>
              <div class="ticket-type">
                <span>SAARC & BIMSTEC Nationals:</span>
                <span class="ticket-price">₹40 per person</span>
              </div>
              <div class="ticket-type">
                <span>Combined Ticket (all monuments):</span>
                <span class="ticket-price">₹100 (Indians), ₹750 (Foreigners)</span>
              </div>
              <p class="ticket-note">Children below 15 years enter free. Tickets can be purchased at the entrance of each monument complex or at the ASI office near the Shore Temple. The combined ticket is valid for one day and provides access to all ASI protected monuments in Mahabalipuram. Credit/debit cards are accepted at the main ticket counters. It's advisable to carry cash for smaller vendors and services.</p>
            </div>
          </div>

          <div class="monument-info">
            <h3>Key Monuments</h3>
            <div class="monument-list">
              <div class="monument-item">
                <h4><i class="fas fa-landmark"></i> Shore Temple</h4>
                <div class="monument-details">
                  <span class="monument-detail"><i class="fas fa-clock"></i> Visit Duration: 1-1.5 hours</span>
                  <span class="monument-detail"><i class="fas fa-walking"></i> Difficulty: Easy</span>
                </div>
                <p>The iconic Shore Temple stands on the shores of the Bay of Bengal and is one of the oldest structural stone temples in South India. Built during the 8th century by the Pallava king Narasimhavarman II, it features three shrines dedicated to Lord Shiva and Lord Vishnu. The temple is known for its intricate carvings and structural elegance, despite centuries of exposure to sea erosion. The surrounding garden area offers beautiful views of the sea and is ideal for photography.</p>
              </div>

              <div class="monument-item">
                <h4><i class="fas fa-car-side"></i> Pancha Rathas (Five Chariots)</h4>
                <div class="monument-details">
                  <span class="monument-detail"><i class="fas fa-clock"></i> Visit Duration: 45 minutes - 1 hour</span>
                  <span class="monument-detail"><i class="fas fa-walking"></i> Difficulty: Easy</span>
                </div>
                <p>The Pancha Rathas are five monolithic temples carved from a single rock in the shape of chariots. Each ratha is named after the Pandava brothers and Draupadi from the Mahabharata epic. These structures showcase different styles of South Indian temple architecture and were never completed or consecrated. The complex also features sculptures of animals, including a life-size elephant. The area is well-maintained with manicured lawns, making it a pleasant place to explore.</p>
              </div>

              <div class="monument-item">
                <h4><i class="fas fa-water"></i> Arjuna's Penance</h4>
                <div class="monument-details">
                  <span class="monument-detail"><i class="fas fa-clock"></i> Visit Duration: 30-45 minutes</span>
                  <span class="monument-detail"><i class="fas fa-walking"></i> Difficulty: Easy</span>
                </div>
                <p>Also known as the Descent of the Ganges, this massive open-air bas-relief is carved on two huge adjacent boulders, spanning 27 meters in length and 9 meters in height. It depicts scenes from Hindu mythology, including the story of Arjuna's penance to obtain Lord Shiva's weapon. The relief features over 100 figures of gods, humans, and animals, including a family of elephants that is remarkably lifelike. Morning light provides the best conditions for viewing and photographing the intricate details.</p>
              </div>
            </div>
          </div>

          <div class="best-time">
            <h3>Best Time to Visit</h3>
            <p>The ideal time to visit Mahabalipuram is from November to February when the weather is pleasant with temperatures ranging from 20°C to 30°C. This period offers comfortable conditions for exploring the outdoor monuments. The early morning hours (6:00 AM - 9:00 AM) provide the best light for photography and fewer crowds. Sunset at the Shore Temple is particularly beautiful and should not be missed.</p>
            <p>If you're interested in cultural events, plan your visit during the Mamallapuram Dance Festival, usually held in December-January. This month-long festival features classical dance performances against the backdrop of illuminated monuments. Weekdays are generally less crowded than weekends, especially during the peak tourist season (December-January). If visiting during summer (March-June), plan your monument visits in the early morning or late afternoon to avoid the midday heat.</p>
          </div>
        </div>

        <!-- Amenities Panel -->
        <div class="travel-panel" id="amenitiesPanel">
          <div class="amenities-grid">
            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Radisson Blu Resort Temple Bay</strong></p>
              <p class="distance">1 km from Shore Temple</p>
              <p class="rating">★★★★★ (4.5/5)</p>
              <p>Luxury beachfront resort with private beach access.</p>
              <p>Price range: ₹12,000 - ₹25,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Grande Bay Resort and Spa</strong></p>
              <p class="distance">2 km from Shore Temple</p>
              <p class="rating">★★★★☆ (4.3/5)</p>
              <p>Upscale resort with pool and spa facilities.</p>
              <p>Price range: ₹8,000 - ₹15,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Mamalla Heritage</strong></p>
              <p class="distance">500m from Shore Temple</p>
              <p class="rating">★★★☆☆ (3.8/5)</p>
              <p>Budget-friendly hotel with traditional architecture.</p>
              <p>Price range: ₹2,000 - ₹4,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Moonrakers Restaurant</strong></p>
              <p class="distance">1 km from Shore Temple</p>
              <p class="rating">★★★★☆ (4.2/5)</p>
              <p>Seafood restaurant with beach views.</p>
              <p>Price range: ₹800 - ₹1,500 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>The Wharf</strong></p>
              <p class="distance">At Radisson Blu Resort</p>
              <p class="rating">★★★★★ (4.6/5)</p>
              <p>Fine dining restaurant with international cuisine.</p>
              <p>Price range: ₹2,000 - ₹3,500 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Golden Palate</strong></p>
              <p class="distance">Near bus stand</p>
              <p class="rating">★★★★☆ (4.0/5)</p>
              <p>Vegetarian restaurant serving South Indian cuisine.</p>
              <p>Price range: ₹500 - ₹800 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-shopping-bag"></i> Shopping</h3>
              <p><strong>Othavadai Street</strong></p>
              <p class="distance">1 km from Shore Temple</p>
              <p>Street lined with shops selling stone sculptures, handicrafts, and souvenirs.</p>
              <p>Must-buy: Miniature stone carvings and replicas of Mahabalipuram monuments.</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-first-aid"></i> Medical Facilities</h3>
              <p><strong>Government Hospital</strong></p>
              <p class="distance">In town center</p>
              <p>Basic medical facilities for minor emergencies.</p>
              <p>For serious medical issues, hospitals in Chennai (60 km) are recommended.</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-info-circle"></i> Tourist Information</h3>
              <p><strong>Tamil Nadu Tourism Office</strong></p>
              <p class="distance">Near Shore Temple</p>
              <p>Provides maps, brochures, and guided tour information.</p>
              <p>Contact: +91 44 2744 2232</p>
            </div>
          </div>

          <div class="best-time">
            <h3>Additional Services</h3>
            <p>Several ATMs are available in the town center, including SBI, ICICI, and HDFC. Most hotels and larger restaurants accept credit cards, but smaller shops and vendors prefer cash. Mobile network coverage is good throughout the town, with Airtel, Jio, and Vodafone providing reliable service. Free Wi-Fi is available in most hotels and some cafes.</p>
            <p>For those interested in learning about the local art forms, several stone carving workshops offer demonstrations and short courses (₹500-1,500). These can be arranged through hotels or the tourism office. Photography enthusiasts can join specialized photography tours that focus on capturing the monuments in the best light (₹1,500-3,000 per person). Yoga classes are also available on the beach, typically in the early morning hours (₹300-500 per session).</p>
          </div>
        </div>
      </div>
    </div>

    <div class="nearby-attractions">
      <h2>Nearby Attractions</h2>
      <div class="attractions-grid">
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/shore-temple_20190619141119.jpg" alt="Shore Temple">
          <div class="attraction-card-content">
            <h3>Shore Temple</h3>
            <p>An 8th-century temple complex overlooking the Bay of Bengal, featuring intricate carvings and shrines dedicated to Lord Shiva.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Pancha_Rathas_20190212182004.jpg" alt="Pancha Rathas">
          <div class="attraction-card-content">
            <h3>Pancha Rathas</h3>
            <p>Five monolithic temples carved in the shape of chariots, each dedicated to a Pandava brother from the Mahabharata.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Arjuna's_Penance_20190212182005.jpg" alt="Arjuna's Penance">
          <div class="attraction-card-content">
            <h3>Arjuna's Penance</h3>
            <p>A massive open-air bas-relief carved on two huge boulders, depicting scenes from Hindu mythology and everyday life.</p>
          </div>
        </div>
      </div>
    </div>
  </main>

  <footer>
    <p>&copy; 2025 Heritage Explorer | Explore Tamil Nadu</p>
  </footer>

  <div id="backToTop">
    <i class="fas fa-arrow-up"></i>
  </div>

  <script>
    // Back to Top Button
    const backToTop = document.getElementById("backToTop");
    window.addEventListener("scroll", () => {
      if (window.scrollY > 300) {
        backToTop.style.display = "flex";
      } else {
        backToTop.style.display = "none";
      }
    });
    backToTop.addEventListener("click", () => {
      window.scrollTo({ top: 0, behavior: "smooth" });
    });

    // Audio Playback
    document.getElementById("playAudioButton").addEventListener("click", () => {
      const description = document.querySelector(".site-info p").textContent;
      const utterance = new SpeechSynthesisUtterance(description);
      utterance.lang = "en-US";
      speechSynthesis.speak(utterance);
    });

    // Favorite Button
    document.getElementById("favoriteButton").addEventListener("click", function() {
      const icon = this.querySelector("i");
      if (icon.classList.contains("far")) {
        icon.classList.remove("far");
        icon.classList.add("fas");
        alert("Added to favorites!");
      } else {
        icon.classList.remove("fas");
        icon.classList.add("far");
        alert("Removed from favorites!");
      }
    });

    // Share Button
    document.getElementById("shareButton").addEventListener("click", () => {
      const shareData = {
        title: "Mahabalipuram - Heritage Explorer",
        text: "Check out this beautiful historical site in Tamil Nadu!",
        url: window.location.href
      };

      if (navigator.share) {
        navigator.share(shareData)
          .then(() => console.log("Shared successfully"))
          .catch((error) => console.error("Error sharing:", error));
      } else {
        navigator.clipboard.writeText(shareData.url)
          .then(() => alert("URL copied to clipboard"))
          .catch((err) => console.error("Could not copy text: ", err));
      }
    });

    // Initialize the travel info tabs
    document.addEventListener("DOMContentLoaded", function() {
      // Set the last updated date
      const today = new Date();
      document.getElementById("lastUpdated").textContent = today.toLocaleDateString();

      // Tab functionality
      const tabs = document.querySelectorAll(".travel-tab");
      tabs.forEach(tab => {
        tab.addEventListener("click", function() {
          // Remove active class from all tabs
          tabs.forEach(t => t.classList.remove("active"));

          // Add active class to clicked tab
          this.classList.add("active");

          // Hide all panels
          const panels = document.querySelectorAll(".travel-panel");
          panels.forEach(panel => panel.classList.remove("active"));

          // Show the corresponding panel
          const tabId = this.getAttribute("data-tab");
          document.getElementById(tabId + "Panel").classList.add("active");
        });
      });

      // Fetch weather data (mock)
      fetchWeatherData();
    });

    // Weather API Integration (Mock)
    function fetchWeatherData() {
      // Coordinates for Mahabalipuram
      const lat = 12.6269;
      const lon = 80.1928;

      // Mock weather data
      const mockWeatherData = {
        current: {
          temp: 31,
          humidity: 75,
          wind_speed: 12,
          weather: [{ main: "Clear", description: "Sunny", icon: "01d" }]
        },
        daily: [
          {
            dt: Date.now() + 86400000, // Tomorrow
            temp: { day: 30 },
            weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
          },
          {
            dt: Date.now() + 172800000, // Day after tomorrow
            temp: { day: 29 },
            weather: [{ main: "Clouds", description: "Cloudy", icon: "03d" }]
          },
          {
            dt: Date.now() + 259200000, // 3 days from now
            temp: { day: 28 },
            weather: [{ main: "Rain", description: "Light Rain", icon: "10d" }]
          }
        ]
      };

      updateWeatherUI(mockWeatherData);
    }

    function updateWeatherUI(data) {
      // Update current weather
      document.querySelector(".weather-temp").textContent = `${data.current.temp}°C`;
      document.querySelector(".weather-desc").textContent = data.current.weather[0].description;
      document.querySelector(".weather-extra").textContent = `Humidity: ${data.current.humidity}% | Wind: ${data.current.wind_speed} km/h`;

      // Update weather icon
      const weatherIconElement = document.querySelector(".weather-icon i");
      updateWeatherIcon(weatherIconElement, data.current.weather[0].main);

      // Update forecast
      const forecastDays = document.querySelectorAll(".forecast-day");
      data.daily.forEach((day, index) => {
        if (index < forecastDays.length) {
          const dayElement = forecastDays[index];
          const date = new Date(day.dt);
          const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });

          dayElement.querySelector("h4").textContent = index === 0 ? "Tomorrow" : dayName;
          dayElement.querySelector(".forecast-temp").textContent = `${day.temp.day}°C`;
          dayElement.querySelector(".forecast-desc").textContent = day.weather[0].description;

          // Update forecast icon
          const iconElement = dayElement.querySelector(".forecast-icon i");
          updateWeatherIcon(iconElement, day.weather[0].main);
        }
      });
    }

    function updateWeatherIcon(iconElement, weatherType) {
      // Remove all existing weather classes
      iconElement.className = "";

      // Add the appropriate weather icon class
      switch (weatherType) {
        case "Clear":
          iconElement.classList.add("fas", "fa-sun");
          break;
        case "Clouds":
          iconElement.classList.add("fas", "fa-cloud");
          break;
        case "Rain":
        case "Drizzle":
          iconElement.classList.add("fas", "fa-cloud-rain");
          break;
        case "Thunderstorm":
          iconElement.classList.add("fas", "fa-bolt");
          break;
        case "Snow":
          iconElement.classList.add("fas", "fa-snowflake");
          break;
        case "Mist":
        case "Fog":
        case "Haze":
          iconElement.classList.add("fas", "fa-smog");
          break;
        default:
          iconElement.classList.add("fas", "fa-cloud-sun");
      }
    }
  </script>

  <!-- Language Toggle Script -->
  <script src="language-toggle.js"></script>
</body>
</html>
