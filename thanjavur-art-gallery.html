<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Thanjavur Art Gallery - Heritage Explorer</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
  <style>
    :root {
      --primary-color: #1a237e;
      --secondary-color: #457b9d;
      --accent-color: #e63946;
      --light-color: #f1faee;
      --dark-color: #1d3557;
      --success-color: #2e7d32;
      --warning-color: #ff9800;
      --danger-color: #c62828;
      --gray-100: #f8f9fa;
      --gray-200: #e9ecef;
      --gray-300: #dee2e6;
      --gray-400: #ced4da;
      --gray-500: #adb5bd;
      --gray-600: #6c757d;
      --gray-700: #495057;
      --gray-800: #343a40;
      --gray-900: #212529;
      --border-radius: 0.5rem;
      --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
      --transition: all 0.3s ease-in-out;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: var(--gray-800);
      background-color: var(--gray-100);
    }

    header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      text-align: center;
    }

    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      background-color: white;
      box-shadow: var(--box-shadow);
    }

    nav a {
      color: var(--gray-700);
      text-decoration: none;
      margin: 0 1rem;
      font-weight: 500;
      transition: var(--transition);
    }

    nav a:hover {
      color: var(--primary-color);
    }

    main {
      max-width: 1200px;
      margin: 2rem auto;
      padding: 0 1rem;
    }

    .site-details {
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    @media (min-width: 768px) {
      .site-details {
        grid-template-columns: 1fr 1fr;
      }
    }

    .site-image {
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
    }

    .site-image img {
      width: 100%;
      height: auto;
      display: block;
    }

    .site-info h1 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .site-info p {
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }

    .site-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .meta-item {
      display: flex;
      align-items: center;
      background-color: var(--gray-200);
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
    }

    .meta-item i {
      margin-right: 0.5rem;
      color: var(--secondary-color);
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      margin-top: 1.5rem;
    }

    .action-buttons button {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      background-color: var(--secondary-color);
      color: white;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
      display: flex;
      align-items: center;
    }

    .action-buttons button i {
      margin-right: 0.5rem;
    }

    .action-buttons button:hover {
      background-color: var(--primary-color);
    }

    .site-description {
      margin-bottom: 2rem;
    }

    .site-description h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .map-container {
      height: 400px;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      margin-bottom: 2rem;
    }

    .nearby-attractions {
      margin-bottom: 2rem;
    }

    .nearby-attractions h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .attractions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .attraction-card {
      background-color: white;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
    }

    .attraction-card:hover {
      transform: translateY(-5px);
    }

    .attraction-card img {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }

    .attraction-card-content {
      padding: 1rem;
    }

    .attraction-card-content h3 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    footer {
      background-color: var(--primary-color);
      color: white;
      text-align: center;
      padding: 1rem;
      margin-top: 2rem;
    }

    #backToTop {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      width: 3rem;
      height: 3rem;
      background-color: var(--primary-color);
      color: white;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
      display: none;
    }

    #backToTop:hover {
      background-color: var(--secondary-color);
    }

    /* Travel Information Section */
    .travel-info {
      margin-bottom: 2rem;
      background-color: white;
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      overflow: hidden;
    }

    .travel-info-header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .travel-info-header h2 {
      margin: 0;
      font-size: 1.5rem;
    }

    .travel-info-tabs {
      display: flex;
      background-color: var(--gray-200);
    }

    .travel-tab {
      padding: 1rem;
      cursor: pointer;
      flex: 1;
      text-align: center;
      font-weight: 500;
      transition: var(--transition);
      border-bottom: 3px solid transparent;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .travel-tab i {
      margin-right: 0.5rem;
    }

    .travel-tab.active {
      background-color: white;
      border-bottom-color: var(--accent-color);
    }

    .travel-tab:hover:not(.active) {
      background-color: var(--gray-300);
    }

    .travel-content {
      padding: 1.5rem;
    }

    .travel-panel {
      display: none;
    }

    .travel-panel.active {
      display: block;
    }

    /* Weather Panel */
    .weather-info {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      align-items: center;
    }

    .weather-current {
      display: flex;
      align-items: center;
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      flex: 1;
      min-width: 250px;
    }

    .weather-icon {
      font-size: 3rem;
      margin-right: 1rem;
      color: var(--secondary-color);
    }

    .weather-details h3 {
      margin: 0;
      color: var(--primary-color);
    }

    .weather-temp {
      font-size: 2rem;
      font-weight: bold;
    }

    .weather-desc {
      color: var(--gray-700);
    }

    .weather-forecast {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      flex: 2;
      min-width: 300px;
    }

    .forecast-day {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      text-align: center;
      flex: 1;
      min-width: 100px;
    }

    .forecast-day h4 {
      margin: 0 0 0.5rem 0;
      color: var(--primary-color);
    }

    .forecast-icon {
      font-size: 1.5rem;
      margin: 0.5rem 0;
      color: var(--secondary-color);
    }

    /* Transportation Panel */
    .transport-options {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .transport-option {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
      display: flex;
      flex-direction: column;
    }

    .transport-option h3 {
      display: flex;
      align-items: center;
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .transport-option h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .transport-option p {
      margin-bottom: 0.5rem;
    }

    .transport-option .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    /* Hours & Tickets Panel */
    .hours-container {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .opening-hours, .ticket-info {
      flex: 1;
      min-width: 250px;
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .opening-hours h3, .ticket-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .opening-hours h3 i, .ticket-info h3 i {
      margin-right: 0.5rem;
    }

    .hours-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .hours-list li {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .hours-list li:last-child {
      border-bottom: none;
    }

    .day {
      font-weight: 500;
    }

    .ticket-type {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .ticket-type:last-child {
      border-bottom: none;
    }

    .ticket-price {
      font-weight: bold;
    }

    /* Amenities Panel */
    .amenities-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .amenity-card {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .amenity-card h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .amenity-card h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .amenity-card p {
      margin-bottom: 0.5rem;
    }

    .amenity-card .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    .amenity-card .rating {
      color: var(--warning-color);
      margin-bottom: 0.5rem;
    }

    .weather-tips h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .local-transport h3, .best-time h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .exhibition-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .exhibition-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .exhibition-list li {
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .exhibition-list li:last-child {
      border-bottom: none;
    }

    .exhibition-title {
      font-weight: 500;
      color: var(--gray-800);
    }

    .exhibition-date {
      font-style: italic;
      color: var(--gray-600);
      font-size: 0.9rem;
    }

    .exhibition-desc {
      margin-top: 0.25rem;
      font-size: 0.95rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>Heritage Explorer</h1>
    <p>Discover Tamil Nadu's rich heritage and culture</p>
  </header>

  <nav>
    <div>
      <a href="website.html"><i class="fas fa-home"></i> Home</a>
      <a href="#"><i class="fas fa-map-marked-alt"></i> Explore</a>
      <a href="#"><i class="fas fa-info-circle"></i> About</a>
    </div>
    <div>
      <a href="#" id="toggleLang">தமிழ்</a>
    </div>
  </nav>

  <main>
    <div class="site-details">
      <div class="site-image">
        <img src="https://c8.alamy.com/comp/C6CAD7/the-ornate-durbar-hall-of-tanjore-palace-in-thanjavur-tamil-nadu-india-C6CAD7.jpg" alt="Thanjavur Art Gallery">
      </div>
      <div class="site-info">
        <h1>Thanjavur Art Gallery</h1>
        <div class="site-meta">
          <div class="meta-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>Thanjavur, Tamil Nadu</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-university"></i>
            <span>Museum</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-calendar-alt"></i>
            <span>Established in 1951</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-palette"></i>
            <span>Chola Art Collection</span>
          </div>
        </div>
        <p>Thanjavur Art Gallery, located within the Thanjavur Palace complex, houses an exquisite collection of bronze and stone sculptures from the Chola period (9th-13th centuries). The gallery showcases masterpieces of South Indian art, including Thanjavur paintings with gold leaf work, ancient manuscripts, and artifacts that highlight the region's rich artistic heritage.</p>
        <div class="action-buttons">
          <button id="playAudioButton"><i class="fas fa-volume-up"></i> Listen</button>
          <button id="favoriteButton"><i class="far fa-heart"></i> Favorite</button>
          <button id="shareButton"><i class="fas fa-share-alt"></i> Share</button>
        </div>
      </div>
    </div>

    <div class="site-description">
      <h2>About Thanjavur Art Gallery</h2>
      <p>Thanjavur Art Gallery, also known as the Raja Raja Chola Art Gallery, is a prestigious museum located within the Thanjavur Palace complex in Tamil Nadu. Established in 1951, the gallery is housed in the Nayak Durbar Hall, a part of the palace that was built during the Nayak period in the 16th century. The gallery was created to preserve and showcase the rich artistic heritage of the Thanjavur region, particularly from the Chola period (9th-13th centuries), which is considered the golden age of Tamil art and culture.</p>
      <p>The gallery's most prized possessions are its collection of bronze sculptures from the Chola period, widely regarded as some of the finest examples of bronze casting in the world. These sculptures, primarily depicting Hindu deities, are known for their elegant proportions, graceful poses, and intricate details. The most famous among them is the bronze Nataraja (Dancing Shiva), which exemplifies the perfect balance, rhythm, and symbolism that characterize Chola bronzes. The gallery also houses an impressive collection of stone sculptures, showcasing the evolution of South Indian sculptural traditions from the early Chola period to later dynasties.</p>
      <p>Another highlight of the Thanjavur Art Gallery is its collection of Thanjavur paintings, a classical South Indian painting style that flourished in the Thanjavur region between the 16th and 19th centuries. These paintings are characterized by their use of vibrant colors, gold leaf overlays, and inlay work with glass beads and precious stones. The paintings typically depict Hindu gods, goddesses, and scenes from Hindu mythology, with a distinctive style that emphasizes the divine nature of the subjects through rich ornamentation and symbolic elements.</p>
      <p>Beyond sculptures and paintings, the gallery also preserves a variety of other artifacts that provide insights into the region's cultural history. These include ancient manuscripts, coins, weapons, musical instruments, and decorative arts. The gallery also houses a collection of palm leaf manuscripts, some of which contain rare texts on medicine, astrology, and literature. For visitors interested in the artistic and cultural heritage of South India, the Thanjavur Art Gallery offers a comprehensive overview of the region's artistic achievements, particularly during the Chola dynasty, which was known for its patronage of arts, architecture, and literature.</p>
    </div>

    <div class="map-container">
      <!-- Map will be embedded here -->
      <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3919.0123456789!2d79.13!3d10.785!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3baab89f0e592a7d%3A0x1d3c2b1c83e5dc08!2sThanjavur%20Art%20Gallery!5e0!3m2!1sen!2sin!4v1652345678901!5m2!1sen!2sin" width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy" title="Map of Thanjavur Art Gallery"></iframe>
    </div>

    <!-- Travel Information Section -->
    <div class="travel-info">
      <div class="travel-info-header">
        <h2>Practical Travel Information</h2>
        <div class="last-updated">Last updated: <span id="lastUpdated">Today</span></div>
      </div>

      <div class="travel-info-tabs">
        <div class="travel-tab active" data-tab="weather">
          <i class="fas fa-cloud-sun"></i> Weather
        </div>
        <div class="travel-tab" data-tab="transport">
          <i class="fas fa-bus"></i> Transportation
        </div>
        <div class="travel-tab" data-tab="hours">
          <i class="far fa-clock"></i> Hours & Exhibitions
        </div>
        <div class="travel-tab" data-tab="amenities">
          <i class="fas fa-concierge-bell"></i> Nearby Amenities
        </div>
      </div>

      <div class="travel-content">
        <!-- Weather Panel -->
        <div class="travel-panel active" id="weatherPanel">
          <div class="weather-info">
            <div class="weather-current">
              <div class="weather-icon">
                <i class="fas fa-sun"></i>
              </div>
              <div class="weather-details">
                <h3>Current Weather</h3>
                <div class="weather-temp">32°C</div>
                <div class="weather-desc">Sunny</div>
                <div class="weather-extra">Humidity: 75% | Wind: 8 km/h</div>
              </div>
            </div>

            <div class="weather-forecast">
              <div class="forecast-day">
                <h4>Tomorrow</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun"></i></div>
                <div class="forecast-temp">33°C</div>
                <div class="forecast-desc">Partly Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Wed</h4>
                <div class="forecast-icon"><i class="fas fa-cloud"></i></div>
                <div class="forecast-temp">31°C</div>
                <div class="forecast-desc">Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Thu</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun-rain"></i></div>
                <div class="forecast-temp">30°C</div>
                <div class="forecast-desc">Light Rain</div>
              </div>
            </div>
          </div>

          <div class="weather-tips">
            <h3>Weather Tips</h3>
            <p>Thanjavur has a tropical climate with temperatures ranging from 25°C to 38°C throughout the year. The summer months (March-June) can be particularly hot and humid, with temperatures often exceeding 35°C. The best time to visit is from October to March when the weather is relatively cooler and more comfortable for exploring the art gallery and other attractions in the city.</p>
            <p>The art gallery is located within the Thanjavur Palace complex and is fully air-conditioned, providing relief from the heat. However, you'll likely spend some time walking around the palace grounds, so it's advisable to visit during early morning (9:00 AM - 11:00 AM) or late afternoon (3:30 PM - 5:00 PM) to avoid the midday heat. Light cotton clothing, sunglasses, and a hat are recommended when exploring the outdoor areas. During the monsoon season (October-December), occasional showers can be expected, so carrying a small umbrella might be useful.</p>
          </div>
        </div>

        <!-- Transportation Panel -->
        <div class="travel-panel" id="transportPanel">
          <div class="transport-options">
            <div class="transport-option">
              <h3><i class="fas fa-plane"></i> By Air</h3>
              <p>Tiruchirappalli International Airport is the nearest airport.</p>
              <p class="distance">55 km from Thanjavur (approx. 1.5 hours by car)</p>
              <p>Regular flights from Chennai, Bangalore, and other major cities.</p>
              <p>International connections to Singapore, Dubai, and Kuala Lumpur.</p>
              <p>Taxi services available from the airport (₹1,200-1,500).</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-train"></i> By Train</h3>
              <p>Thanjavur Junction Railway Station is well-connected.</p>
              <p class="distance">3 km from the art gallery (10 minutes by auto-rickshaw)</p>
              <p>Regular trains from Chennai, Trichy, Madurai, and other major cities.</p>
              <p>Auto-rickshaws and taxis available outside the station.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-bus"></i> By Bus</h3>
              <p>Thanjavur has excellent bus connectivity.</p>
              <p>TNSTC and private operators run services to all major cities.</p>
              <p>New Bus Stand is 4 km from the art gallery.</p>
              <p>Old Bus Stand is just 1 km from the art gallery.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-car"></i> By Car</h3>
              <p>Well-connected by highways from major cities.</p>
              <p>From Chennai: NH-32 and NH-83 (350 km, approx. 6 hours)</p>
              <p>From Trichy: NH-83 (55 km, approx. 1.5 hours)</p>
              <p>From Madurai: NH-38 and NH-83 (170 km, approx. 3.5 hours)</p>
            </div>
          </div>

          <div class="local-transport">
            <h3>Local Transportation</h3>
            <p>Within Thanjavur, auto-rickshaws are the most convenient mode of transportation. They typically charge a fixed rate rather than by meter, so negotiate the fare before starting your journey. The standard fare from the railway station to the art gallery is around ₹50-70. The art gallery is located within the Thanjavur Palace complex, which is in the heart of the city. Many other attractions like the Brihadeeswarar Temple are within walking distance (1 km). For a more immersive experience, consider taking a guided walking tour of the historic areas around the palace complex. Bicycle rentals are also available near the palace for exploring the city at a leisurely pace (₹100-150 per day).</p>
          </div>
        </div>

        <!-- Hours & Exhibitions Panel -->
        <div class="travel-panel" id="hoursPanel">
          <div class="hours-container">
            <div class="opening-hours">
              <h3><i class="far fa-clock"></i> Opening Hours</h3>
              <ul class="hours-list">
                <li><span class="day">Tuesday to Sunday:</span> <span class="time">9:00 AM - 5:00 PM</span></li>
                <li><span class="day">Monday:</span> <span class="time">Closed</span></li>
                <li><span class="day">Last Entry:</span> <span class="time">4:30 PM</span></li>
                <li><span class="day">Guided Tours:</span> <span class="time">10:00 AM, 12:00 PM, 2:00 PM</span></li>
                <li><span class="day">Audio Guide:</span> <span class="time">Available in English, Tamil, Hindi</span></li>
              </ul>
              <p class="hours-note">The art gallery remains open on most public holidays except for certain national holidays. It's advisable to check the official website or call ahead if you're planning to visit on a holiday. Photography is allowed in most areas of the gallery, but flash photography is strictly prohibited to protect the delicate artworks. Special photography permits for professional equipment can be obtained at the entrance for an additional fee.</p>
            </div>

            <div class="ticket-info">
              <h3><i class="fas fa-ticket-alt"></i> Ticket Information</h3>
              <div class="ticket-type">
                <span>Indian Nationals:</span>
                <span class="ticket-price">₹20 per person</span>
              </div>
              <div class="ticket-type">
                <span>Foreign Nationals:</span>
                <span class="ticket-price">₹200 per person</span>
              </div>
              <div class="ticket-type">
                <span>Students (with ID):</span>
                <span class="ticket-price">₹10</span>
              </div>
              <div class="ticket-type">
                <span>Guided Tour:</span>
                <span class="ticket-price">₹100 additional</span>
              </div>
              <div class="ticket-type">
                <span>Audio Guide:</span>
                <span class="ticket-price">₹75</span>
              </div>
              <p class="ticket-note">Tickets can be purchased at the entrance. Only cash payment is accepted. The guided tours are highly recommended as they provide valuable insights into the history, artistic techniques, and cultural significance of the artworks. Group discounts are available for parties of 10 or more people. A combined ticket for the Art Gallery, Palace, and Saraswathi Mahal Library is available for ₹50 for Indian nationals and ₹500 for foreign nationals.</p>
            </div>
          </div>

          <div class="exhibition-info">
            <h3>Current & Upcoming Exhibitions</h3>
            <ul class="exhibition-list">
              <li>
                <div class="exhibition-title">Masterpieces of Chola Bronze Art</div>
                <div class="exhibition-date">Permanent Exhibition</div>
                <div class="exhibition-desc">A comprehensive display of bronze sculptures from the Chola period (9th-13th centuries), including the famous Nataraja (Dancing Shiva) figures.</div>
              </li>
              <li>
                <div class="exhibition-title">Evolution of Thanjavur Paintings</div>
                <div class="exhibition-date">Permanent Exhibition</div>
                <div class="exhibition-desc">A collection showcasing the development of the distinctive Thanjavur painting style from the 16th to 19th centuries.</div>
              </li>
              <li>
                <div class="exhibition-title">Contemporary Interpretations of Classical Art</div>
                <div class="exhibition-date">January 15 - March 30, 2025</div>
                <div class="exhibition-desc">A special exhibition featuring works by modern artists inspired by classical Thanjavur and Chola artistic traditions.</div>
              </li>
              <li>
                <div class="exhibition-title">Manuscripts and Literary Heritage</div>
                <div class="exhibition-date">April 15 - June 30, 2025</div>
                <div class="exhibition-desc">An upcoming exhibition showcasing rare palm leaf manuscripts and ancient texts from the region.</div>
              </li>
            </ul>
            <p>The art gallery regularly organizes special exhibitions, workshops, and cultural events. Check the official website or inquire at the information desk for the latest schedule during your visit.</p>
          </div>
        </div>

        <!-- Amenities Panel -->
        <div class="travel-panel" id="amenitiesPanel">
          <div class="amenities-grid">
            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Svatma</strong></p>
              <p class="distance">2 km from art gallery</p>
              <p class="rating">★★★★★ (4.7/5)</p>
              <p>Luxury heritage hotel with traditional architecture and modern amenities.</p>
              <p>Price range: ₹8,000 - ₹15,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Ideal River View Resort</strong></p>
              <p class="distance">5 km from art gallery</p>
              <p class="rating">★★★★☆ (4.3/5)</p>
              <p>Riverside resort with cottages and beautiful garden views.</p>
              <p>Price range: ₹4,000 - ₹7,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Hotel Gnanam</strong></p>
              <p class="distance">1.5 km from art gallery</p>
              <p class="rating">★★★☆☆ (3.8/5)</p>
              <p>Budget-friendly hotel with clean rooms and good location.</p>
              <p>Price range: ₹1,500 - ₹3,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Bommu Virundhu</strong></p>
              <p class="distance">1 km from art gallery</p>
              <p class="rating">★★★★★ (4.6/5)</p>
              <p>Traditional Thanjavur cuisine served on banana leaves.</p>
              <p>Price range: ₹300 - ₹500 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Svatma Restaurant</strong></p>
              <p class="distance">2 km from art gallery</p>
              <p class="rating">★★★★☆ (4.5/5)</p>
              <p>Fine dining restaurant serving authentic Tamil cuisine.</p>
              <p>Price range: ₹1,000 - ₹1,500 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-shopping-bag"></i> Shopping</h3>
              <p><strong>Thanjavur Art Gallery Market</strong></p>
              <p class="distance">At the gallery exit</p>
              <p>Shops selling replicas of Thanjavur paintings, bronze statues, and handicrafts.</p>
              <p>Also offers art supplies and books on South Indian art history.</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-first-aid"></i> Medical Facilities</h3>
              <p><strong>Thanjavur Medical College Hospital</strong></p>
              <p class="distance">3 km from art gallery</p>
              <p>Government hospital with 24/7 emergency services.</p>
              <p>Contact: +91 4362 240 851</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-info-circle"></i> Tourist Information</h3>
              <p><strong>Tamil Nadu Tourism Office</strong></p>
              <p class="distance">1 km from art gallery</p>
              <p>Provides maps, brochures, and guided tour information.</p>
              <p>Contact: +91 4362 230 984</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="nearby-attractions">
      <h2>Nearby Attractions</h2>
      <div class="attractions-grid">
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Brihadeeswarar_Temple_20190212182003.jpg" alt="Brihadeeswarar Temple">
          <div class="attraction-card-content">
            <h3>Brihadeeswarar Temple</h3>
            <p>A UNESCO World Heritage Site built by Raja Raja Chola I in the 11th century, featuring a 216-foot tall vimana and exquisite Chola architecture.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Saraswathi_Mahal_Library_20190212182004.jpg" alt="Saraswathi Mahal Library">
          <div class="attraction-card-content">
            <h3>Saraswathi Mahal Library</h3>
            <p>One of the oldest libraries in Asia, housing over 30,000 rare manuscripts and books in various languages including Tamil, Sanskrit, and Marathi.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Thanjavur_Palace_20190212182005.jpg" alt="Thanjavur Palace">
          <div class="attraction-card-content">
            <h3>Thanjavur Palace</h3>
            <p>A royal palace complex built by the Nayak rulers and later renovated by the Marathas, featuring the Durbar Hall, royal quarters, and watchtowers.</p>
          </div>
        </div>
      </div>
    </div>
  </main>

  <footer>
    <p>&copy; 2025 Heritage Explorer | Explore Tamil Nadu</p>
  </footer>

  <div id="backToTop">
    <i class="fas fa-arrow-up"></i>
  </div>

  <script>
    // Back to Top Button
    const backToTop = document.getElementById("backToTop");
    window.addEventListener("scroll", () => {
      if (window.scrollY > 300) {
        backToTop.style.display = "flex";
      } else {
        backToTop.style.display = "none";
      }
    });
    backToTop.addEventListener("click", () => {
      window.scrollTo({ top: 0, behavior: "smooth" });
    });

    // Audio Playback
    document.getElementById("playAudioButton").addEventListener("click", () => {
      const description = document.querySelector(".site-info p").textContent;
      const utterance = new SpeechSynthesisUtterance(description);
      utterance.lang = "en-US";
      speechSynthesis.speak(utterance);
    });

    // Favorite Button
    document.getElementById("favoriteButton").addEventListener("click", function() {
      const icon = this.querySelector("i");
      if (icon.classList.contains("far")) {
        icon.classList.remove("far");
        icon.classList.add("fas");
        alert("Added to favorites!");
      } else {
        icon.classList.remove("fas");
        icon.classList.add("far");
        alert("Removed from favorites!");
      }
    });

    // Share Button
    document.getElementById("shareButton").addEventListener("click", () => {
      const shareData = {
        title: "Thanjavur Art Gallery - Heritage Explorer",
        text: "Check out this magnificent art gallery in Tamil Nadu!",
        url: window.location.href
      };

      if (navigator.share) {
        navigator.share(shareData)
          .then(() => console.log("Shared successfully"))
          .catch((error) => console.error("Error sharing:", error));
      } else {
        navigator.clipboard.writeText(shareData.url)
          .then(() => alert("URL copied to clipboard"))
          .catch((err) => console.error("Could not copy text: ", err));
      }
    });

    // Initialize the travel info tabs
    document.addEventListener("DOMContentLoaded", function() {
      // Set the last updated date
      const today = new Date();
      document.getElementById("lastUpdated").textContent = today.toLocaleDateString();

      // Tab functionality
      const tabs = document.querySelectorAll(".travel-tab");
      tabs.forEach(tab => {
        tab.addEventListener("click", function() {
          // Remove active class from all tabs
          tabs.forEach(t => t.classList.remove("active"));

          // Add active class to clicked tab
          this.classList.add("active");

          // Hide all panels
          const panels = document.querySelectorAll(".travel-panel");
          panels.forEach(panel => panel.classList.remove("active"));

          // Show the corresponding panel
          const tabId = this.getAttribute("data-tab");
          document.getElementById(tabId + "Panel").classList.add("active");
        });
      });

      // Fetch weather data (mock)
      fetchWeatherData();
    });

    // Weather API Integration (Mock)
    function fetchWeatherData() {
      // Coordinates for Thanjavur
      const lat = 10.7850;
      const lon = 79.1300;

      // Mock weather data
      const mockWeatherData = {
        current: {
          temp: 32,
          humidity: 75,
          wind_speed: 8,
          weather: [{ main: "Clear", description: "Sunny", icon: "01d" }]
        },
        daily: [
          {
            dt: Date.now() + 86400000, // Tomorrow
            temp: { day: 33 },
            weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
          },
          {
            dt: Date.now() + 172800000, // Day after tomorrow
            temp: { day: 31 },
            weather: [{ main: "Clouds", description: "Cloudy", icon: "03d" }]
          },
          {
            dt: Date.now() + 259200000, // 3 days from now
            temp: { day: 30 },
            weather: [{ main: "Rain", description: "Light Rain", icon: "10d" }]
          }
        ]
      };

      updateWeatherUI(mockWeatherData);
    }

    function updateWeatherUI(data) {
      // Update current weather
      document.querySelector(".weather-temp").textContent = `${data.current.temp}°C`;
      document.querySelector(".weather-desc").textContent = data.current.weather[0].description;
      document.querySelector(".weather-extra").textContent = `Humidity: ${data.current.humidity}% | Wind: ${data.current.wind_speed} km/h`;

      // Update weather icon
      const weatherIconElement = document.querySelector(".weather-icon i");
      updateWeatherIcon(weatherIconElement, data.current.weather[0].main);

      // Update forecast
      const forecastDays = document.querySelectorAll(".forecast-day");
      data.daily.forEach((day, index) => {
        if (index < forecastDays.length) {
          const dayElement = forecastDays[index];
          const date = new Date(day.dt);
          const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });

          dayElement.querySelector("h4").textContent = index === 0 ? "Tomorrow" : dayName;
          dayElement.querySelector(".forecast-temp").textContent = `${day.temp.day}°C`;
          dayElement.querySelector(".forecast-desc").textContent = day.weather[0].description;

          // Update forecast icon
          const iconElement = dayElement.querySelector(".forecast-icon i");
          updateWeatherIcon(iconElement, day.weather[0].main);
        }
      });
    }

    function updateWeatherIcon(iconElement, weatherType) {
      // Remove all existing weather classes
      iconElement.className = "";

      // Add the appropriate weather icon class
      switch (weatherType) {
        case "Clear":
          iconElement.classList.add("fas", "fa-sun");
          break;
        case "Clouds":
          iconElement.classList.add("fas", "fa-cloud");
          break;
        case "Rain":
        case "Drizzle":
          iconElement.classList.add("fas", "fa-cloud-rain");
          break;
        case "Thunderstorm":
          iconElement.classList.add("fas", "fa-bolt");
          break;
        case "Snow":
          iconElement.classList.add("fas", "fa-snowflake");
          break;
        case "Mist":
        case "Fog":
        case "Haze":
          iconElement.classList.add("fas", "fa-smog");
          break;
        default:
          iconElement.classList.add("fas", "fa-cloud-sun");
      }
    }
  </script>

  <!-- Language Toggle Script -->
  <script src="language-toggle.js"></script>
</body>
</html>
