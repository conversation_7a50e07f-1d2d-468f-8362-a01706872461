/**
 * Heritage Chatbot - Enhanced functionality for Tamil Nadu cultural heritage information
 * This module provides detailed responses about Tamil Nadu's cultural heritage sites
 */

// Define question types for better response targeting
const questionTypes = {
  "what": ["what is", "what's", "tell me about", "describe", "information about", "details about", "facts about"],
  "where": ["where is", "where's", "location of", "situated", "located"],
  "when": ["when was", "when is", "history of", "built", "constructed", "established"],
  "who": ["who built", "who constructed", "who designed", "who created", "built by", "designed by"],
  "how": ["how was", "how is", "how to", "how can", "how do"],
  "why": ["why is", "why was", "why do", "reason for", "significance of", "importance of"]
};

// Define site keywords for better site identification
const siteKeywords = {
  "brihadeeswarar": ["big temple", "thanjavur temple", "raja raja chola", "brihadeeswara"],
  "meenakshi": ["madurai temple", "meenakshi amman"],
  "shore temple": ["mahabalipuram", "mamallapuram", "sea shore temple"],
  "ram<PERSON><PERSON><PERSON><PERSON>": ["rameswaram temple", "ramanathaswamy"],
  "gangaikonda cholapuram": ["jayankondam temple", "gangaikonda", "cholapuram"],
  "airavatesvara": ["darasuram temple", "airavatesvara", "darasuram"],
  "chettinad": ["karaikudi", "chettinad mansions", "chettiar mansions"],
  "palaces": ["thanjavur palace", "thirumalai nayakkar palace", "padmanabhapuram palace", "chettinad mansions"],
  "monuments": ["vellore fort", "rock fort", "gingee fort", "danish fort", "arjuna's penance"],
  "museums": ["government museum chennai", "thanjavur art gallery", "madras museum"],
  "churches": ["santhome basilica", "st. mary's church", "velankanni church", "st. thomas mount church", "christ the king church", "st. francis xavier church"],
  "santhome basilica": ["san thome cathedral", "st thomas tomb", "santhome cathedral"],
  "st. mary's church": ["st marys", "fort st george church", "oldest anglican church"],
  "velankanni church": ["our lady of good health", "lourdes of the east", "marian shrine"],
  "st. thomas mount church": ["bleeding cross", "st thomas martyrdom", "church of our lady of expectation"],
  "christ the king church": ["pudukkottai church", "christ king", "catholic cathedral pudukkottai"],
  "st. francis xavier church": ["kottar church", "kanyakumari church", "xavier cathedral"]
};

// Define general heritage keywords
const generalHeritageKeywords = [
  "heritage", "culture", "history", "architecture", "temple", "monument",
  "unesco", "world heritage", "historical", "ancient", "traditional", "church", "cathedral", "basilica"
];

// Heritage sites database with detailed information
// Make it accessible globally so it can be used by other scripts
window.heritageSiteInfo = {
  "brihadeeswarar temple": {
    name: "Brihadeeswarar Temple",
    location: "Thanjavur",
    period: "Chola dynasty (11th century)",
    description: "The Brihadeeswarar Temple, also known as the 'Big Temple,' is a UNESCO World Heritage Site built by Raja Raja Chola I. It features a 216-foot tall vimana (tower) and is one of the largest temples in India. The temple is dedicated to Lord Shiva and is renowned for its architectural grandeur and intricate sculptures.",
    significance: "It represents the pinnacle of Chola architecture and is famous for its massive Nandi statue carved from a single stone. The temple's dome is made from a single granite block weighing approximately 80 tons.",
    visitingInfo: "Open daily from 6:00 AM to 8:30 PM. The best time to visit is during the morning or evening to avoid the heat.",
    facts: [
      "The shadow of the temple's tower never falls on the ground at noon.",
      "The temple was built in just 7 years, which was a remarkable achievement for that time.",
      "The temple has been functioning continuously for over 1000 years.",
      "It was the tallest temple in India when it was built."
    ]
  },
  "meenakshi amman temple": {
    name: "Meenakshi Amman Temple",
    location: "Madurai",
    period: "Nayak dynasty (16th-17th century)",
    description: "The Meenakshi Amman Temple is a historic Hindu temple dedicated to Goddess Meenakshi (a form of Parvati) and Lord Sundareswarar (a form of Shiva). The temple is known for its towering gopurams (gateway towers) adorned with thousands of colorful sculptures.",
    significance: "It is one of the most important temples for Tamil Hindus and represents the Dravidian architectural style at its finest. The temple complex covers 14 acres and has 14 gateway towers."
  },
  "gangaikonda cholapuram": {
    name: "Gangaikonda Cholapuram Temple",
    location: "Jayankondam",
    period: "Chola dynasty (11th century)",
    description: "Gangaikonda Cholapuram Temple was built by Rajendra Chola I, the son of Raja Raja Chola I, to commemorate his victories. It is part of the UNESCO World Heritage Site 'Great Living Chola Temples' and features exquisite stone carvings and sculptures.",
    significance: "The temple represents the architectural and artistic excellence of the Chola dynasty. It was the capital of the Chola Empire for about 250 years."
  },
  "airavatesvara temple": {
    name: "Airavatesvara Temple",
    location: "Darasuram",
    period: "Chola dynasty (12th century)",
    description: "Airavatesvara Temple is a UNESCO World Heritage Site built by Chola king Rajaraja II in the 12th century. It is known for its intricate stone carvings and unique architectural features including a stone chariot.",
    significance: "The temple is famous for its elaborate carvings and sculptures that depict various scenes from Hindu mythology. It is smaller than the other Great Living Chola Temples but equally magnificent in its craftsmanship."
  },
  "chettinad mansions": {
    name: "Chettinad Mansions",
    location: "Karaikudi",
    period: "19th-20th century",
    description: "Chettinad Mansions are opulent palatial homes built by the wealthy Chettiar merchant community. These mansions feature a unique blend of European, East Asian, and traditional Tamil architectural styles.",
    significance: "These mansions showcase the wealth and global connections of the Chettiar community. They are known for their imported materials including Italian marble, Burmese teak, and European chandeliers."
  },
  "thanjavur palace": {
    name: "Thanjavur Palace",
    location: "Thanjavur",
    period: "Nayak and Maratha period (16th-19th century)",
    description: "Thanjavur Palace, also known as Maratha Palace, was built by the Nayak rulers and later expanded by the Marathas. It houses the Saraswathi Mahal Library, one of the oldest libraries in Asia with a rare collection of manuscripts.",
    significance: "The palace complex is a blend of various architectural styles including Dravidian, Rajput, and Maratha. It is an important historical monument that showcases the cultural heritage of Tamil Nadu."
  },
  "thirumalai nayakkar palace": {
    name: "Thirumalai Nayakkar Palace",
    location: "Madurai",
    period: "Nayak dynasty (17th century)",
    description: "Thirumalai Nayakkar Palace was built in 1636 by King Thirumalai Nayak. It is a classic example of Indo-Saracenic architecture with massive pillars and ornate arches.",
    significance: "The palace is known for its grand Darbar Hall (audience chamber) with massive pillars. The architectural style shows influences from both Dravidian and Islamic traditions."
  },
  "vellore fort": {
    name: "Vellore Fort",
    location: "Vellore",
    period: "Vijayanagara Empire (16th century)",
    description: "Vellore Fort is a large 16th-century fort with a moat and robust ramparts. It houses the Jalakanteswarar Temple, a church, and a mosque within its walls, showcasing religious harmony.",
    significance: "The fort is historically significant as the site of the Vellore Mutiny of 1806, considered the first instance of a military uprising against British rule in India."
  },
  "government museum chennai": {
    name: "Government Museum Chennai",
    location: "Chennai",
    period: "British colonial period (1851)",
    description: "The Government Museum Chennai is one of the oldest museums in India, established in 1851. It houses rich collections of archaeology, numismatics, zoology, natural history, and Chola bronze sculptures.",
    significance: "The museum's bronze gallery is particularly famous for its collection of exquisite Chola bronze sculptures, considered among the finest in the world."
  },

  "santhome basilica": {
    name: "Santhome Basilica",
    location: "Chennai",
    period: "Late 19th century (1893-1896)",
    description: "Santhome Basilica, officially known as San Thome Cathedral Basilica, is a historic Roman Catholic minor basilica built over the tomb of St. Thomas, one of the twelve apostles of Jesus Christ. The Neo-Gothic style church features a stunning white façade and soaring spires reaching 155 feet.",
    significance: "It is one of only three churches in the world built over the tomb of an apostle of Jesus Christ—the others being St. Peter's Basilica in Vatican City and Santiago de Compostela Cathedral in Spain.",
    visitingInfo: "Open daily from 6:00 AM to 8:00 PM. Mass is conducted in English and Tamil at various times throughout the day.",
    facts: [
      "The original church was built by Portuguese explorers in the 16th century over the tomb of St. Thomas.",
      "The present Neo-Gothic structure was rebuilt by the British in 1893.",
      "The church houses a museum with artifacts related to St. Thomas and early Christianity in India.",
      "The underground tomb chapel contains relics of St. Thomas."
    ]
  },

  "st. mary's church": {
    name: "St. Mary's Church",
    location: "Chennai",
    period: "Late 17th century (1678-1680)",
    description: "St. Mary's Church, located within the historic Fort St. George complex, is the oldest Anglican church in India and the oldest British building in Chennai. The church features a simple yet elegant English Baroque style with a pristine white exterior.",
    significance: "Often referred to as the 'Westminster Abbey of the East', the church houses several valuable artifacts including the Bible and prayer book used by Robert Clive and ancient tombstones with fascinating epitaphs.",
    visitingInfo: "Open from Monday to Saturday, 10:00 AM to 5:00 PM. Sunday services are held at 8:30 AM.",
    facts: [
      "The church's bell tower houses a clock that has been keeping time for over three centuries.",
      "The baptismal register contains records of notable figures like Elihu Yale, after whom Yale University is named.",
      "The church survived several wars and natural disasters, including the French siege of Madras in 1746.",
      "The interior features tall teakwood columns and a beautifully ornate altar piece."
    ]
  },

  "velankanni church": {
    name: "Velankanni Church",
    location: "Nagapattinam",
    period: "Late 16th to early 20th century",
    description: "The Basilica of Our Lady of Good Health, commonly known as Velankanni Church, is one of the most revered Catholic pilgrimage sites in India. Built in Gothic architectural style with Portuguese and Indian influences, it features a stunning white and cream façade with two towering spires.",
    significance: "Known as the 'Lourdes of the East', the basilica attracts millions of pilgrims annually from various faiths. Its history is steeped in miraculous tales dating back to the 16th century, including apparitions of Mary and the miraculous healing of a lame buttermilk vendor.",
    visitingInfo: "Open daily from 5:00 AM to 9:00 PM. The annual feast of the Nativity of Mary in September is a major celebration.",
    facts: [
      "Despite being a Catholic shrine, it attracts devotees from various faiths, including Hindus and Muslims.",
      "The central shrine houses the statue of Our Lady of Good Health, adorned in a traditional Indian sari.",
      "The basilica complex includes a museum, meditation centers, and the Morning Star Church.",
      "The church was elevated to the status of a Minor Basilica in 1962 by Pope John XXIII."
    ]
  },

  "st. thomas mount church": {
    name: "St. Thomas Mount Church",
    location: "Chennai",
    period: "16th century (1523)",
    description: "St. Thomas Mount Church, officially known as the Church of Our Lady of Expectation, is a historic Catholic church perched atop a small hillock. This sacred site is believed to be the place where St. Thomas, one of the twelve apostles of Jesus Christ, was martyred in 72 AD.",
    significance: "The church's most treasured possession is an ancient 'Bleeding Cross,' carved by St. Thomas himself, which is said to have miraculously bled periodically throughout history, with the last recorded instance in 1704.",
    visitingInfo: "Open daily from 6:00 AM to 8:00 PM. The feast of St. Thomas is celebrated on July 3rd.",
    facts: [
      "The church was built by Portuguese explorers in 1523.",
      "It features a blend of Portuguese, Gothic, and Indian architectural elements.",
      "The church houses a beautiful altar painting of the Madonna, believed to have been created by St. Luke the Evangelist.",
      "The 134 stone steps leading to the summit are lined with the Stations of the Cross."
    ]
  },

  "christ the king church": {
    name: "Christ the King Church",
    location: "Pudukkottai",
    period: "Early 20th century",
    description: "Christ the King Church in Pudukkottai is a magnificent Catholic church that stands as a testament to the harmonious blend of Gothic and Indian architectural styles. The church serves as the cathedral church of the Diocese of Pudukkottai.",
    significance: "The church's architecture is particularly noteworthy for its soaring spires, pointed arches, and ribbed vaults characteristic of Gothic design, while incorporating elements that reflect local Tamil architectural traditions.",
    visitingInfo: "Open daily from 6:00 AM to 8:00 PM. The Feast of Christ the King in November is the most significant celebration.",
    facts: [
      "The façade features intricate carvings and a large rose window that bathes the interior in colorful light.",
      "The church grounds include a grotto dedicated to Our Lady of Lourdes and well-maintained gardens.",
      "The interior has a high vaulted ceiling, ornate altar, and beautiful statues and paintings depicting scenes from the life of Christ.",
      "It serves as both a religious center and a cultural landmark in Pudukkottai."
    ]
  },

  "st. francis xavier church": {
    name: "St. Francis Xavier Church",
    location: "Kottar, Kanyakumari",
    period: "16th century",
    description: "St. Francis Xavier Church, located in Kottar, is one of the oldest and most historically significant churches in Tamil Nadu. Built in the 16th century by Portuguese missionaries, this magnificent church is dedicated to St. Francis Xavier, the renowned Jesuit missionary.",
    significance: "The church stands as a testament to the early Christian presence in the southernmost tip of India and the cultural exchange between European missionaries and local communities.",
    visitingInfo: "Open daily from 6:00 AM to 8:00 PM. The annual feast of St. Francis Xavier in December attracts thousands of pilgrims.",
    facts: [
      "The church's architecture represents a beautiful blend of Portuguese, Gothic, and local Indian styles.",
      "Its impressive façade features a central bell tower, ornate arches, and detailed stonework.",
      "The interior has a high vaulted ceiling, elegant columns, and a stunning altar adorned with intricate carvings.",
      "The church houses several valuable religious artifacts, including a statue of St. Francis Xavier that is believed to have miraculous powers."
    ]
  }
};

/**
 * Find the best match for a word in a list of strings using fuzzy matching
 * @param {string} word - The word to match
 * @param {Array<string>} list - The list of strings to match against
 * @return {string|null} - The best match or null if no match found
 */
function findBestMatch(word, list) {
  // Simple fuzzy matching - check if the word is contained in any of the strings
  for (const item of list) {
    if (item.includes(word) || word.includes(item)) {
      return item;
    }
  }

  // Check for common misspellings or partial matches
  for (const item of list) {
    // Split both strings into characters
    const wordChars = word.split('');
    const itemChars = item.split('');

    // Count matching characters
    let matchCount = 0;
    for (const char of wordChars) {
      if (itemChars.includes(char)) {
        matchCount++;
      }
    }

    // If more than 70% of characters match, consider it a match
    if (matchCount / wordChars.length > 0.7) {
      return item;
    }
  }

  return null;
}

/**
 * Extract city name from a message using fuzzy matching
 * @param {string} message - The user's message
 * @return {string|null} - The extracted city name or null
 */
function extractCityWithFuzzyMatch(message) {
  const tamilNaduCities = [
    "chennai", "madurai", "coimbatore", "trichy", "salem", "tirunelveli",
    "vellore", "thanjavur", "tanjore", "kanyakumari", "ooty", "nilgiris",
    "kodaikanal", "rameswaram", "mahabalipuram", "kanchipuram", "dindigul",
    "tenkasi", "dharmapuri"
  ];

  // First check for direct matches
  for (const city of tamilNaduCities) {
    if (message.includes(city)) {
      return city;
    }
  }

  // Then try fuzzy matching on individual words
  const words = message.split(/\s+/);
  for (const word of words) {
    if (word.length < 3) continue;

    for (const city of tamilNaduCities) {
      // Check if the word is similar to the city name
      if (city.includes(word) || word.includes(city)) {
        return city;
      }

      // Check for common misspellings
      const wordChars = word.split('');
      const cityChars = city.split('');

      let matchCount = 0;
      for (const char of wordChars) {
        if (cityChars.includes(char)) {
          matchCount++;
        }
      }

      if (matchCount / wordChars.length > 0.7) {
        return city;
      }
    }
  }

  return null;
}

/**
 * Get a response from the heritage chatbot
 * @param {string} userMessage - The message from the user
 * @param {string} language - The language code (e.g., 'en')
 * @param {Array} conversationHistory - The conversation history
 * @return {string} - The chatbot's response
 */
function getHeritageResponse(userMessage, language, conversationHistory) {
  const lowerCaseMessage = userMessage.toLowerCase().trim();

  // Check for church-related queries with more flexible matching
  const churchQueries = [
    { name: "velankanni church", keywords: ["velankanni", "velanganni", "lady of good health"] },
    { name: "santhome basilica", keywords: ["santhome", "san thome", "st thomas basilica", "st. thomas basilica"] },
    { name: "st. mary's church", keywords: ["st. mary", "st mary", "st. marys", "st marys", "fort st george"] },
    { name: "st. thomas mount church", keywords: ["st. thomas mount", "st thomas mount", "thomas mount"] },
    { name: "christ the king church", keywords: ["christ the king", "christ king", "pudukkottai church"] },
    { name: "st. francis xavier church", keywords: ["st. francis", "st francis", "xavier", "kottar church"] }
  ];

  // Check for natural language queries about churches

  for (const church of churchQueries) {
    // Check if any of the church keywords are in the message
    if (church.keywords.some(keyword => lowerCaseMessage.includes(keyword))) {
      // Format a nice response with the church information
      const churchInfo = window.heritageSiteInfo[church.name];

      // Check for specific question types
      if (lowerCaseMessage.includes("where") || lowerCaseMessage.includes("located")) {
        return `${churchInfo.name} is located in ${churchInfo.location}, Tamil Nadu.`;
      } else if (lowerCaseMessage.includes("when") || lowerCaseMessage.includes("built") || lowerCaseMessage.includes("history")) {
        return `${churchInfo.name} was built during the ${churchInfo.period}. ${churchInfo.description}`;
      } else if (lowerCaseMessage.includes("significance") || lowerCaseMessage.includes("importance") || lowerCaseMessage.includes("why")) {
        return `${churchInfo.name} is significant because ${churchInfo.significance}`;
      } else {
        return `${churchInfo.name} (${churchInfo.location}): ${churchInfo.description}`;
      }
    }
  }

  // Direct check for exact church names
  if (lowerCaseMessage === "velankanni church") {
    return window.heritageSiteInfo["velankanni church"].description;
  }
  if (lowerCaseMessage === "santhome basilica") {
    return window.heritageSiteInfo["santhome basilica"].description;
  }
  if (lowerCaseMessage === "st. mary's church") {
    return window.heritageSiteInfo["st. mary's church"].description;
  }
  if (lowerCaseMessage === "st. thomas mount church") {
    return window.heritageSiteInfo["st. thomas mount church"].description;
  }
  if (lowerCaseMessage === "christ the king church") {
    return window.heritageSiteInfo["christ the king church"].description;
  }
  if (lowerCaseMessage === "st. francis xavier church") {
    return window.heritageSiteInfo["st. francis xavier church"].description;
  }

  // SPECIFIC HILL LOCATIONS - Check these first to prevent matching with generic "hills" category
  if (lowerCaseMessage === "yercaud hills") {
    return `The Yercaud Hills, part of the Shevaroy range in the Eastern Ghats, are a picturesque mountain landscape in Salem district of Tamil Nadu. Rising to an elevation of about 1,500 meters (4,970 feet), these ancient hills are known for their moderate climate, lush vegetation, and rich biodiversity. The name "Yercaud" comes from the Tamil words "yeri" (lake) and "kaadu" (forest), aptly describing the region's beautiful Emerald Lake surrounded by dense forests. The hills are characterized by their coffee and spice plantations, fruit orchards (particularly oranges, jackfruits, and guavas), and extensive silver oak trees that provide shade to the coffee plants. Unlike the more commercialized Western Ghats hill stations, the Yercaud Hills maintain a tranquil atmosphere with less tourist congestion. The hills feature numerous viewpoints offering panoramic vistas of the Salem plains below, including the famous Lady's Seat, Pagoda Point, and Servarayan Temple viewpoint. The hills are home to diverse flora including orchids, medicinal plants, and the rare Kurinji flower that blooms once every 12 years. Wildlife enthusiasts can spot various bird species, butterflies, and occasionally wild animals like deer and wild boar. The hills also have cultural significance with tribal communities like the Malayalis (not to be confused with people from Kerala) who have inhabited these hills for centuries, maintaining their unique traditions and agricultural practices. For adventure seekers, the hills offer excellent trekking trails, particularly to Kiliyur Falls and through the coffee estates. The Yercaud Hills represent one of Tamil Nadu's most pristine natural environments, offering visitors a perfect blend of natural beauty, adventure, and tranquility.`;
  }

  // DIRECT MATCHES FOR SPECIFIC PLACES
  if (lowerCaseMessage === "ooty") {
    return `Ooty (Udhagamandalam), a popular hill station in the Nilgiri Hills, is known for its pleasant climate and scenic beauty. Top places to visit in Ooty:\n\n1. Botanical Gardens - Established in 1848, featuring a fossil tree trunk estimated to be 20 million years old.\n2. Ooty Lake - An artificial lake created by John Sullivan in 1824, offering boating facilities.\n3. Doddabetta Peak - The highest peak in the Nilgiri mountains with a telescope house.\n4. Nilgiri Mountain Railway - A UNESCO World Heritage Site, this toy train offers breathtaking views.\n5. Rose Garden - Asia's largest rose garden with over 20,000 varieties of roses.`;
  }

  if (lowerCaseMessage === "ooty hills") {
    return `The Ooty Hills, part of the Nilgiri Mountain range in the Western Ghats, are known for their stunning landscapes and biodiversity. These hills, rising to over 2,600 meters, feature rolling grasslands, dense shola forests, tea plantations, and eucalyptus groves. The hills are home to diverse flora and fauna, including several endemic species. The Nilgiri Biosphere Reserve, covering much of these hills, is recognized by UNESCO for its ecological significance. The hills offer numerous trekking trails, viewpoints, and natural attractions like Avalanche Lake, Emerald Lake, and Pykara Falls. The cool climate, misty landscapes, and colonial heritage make the Ooty Hills one of South India's most popular mountain destinations. The hills also support the livelihoods of indigenous tribal communities like the Todas, Kotas, and Badagas, who have unique cultural traditions.`;
  }

  if (lowerCaseMessage === "madurai") {
    return `Madurai, one of the oldest continuously inhabited cities in the world, is known as the Temple City, with the magnificent Meenakshi Amman Temple at its heart. Top places to visit in Madurai:\n\n1. Meenakshi Amman Temple - The iconic temple with 14 gopurams (gateway towers) and thousands of sculptures.\n2. Thirumalai Nayakkar Palace - A 17th-century palace showcasing Dravidian and Islamic architectural styles.\n3. Gandhi Memorial Museum - Houses the blood-stained cloth worn by Gandhi when he was assassinated.\n4. Vandiyur Mariamman Teppakulam - A massive temple tank used for the float festival.\n5. Alagar Koil - A Vishnu temple located 21 km from Madurai with a sacred spring.`;
  }

  if (lowerCaseMessage === "kanyakumari") {
    return `Kanyakumari, the southernmost tip of mainland India, is where the Arabian Sea, Bay of Bengal, and Indian Ocean meet. Top places to visit in Kanyakumari:\n\n1. Vivekananda Rock Memorial - Built on a rock where Swami Vivekananda meditated in 1892.\n2. Thiruvalluvar Statue - A 133-foot tall stone sculpture of the Tamil poet and philosopher.\n3. Kanyakumari Temple - Ancient temple dedicated to Goddess Parvati as Devi Kanya Kumari.\n4. Sunset Point - Famous spot to witness the sunset and sunrise over the ocean.\n5. Gandhi Memorial Mandapam - Built where the ashes of Mahatma Gandhi were kept before immersion.`;
  }

  if (lowerCaseMessage === "jayankondam") {
    return `Jayankondam is a town in Ariyalur district of Tamil Nadu, famous for the Gangaikonda Cholapuram Temple. This UNESCO World Heritage Site was built by Rajendra Chola I in the 11th century to commemorate his victories. The temple features exquisite stone carvings and sculptures, and was once the capital of the Chola Empire for about 250 years. The temple's architecture represents the pinnacle of Chola craftsmanship.`;
  }

  if (lowerCaseMessage === "darasuram") {
    return `Darasuram is home to the magnificent Airavatesvara Temple, a UNESCO World Heritage Site built by Chola king Rajaraja II in the 12th century. This temple is known for its intricate stone carvings and unique architectural features including a stone chariot. Though smaller than other Great Living Chola Temples, it is equally magnificent in its craftsmanship with elaborate carvings depicting scenes from Hindu mythology.`;
  }

  if (lowerCaseMessage === "karaikudi") {
    return `Karaikudi is the main town in the Chettinad region, famous for its palatial mansions built by the wealthy Chettiar merchant community in the 19th-20th centuries. These Chettinad mansions feature a unique blend of European, East Asian, and traditional Tamil architectural styles. They showcase the wealth and global connections of the Chettiar community with imported materials including Italian marble, Burmese teak, and European chandeliers. Karaikudi is also known for its spicy Chettinad cuisine.`;
  }

  if (lowerCaseMessage === "yercaud") {
    return `Yercaud is a serene hill station in Salem district, known as the 'Jewel of the South'. Located in the Shevaroy Hills of the Eastern Ghats, it offers a pleasant climate year-round. Top attractions include Emerald Lake, Kiliyur Falls, Lady's Seat viewpoint, Pagoda Point, and the Botanical Garden. The area is famous for its coffee plantations, orange groves, and spice gardens. Yercaud is less crowded than Ooty or Kodaikanal, making it perfect for a peaceful retreat.`;
  }

  if (lowerCaseMessage === "yercaud hills") {
    return `The Yercaud Hills, part of the Shevaroy range in the Eastern Ghats, are a picturesque mountain landscape in Salem district of Tamil Nadu. Rising to an elevation of about 1,500 meters (4,970 feet), these ancient hills are known for their moderate climate, lush vegetation, and rich biodiversity. The name "Yercaud" comes from the Tamil words "yeri" (lake) and "kaadu" (forest), aptly describing the region's beautiful Emerald Lake surrounded by dense forests. The hills are characterized by their coffee and spice plantations, fruit orchards (particularly oranges, jackfruits, and guavas), and extensive silver oak trees that provide shade to the coffee plants. Unlike the more commercialized Western Ghats hill stations, the Yercaud Hills maintain a tranquil atmosphere with less tourist congestion. The hills feature numerous viewpoints offering panoramic vistas of the Salem plains below, including the famous Lady's Seat, Pagoda Point, and Servarayan Temple viewpoint. The hills are home to diverse flora including orchids, medicinal plants, and the rare Kurinji flower that blooms once every 12 years. Wildlife enthusiasts can spot various bird species, butterflies, and occasionally wild animals like deer and wild boar. The hills also have cultural significance with tribal communities like the Malayalis (not to be confused with people from Kerala) who have inhabited these hills for centuries, maintaining their unique traditions and agricultural practices. For adventure seekers, the hills offer excellent trekking trails, particularly to Kiliyur Falls and through the coffee estates. The Yercaud Hills represent one of Tamil Nadu's most pristine natural environments, offering visitors a perfect blend of natural beauty, adventure, and tranquility.`;
  }

  if (lowerCaseMessage === "yelagiri") {
    return `Yelagiri is a small hill station in Vellore district, offering a peaceful retreat with less crowds than other hill stations. Key attractions include Punganoor Lake Park where you can enjoy boating, Jalagamparai Waterfalls, Swamimalai Hills for trekking, Nature Park, and Telescope Observatory. The hill station is known for its orchards, rose gardens, and the annual summer festival. It's an ideal weekend getaway with pleasant weather throughout the year.`;
  }

  if (lowerCaseMessage === "yelagiri hills") {
    return `Yelagiri Hills is a peaceful hill station situated at an altitude of 1,110 meters. The hills offer panoramic views, pleasant weather, and activities like trekking, paragliding, and rock climbing. Key attractions include the Jalagamparai Waterfalls, Swamimalai Hills, Nature Park, and the serene Punganoor Lake where boating is popular. The hills are dotted with orchards, flower gardens, and walking trails, making it perfect for nature lovers. Unlike more commercialized hill stations, Yelagiri maintains a tranquil atmosphere and is ideal for those seeking a quiet retreat.`;
  }

  if (lowerCaseMessage === "kodaikanal") {
    return `Kodaikanal, known as the "Princess of Hill Stations," is a charming hill station in the Dindigul district of Tamil Nadu. Located at an altitude of 7,000 feet in the Palani Hills, it offers a pleasant climate year-round. Top attractions include the star-shaped Kodaikanal Lake where you can enjoy boating, Bryant Park with its beautiful flower displays, Coaker's Walk offering panoramic views of the valleys, Pillar Rocks with their impressive rock formations, and Silver Cascade Falls. The town is also known for its homemade chocolates, aromatic eucalyptus oil, and the Kurinji flower that blooms once every 12 years.`;
  }

  if (lowerCaseMessage === "kodaikanal hills") {
    return `The Kodaikanal Hills, part of the Palani Hills range in the Western Ghats, offer breathtaking landscapes with dense forests, meadows, grasslands, and valleys. The hills are known for their rich biodiversity, including several endemic species of plants and animals. Popular viewpoints include Dolphin's Nose, a narrow rock projection offering spectacular views of the valleys; Green Valley View (formerly Suicide Point) with its deep valley views; and Pillar Rocks, massive vertical rock pillars standing 400 feet high. The hills are perfect for trekking, with trails leading to hidden waterfalls, pristine lakes, and scenic viewpoints. The cool climate and misty landscapes have earned Kodaikanal the nickname "Gift of the Forest."`;
  }

  if (lowerCaseMessage === "kolli hills") {
    return `Kolli Hills, located in Namakkal district, is known for its 70 hairpin bends on the ghat road. This less-explored hill station offers pristine natural beauty and is famous for the Agaya Gangai waterfall, which requires a trek of 1,300 steps to reach. Other attractions include Arapaleeswarar Temple, Botanical Garden, Seekuparai Viewpoint, and Masila Falls. The area is rich in medicinal herbs and is mentioned in ancient Tamil literature. It's an ideal destination for adventure enthusiasts and nature lovers.`;
  }

  if (lowerCaseMessage === "agaya gangai" || lowerCaseMessage === "agaya gangai falls") {
    return `Agaya Gangai Falls is a magnificent 300-foot waterfall located in the Kolli Hills of Namakkal district. To reach this breathtaking waterfall, visitors must trek down approximately 1,300 steps through lush forest. The name "Agaya Gangai" translates to "Ganges from the sky." The waterfall is most impressive during and after the monsoon season (July to January). The challenging trek is rewarded with the sight of water cascading down the rocky cliff face into a natural pool below. The area around the falls is rich in biodiversity and offers excellent photography opportunities.`;
  }

  if (lowerCaseMessage === "kiliyur" || lowerCaseMessage === "kiliyur falls") {
    return `Kiliyur Falls is a picturesque seasonal waterfall located in Yercaud, Salem district. The waterfall cascades down from the Servarayan Hills (Shevaroy Hills) and is formed by the overflow of the Emerald Lake. The falls drop from a height of about 300 feet into a beautiful valley below. Kiliyur Falls is best visited during and after the monsoon season when the water flow is at its peak. To reach the falls, visitors need to trek down a steep path of about 1 km from the viewpoint. The surrounding area offers stunning views of the Eastern Ghats and is rich in flora and fauna.`;
  }

  if (lowerCaseMessage === "thirparappu" || lowerCaseMessage === "thirparappu falls") {
    return `Thirparappu Falls is a man-made waterfall created by a small dam built across the Kodayar River in Kanyakumari district. The falls have a height of about 50 feet and a width of 300 feet, creating an impressive curtain of water. The water cascades over rocks into a natural pool below where visitors can enjoy a refreshing swim. The falls are surrounded by dense forests and hills, making it a scenic spot for picnics and photography. The best time to visit is between June and December when the water flow is abundant. Nearby is the ancient Mahadeva Temple, which is over 400 years old.`;
  }

  if (lowerCaseMessage === "marina beach") {
    return `Marina Beach in Chennai is the second-longest urban beach in the world, stretching for about 13 km along the Bay of Bengal. This iconic landmark features golden sands, a wide promenade, and numerous statues and monuments including those of Mahatma Gandhi, Robert Caldwell, and Thiruvalluvar. The beach is a hub of activity, especially in the evenings, with food stalls, horse rides, and various games. Visitors can enjoy stunning sunrise views, fishing activities at the harbor end, and the historic buildings nearby including the University of Madras and Vivekananda House. Swimming is not recommended due to strong undercurrents.`;
  }

  if (lowerCaseMessage === "kanyakumari beach") {
    return `Kanyakumari Beach, located at the southernmost tip of mainland India, is where the Arabian Sea, Bay of Bengal, and Indian Ocean meet. This unique geographical position makes it one of the few places in the world where you can witness both sunrise and sunset over the ocean from the same spot. The beach offers spectacular views of the Vivekananda Rock Memorial and the 133-foot tall Thiruvalluvar Statue situated on small islands offshore. During full moon nights, visitors can witness the rare phenomenon of the sunset and moonrise simultaneously. The multi-colored sand and the lighthouse add to the beach's charm.`;
  }

  if (lowerCaseMessage === "kovalam beach" || lowerCaseMessage === "covelong beach") {
    return `Kovalam Beach (also known as Covelong Beach) is located about 40 km south of Chennai. This crescent-shaped beach with golden sands was once a port town developed by the Nawab of Carnatic and later by the Dutch. Today, it's a popular destination for water sports enthusiasts, offering activities like surfing, windsurfing, and swimming. The beach is home to one of India's few surfing schools. The old Dutch fort converted into the Taj Fisherman's Cove resort adds historical charm. The fishing village nearby provides glimpses into the traditional lifestyle of local fishermen. The beach is relatively less crowded compared to Marina Beach, offering a more peaceful experience.`;
  }

  if (lowerCaseMessage === "elliot's beach" || lowerCaseMessage === "elliots beach" || lowerCaseMessage === "besant nagar beach") {
    return `Elliot's Beach, also known as Besant Nagar Beach or "Bessie" to locals, is a pristine stretch of shoreline in the upscale Besant Nagar neighborhood of Chennai. Unlike the more famous and crowded Marina Beach, Elliot's Beach offers a more serene and relaxed atmosphere, making it a favorite among residents seeking tranquility. The beach is named after Edward Elliot, who served as the Chief Magistrate and Superintendent of Police for Madras in the early 1900s. A prominent landmark on the beach is the Karl Schmidt Memorial, a distinctive white structure built in memory of a Dutch sailor who lost his life while saving a drowning swimmer in 1930. The shoreline is well-maintained with clean sands and relatively calm waters, though swimming is generally discouraged due to strong undercurrents. The beach is particularly enchanting during sunrise and sunset, offering spectacular views of the Bay of Bengal. The surrounding area has developed into one of Chennai's most vibrant neighborhoods, with a diverse array of cafes, restaurants, boutiques, and ice cream parlors lining the promenade. On weekends, the beach comes alive with families, joggers, walkers, and young people, creating a lively yet relaxed ambiance. Ashtalakshmi Temple, dedicated to the eight forms of Goddess Lakshmi, is located nearby and adds cultural significance to the area. Elliot's Beach represents the perfect balance between urban convenience and natural beauty, making it an essential part of Chennai's coastal charm.`;
  }

  if (lowerCaseMessage === "mahabalipuram") {
    return `Mahabalipuram (also known as Mamallapuram) is a UNESCO World Heritage Site famous for its magnificent stone carvings and temples from the 7th-8th century Pallava dynasty. Key attractions include the Shore Temple standing on the coastline, the Five Rathas (monolithic temples shaped like chariots), Arjuna's Penance (one of the largest open-air rock reliefs in the world), Krishna's Butter Ball (a massive balancing rock), and numerous cave temples with intricate sculptures. The town was an important seaport and trading center in ancient times. Today, it's also known for its stone carving industry, with many artisans continuing the traditional craft. The combination of historical monuments, beach, and cultural significance makes it one of Tamil Nadu's most important heritage destinations.`;
  }

  if (lowerCaseMessage === "mahabalipuram beach") {
    return `Mahabalipuram Beach (also known as Mamallapuram Beach) offers a unique blend of history, culture, and natural beauty. The beach is famous for the Shore Temple, a UNESCO World Heritage Site built by the Pallava dynasty in the 8th century, which stands majestically on its shoreline. The golden sands stretch for several kilometers, providing a perfect setting for relaxation and beach activities. The beach is popular for its stone sculptures and rathas (temple chariots) carved out of single rocks. Visitors can enjoy swimming in designated areas, though caution is advised due to occasional strong currents. The beach is also known for its seafood restaurants serving fresh catches of the day.`;
  }

  if (lowerCaseMessage === "rameshwaram" || lowerCaseMessage === "rameswaram") {
    return `Rameshwaram is a sacred island town in Tamil Nadu, connected to the mainland by the Pamban Bridge. It's one of the holiest pilgrimage sites for Hindus as it's associated with Lord Rama's journey to Lanka. The town is famous for the magnificent Ramanathaswamy Temple, one of the twelve Jyotirlinga temples dedicated to Lord Shiva, known for its impressive corridors (the longest in India) and 22 sacred water tanks. Other attractions include Dhanushkodi (the ghost town destroyed in the 1964 cyclone), Pamban Bridge (India's first sea bridge), Agni Theertham (a sacred bathing spot), and the former President Dr. A.P.J. Abdul Kalam's memorial. The town's spiritual significance, architectural splendor, and natural beauty make it a unique destination combining religious pilgrimage with historical interest.`;
  }

  if (lowerCaseMessage === "rameshwaram beach") {
    return `Rameshwaram Beach, located on Pamban Island, offers pristine waters and unique geography where the Bay of Bengal meets the Indian Ocean. The beach at Dhanushkodi is known for its ghost town ruins that were destroyed in a 1964 cyclone, creating a hauntingly beautiful landscape. The clear turquoise waters and white sands make it perfect for long walks and photography. The beach is considered sacred by pilgrims who visit the nearby Ramanathaswamy Temple, as it's believed that Lord Rama crossed to Lanka from this point. The shallow waters make it suitable for wading, though swimming is restricted in many areas due to strong currents.`;
  }

  if (lowerCaseMessage === "gangaikonda cholapuram" || lowerCaseMessage === "gangaikonda cholapuram temple") {
    return `Gangaikonda Cholapuram Temple is a magnificent UNESCO World Heritage Site built by Rajendra Chola I in the 11th century to commemorate his victories over the Gangetic plains. The temple, dedicated to Lord Shiva, features a 55-meter high vimana (tower) and exquisite stone carvings that showcase the architectural brilliance of the Chola dynasty. Notable sculptures include the Chandesanugrahamurti panel, depicting Lord Shiva blessing Chandesa, and the dancing Ganesha. The temple was the royal capital for about 250 years, though most of the palace structures have been lost to time. The engineering marvel includes a water tank called Chola Gangam, built to commemorate the bringing of Ganges water to this temple.`;
  }

  if (lowerCaseMessage === "arjuna's penance" || lowerCaseMessage === "arjunas penance") {
    return `Arjuna's Penance in Mahabalipuram is one of the largest open-air rock reliefs in the world, carved on two enormous adjacent boulders spanning 27 meters in length and 9 meters in height. Created during the 7th century Pallava dynasty, this masterpiece depicts either Arjuna's penance to obtain Lord Shiva's weapon or the story of the descent of the River Ganges to Earth. The intricate carving features over 100 figures of gods, humans, and animals, including a family of elephants that appears remarkably lifelike. The central cleft represents the River Ganges, with nagas (serpent deities) and celestial beings on either side. This magnificent relief demonstrates the exceptional artistic skills of ancient Indian sculptors and is a testament to the rich cultural heritage of the region.`;
  }

  if (lowerCaseMessage === "airavatesvara temple") {
    return `Airavatesvara Temple in Darasuram is a UNESCO World Heritage Site built by Chola king Rajaraja II in the 12th century. This architectural marvel is smaller than the other Great Living Chola Temples but equally magnificent in its craftsmanship. The temple is dedicated to Lord Shiva and named after Airavata, the white elephant of Indra who worshipped Shiva here. The temple's unique features include a stone chariot with wheels that actually rotate and a miniature shrine designed as a horse-drawn chariot. The pillared halls contain elaborate carvings depicting scenes from Hindu mythology, daily life, and various dance forms. The temple's mandapam (hall) has 108 pillars with intricate sculptures, and the walls feature inscriptions in Tamil and Sanskrit that provide valuable historical information about the Chola period.`;
  }

  if (lowerCaseMessage === "rock fort" || lowerCaseMessage === "rock fort temple") {
    return `Rock Fort Temple in Trichy (Tiruchirappalli) is a historic fortification and temple complex built on an ancient rock that is estimated to be around 3.8 billion years old, making it one of the oldest rock formations in the world. The complex rises 83 meters high and consists of two temples: the Ucchi Pillayar Temple dedicated to Lord Ganesha at the top, and the Thayumanaswamy Temple dedicated to Lord Shiva halfway up the rock. Visitors need to climb 437 steps cut into the rock to reach the summit, which offers panoramic views of Trichy city and the Kaveri River. The fort was built by the Pallavas but was later modified by the Cholas, Vijayanagara rulers, Nayaks, and the British. The rock has witnessed numerous historical battles and is a symbol of the region's rich heritage.`;
  }

  if (lowerCaseMessage === "vellore fort") {
    return `Vellore Fort is a large 16th-century fort situated in the heart of Vellore city, built by Chinna Bommi Nayak, a chieftain of the Vijayanagara Empire. The fort is known for its grand ramparts, wide moat, and robust masonry. Inside the fort complex, visitors can find the beautiful Jalakanteswarar Temple with its intricate carvings, a church, and a mosque, showcasing religious harmony. The fort houses a museum displaying artifacts from different periods. Historically significant as the site of the Vellore Mutiny of 1806, considered the first instance of a military uprising against British rule in India, the fort also served as a prison for the royal family of Srirangapatna, including Tipu Sultan's family. The fort's architecture blends military functionality with aesthetic elements, making it one of the best-preserved forts in Tamil Nadu.`;
  }

  if (lowerCaseMessage === "chettinad mansions" || lowerCaseMessage === "chettinad palace") {
    return `Chettinad Mansions are opulent palatial homes built by the wealthy Chettiar merchant community in the Chettinad region, primarily during the 19th and early 20th centuries. These architectural marvels feature a unique blend of European, East Asian, and traditional Tamil styles. The mansions are known for their spacious courtyards, massive teak wood doors, Italian marble floors, Spanish tiles, Belgian glass mirrors, and intricate carvings. Some mansions have as many as 100 rooms arranged around multiple courtyards. The Chettiars, who were successful traders and bankers, imported materials from around the world: teak from Burma, marble from Italy, chandeliers from Europe, and tiles from Japan. Many of these mansions are still owned by Chettiar families, though some have been converted into heritage hotels. The mansions stand as a testament to the wealth, global connections, and aesthetic sensibilities of the Chettiar community.`;
  }

  if (lowerCaseMessage === "thirumalai nayakkar palace") {
    return `Thirumalai Nayakkar Palace in Madurai is a magnificent 17th-century palace built by King Thirumalai Nayak in 1636. This architectural masterpiece represents a fusion of Dravidian and Islamic styles, showcasing the cultural blend that characterized the Nayak dynasty. The palace originally covered a vast area four times its current size, with only the main palace structure remaining today. The most impressive feature is the Swarga Vilasam (Celestial Pavilion), a grand ceremonial hall with a height of 73 feet, supported by massive white pillars each 4 feet in diameter. The intricate stucco work on the domes and arches displays exceptional craftsmanship. The palace's courtyard features ornate arches, detailed paintings, and a sophisticated drainage system. The evening sound and light show narrates the rich history of Madurai and the Nayak dynasty, bringing the palace's past to life. Despite being only a quarter of its original size, the palace remains one of South India's most impressive historical monuments, attracting architecture enthusiasts and history buffs alike.`;
  }

  if (lowerCaseMessage === "athangudi palace") {
    return `Athangudi Palace in Chettinad is a stunning heritage mansion renowned for its exquisite handmade Athangudi tiles that create vibrant, kaleidoscopic floor patterns throughout the building. Built in the early 20th century by wealthy Chettiar merchants, this architectural gem exemplifies the unique Chettinad style that blends Tamil traditions with European and East Asian influences. The palace features multiple spacious courtyards (mutrams) designed to provide natural ventilation and light, massive wooden doors with intricate carvings, high ceilings adorned with Belgian mirrors, walls decorated with Japanese enamel tiles, and pillars made from Italian marble and Burmese teak. The most distinctive feature is the famous Athangudi floor tiles, created using a special technique where glass is poured over natural pigments in wooden frames and sun-dried, resulting in glossy, durable tiles with unique patterns and colors. Each room in the palace showcases different tile designs, creating a visual feast for visitors. The palace's meticulous restoration has preserved its original grandeur, offering visitors an authentic glimpse into the opulent lifestyle of the prosperous Chettiar community. Today, the palace serves as both a tourist attraction and a living museum of Chettinad's architectural heritage.`;
  }

  if (lowerCaseMessage === "padmanabhapuram palace") {
    return `Padmanabhapuram Palace, located in Kanyakumari district near the border with Kerala, is the largest wooden palace in Asia and a masterpiece of traditional Kerala architecture. Built primarily in the 16th century by the Travancore rulers, the palace complex spans 6.5 acres and consists of various structures including the King's Council Chamber, the Queen Mother's Palace, and the Nataksala (performance hall). The palace is renowned for its intricate rosewood and teakwood carvings, granite floors polished to a mirror-like finish using egg whites and plant extracts, and traditional Kerala-style sloping roofs designed to manage heavy rainfall. Notable features include the medicinal bed made of 64 types of wood with healing properties, the Belgian mirrors, and the Chinese jars. Though located in Tamil Nadu, the palace is maintained by the Kerala government due to historical agreements. The palace offers a fascinating glimpse into the royal lifestyle of the Travancore dynasty.`;
  }

  if (lowerCaseMessage === "hogenakkal" || lowerCaseMessage === "hogenakkal falls") {
    return `Hogenakkal Falls, often called the "Niagara of India," is located on the Kaveri River in Dharmapuri district. The name "Hogenakkal" comes from Kannada words "Hoge" (smoke) and "Kal" (rock), referring to the mist that rises from the falls resembling smoke. The falls consist of a series of cascades dropping from heights ranging from 15 to 65 feet, creating a spectacular sight especially during the monsoon season when the river is in full flow. One of the unique experiences here is the coracle ride, where visitors can travel in traditional circular boats made of bamboo and buffalo hide. The area is also known for its medicinal baths, as the water is believed to have curative properties due to the herbs it passes through. The carbonatite rocks found here are among the oldest in South Asia, dating back to 2.5 billion years. The surrounding landscape offers excellent trekking opportunities and views of the Eastern Ghats.`;
  }

  if (lowerCaseMessage === "courtallam" || lowerCaseMessage === "courtallam falls") {
    return `Courtallam Falls, known as the "Spa of South India," is located in Tenkasi district near the Western Ghats. The falls are renowned for their medicinal properties as the water passes through forests rich in herbs before cascading down. There are nine different falls in the area, with the Main Falls (Peraruvi) being the largest with a 60-foot drop. Other notable falls include the Five Falls (Aintharuvi), Small Falls (Chitraruvi), and Old Falls (Pazhaya Courtallam). The water is believed to help cure various skin ailments and nervous disorders. The falls are surrounded by lush forests and offer a refreshing retreat, especially during the southwest monsoon season from June to September. The nearby Courtallam Shiva Temple, dating back to the 7th century, adds cultural significance to the area. The combination of natural beauty, therapeutic waters, and religious importance makes Courtallam a popular destination for both wellness and leisure.`;
  }

  // DIRECT MATCHES FOR CATEGORIES
  if (lowerCaseMessage === "beaches" || lowerCaseMessage === "beach") {
    return `Tamil Nadu has a beautiful coastline with several beaches. Here are some popular beaches:\n\n1. Marina Beach (Chennai) - The second-longest urban beach in the world.\n2. Covelong Beach (Kovalam) - Known for water sports and surfing.\n3. Mahabalipuram Beach - Historic beach with shore temples.\n4. Rameshwaram Beach - A sacred beach with clear waters.\n5. Kanyakumari Beach - Where three oceans meet, famous for sunrise and sunset views.\n6. Elliot's Beach (Chennai) - Also known as Besant Nagar Beach, a quieter alternative to Marina Beach.`;
  }

  if (lowerCaseMessage === "hills" || lowerCaseMessage === "hill stations" || lowerCaseMessage === "hill station") {
    return `Tamil Nadu has several beautiful hill stations. Here are the most popular ones:\n\n1. Ooty - Known for its botanical gardens and pleasant climate.\n2. Kodaikanal - Famous for its star-shaped lake and cool climate.\n3. Yelagiri - A smaller, less crowded hill station with trekking opportunities, orchards, and rose gardens.\n4. Kolli Hills - Known for its 70 hairpin bends and Agaya Gangai waterfall.\n5. Yercaud - A serene hill station with coffee plantations, orange groves, and the beautiful Emerald Lake.\n6. Coonoor - Famous for its tea plantations and the Nilgiri Mountain Railway.\n7. Valparai - A lesser-known hill station with tea estates and wildlife sanctuaries.\n8. Meghamalai - Known as the 'High Wavy Mountains', famous for its tea and coffee plantations.\n9. Javadi Hills - A part of the Eastern Ghats with beautiful waterfalls and tribal villages.\n10. Sirumalai - A small hill station near Dindigul with pleasant climate and fruit orchards.`;
  }

  if (lowerCaseMessage === "temples" || lowerCaseMessage === "temple") {
    return `Tamil Nadu is famous for its magnificent temples. Here are some notable temples:\n\n1. Brihadeeswarar Temple (Thanjavur) - A UNESCO World Heritage Site built by Raja Raja Chola I.\n2. Meenakshi Amman Temple (Madurai) - Known for its colorful gopurams and thousands of sculptures.\n3. Shore Temple (Mahabalipuram) - A UNESCO site with beautiful seaside location.\n4. Ramanathaswamy Temple (Rameswaram) - Famous for its long corridors and 22 holy water tanks.\n5. Ekambareswarar Temple (Kanchipuram) - One of the five major Shiva temples with a 3,500-year-old mango tree.`;
  }

  if (lowerCaseMessage === "waterfalls" || lowerCaseMessage === "waterfall") {
    return `Tamil Nadu has several beautiful waterfalls. Here are some notable ones:\n\n1. Hogenakkal Falls (Dharmapuri) - Known as the 'Niagara of India', famous for its medicinal baths and boat rides.\n2. Agaya Gangai Falls (Kolli Hills) - A 300-foot waterfall requiring a trek of 1,300 steps to reach its base.\n3. Kiliyur Falls (Yercaud) - A seasonal waterfall that cascades down from the Servarayan Hills.\n4. Thirparappu Falls (Kanyakumari) - A man-made waterfall created by a small dam across the Kodayar River.\n5. Courtallam Falls (Tenkasi) - Known as the 'Spa of South India' for its medicinal properties.`;
  }

  if (lowerCaseMessage === "palaces" || lowerCaseMessage === "palace") {
    return `Tamil Nadu has several magnificent palaces showcasing different architectural styles. Here are some notable palaces:\n\n1. Thanjavur Palace - Built by the Nayak rulers and later expanded by the Marathas, houses the Saraswathi Mahal Library.\n2. Thirumalai Nayakkar Palace (Madurai) - A 17th-century palace with Indo-Saracenic architecture and massive pillars.\n3. Padmanabhapuram Palace (Kanyakumari district) - The largest wooden palace in Asia with traditional Kerala architecture.\n4. Chettinad Mansions (Karaikudi) - Palatial homes with a blend of European, East Asian, and Tamil architectural styles.\n5. Aayiram Jannal Veedu (Thousand Window House) in Karaikudi - A famous Chettiar mansion with numerous windows.`;
  }

  if (lowerCaseMessage === "monuments" || lowerCaseMessage === "monument") {
    return `Tamil Nadu has several historical monuments showcasing its rich heritage. Here are some notable monuments:\n\n1. Vellore Fort - A 16th-century fort with a moat and robust ramparts housing a temple, church, and mosque.\n2. Rock Fort (Trichy) - A historic fortification built on an ancient rock with the Ucchi Pillayar Temple at the top.\n3. Gingee Fort (Villupuram) - Called the "Troy of the East" with three hilltop citadels connected by walls.\n4. Danish Fort (Tranquebar) - A 17th-century fort built by Danish colonists on the Coromandel Coast.\n5. Arjuna's Penance (Mahabalipuram) - A massive open-air bas-relief sculpture depicting scenes from Hindu mythology.`;
  }

  if (lowerCaseMessage === "museums" || lowerCaseMessage === "museum") {
    return `Tamil Nadu has several museums preserving its cultural and historical artifacts. Here are some notable museums:\n\n1. Government Museum Chennai - One of the oldest museums in India with rich collections of archaeology and Chola bronzes.\n2. Thanjavur Art Gallery - Houses bronze statues from the Chola period and other artifacts.\n3. DakshinaChitra Museum (Chennai) - An interactive museum showcasing the art, architecture, and culture of South India.\n4. Gandhi Memorial Museum (Madurai) - Houses the blood-stained cloth worn by Gandhi when he was assassinated.\n5. Indo-French Historical Museum (Puducherry) - Showcases the French colonial influence in the region.`;
  }

  if (lowerCaseMessage === "churches" || lowerCaseMessage === "church") {
    return `Tamil Nadu has several historic and architecturally significant churches. Here are some notable churches:\n\n1. Santhome Basilica (Chennai) - Built over the tomb of St. Thomas the Apostle, one of only three churches in the world built over an apostle's tomb.\n2. St. Mary's Church (Chennai) - The oldest Anglican church in India and the oldest British building in Chennai, built between 1678-1680.\n3. Velankanni Church (Nagapattinam) - Known as the "Lourdes of the East," this basilica attracts millions of pilgrims annually from various faiths.\n4. St. Thomas Mount Church (Chennai) - Built at the site where St. Thomas was martyred in 72 AD, featuring the famous "Bleeding Cross."\n5. Christ the King Church (Pudukkottai) - A magnificent church blending Gothic and Indian architectural styles.\n6. St. Francis Xavier Church (Kottar, Kanyakumari) - One of the oldest churches in Tamil Nadu, built by Portuguese missionaries in the 16th century.`;
  }

  // PATTERN MATCHING FOR COMPLEX QUERIES
  if (lowerCaseMessage.includes("places to visit in") ||
      lowerCaseMessage.includes("places in") ||
      lowerCaseMessage.includes("must visit places in") ||
      lowerCaseMessage.includes("tourist places in") ||
      lowerCaseMessage.includes("famous places in") ||
      lowerCaseMessage.includes("best places in") ||
      lowerCaseMessage.includes("attractions in")) {

    let cityName = "";
    if (lowerCaseMessage.includes("places to visit in")) {
      cityName = lowerCaseMessage.split("places to visit in")[1].trim();
    } else if (lowerCaseMessage.includes("must visit places in")) {
      cityName = lowerCaseMessage.split("must visit places in")[1].trim();
    } else if (lowerCaseMessage.includes("tourist places in")) {
      cityName = lowerCaseMessage.split("tourist places in")[1].trim();
    } else if (lowerCaseMessage.includes("famous places in")) {
      cityName = lowerCaseMessage.split("famous places in")[1].trim();
    } else if (lowerCaseMessage.includes("best places in")) {
      cityName = lowerCaseMessage.split("best places in")[1].trim();
    } else if (lowerCaseMessage.includes("attractions in")) {
      cityName = lowerCaseMessage.split("attractions in")[1].trim();
    } else {
      cityName = lowerCaseMessage.split("places in")[1].trim();
    }

    // Check for common cities
    if (cityName.includes("madurai") || cityName.includes("madura")) {
      return `Top places to visit in Madurai:\n\n1. Meenakshi Amman Temple - The iconic temple with 14 gopurams (gateway towers) and thousands of sculptures.\n2. Thirumalai Nayakkar Palace - A 17th-century palace showcasing Dravidian and Islamic architectural styles.\n3. Gandhi Memorial Museum - Houses the blood-stained cloth worn by Gandhi when he was assassinated.\n4. Vandiyur Mariamman Teppakulam - A massive temple tank used for the float festival.\n5. Alagar Koil - A Vishnu temple located 21 km from Madurai with a sacred spring.`;
    }

    if (cityName.includes("chennai")) {
      return `Top places to visit in Chennai:\n\n1. Marina Beach - The second-longest urban beach in the world.\n2. Fort St. George - The first English fortress in India, now housing the Tamil Nadu government secretariat.\n3. Kapaleeshwarar Temple - A 7th-century Dravidian-style temple dedicated to Lord Shiva.\n4. Government Museum - One of the oldest museums in India with archaeological collections.\n5. San Thome Basilica - Built over the tomb of St. Thomas the Apostle.`;
    }

    if (cityName.includes("thanjavur") || cityName.includes("tanjore")) {
      return `Top places to visit in Thanjavur:\n\n1. Brihadeeswarar Temple - The UNESCO World Heritage Site with its 216-foot tall vimana.\n2. Thanjavur Palace - Built by the Nayaks and later by the Marathas, housing the Saraswathi Mahal Library.\n3. Saraswathi Mahal Library - One of the oldest libraries in Asia with rare manuscripts.\n4. Thanjavur Art Gallery - Houses bronze statues from the Chola period.\n5. Schwartz Church - Built in 1779 by Raja Serfoji in honor of Rev. C.V. Schwartz.`;
    }

    if (cityName.includes("ooty") || cityName.includes("nilgiris")) {
      return `Top places to visit in Ooty (Nilgiris):\n\n1. Botanical Gardens - Established in 1848, featuring a fossil tree trunk estimated to be 20 million years old.\n2. Ooty Lake - An artificial lake created by John Sullivan in 1824, offering boating facilities.\n3. Doddabetta Peak - The highest peak in the Nilgiri mountains with a telescope house.\n4. Nilgiri Mountain Railway - A UNESCO World Heritage Site, this toy train offers breathtaking views.\n5. Rose Garden - Asia's largest rose garden with over 20,000 varieties of roses.`;
    }

    if (cityName.includes("kanyakumari") || cityName.includes("kaniyakumari")) {
      return `Top places to visit in Kanyakumari:\n\n1. Vivekananda Rock Memorial - Built on a rock where Swami Vivekananda meditated in 1892.\n2. Thiruvalluvar Statue - A 133-foot tall stone sculpture of the Tamil poet and philosopher.\n3. Kanyakumari Temple - Ancient temple dedicated to Goddess Parvati as Devi Kanya Kumari.\n4. Sunset Point - Famous spot to witness the sunset and sunrise over the ocean.\n5. Gandhi Memorial Mandapam - Built where the ashes of Mahatma Gandhi were kept before immersion.`;
    }

    if (cityName.includes("dindigul")) {
      return `Top places to visit in Dindigul:\n\n1. Dindigul Fort - A 17th-century fort built by the Nayak rulers and later strengthened by Hyder Ali and Tipu Sultan.\n2. Kodaikanal - A beautiful hill station just 100 km from Dindigul.\n3. Palani Murugan Temple - One of the six abodes of Lord Murugan, located 40 km from Dindigul.\n4. Kamarajar Lake - A scenic lake with boating facilities and a children's park.\n5. Athoor Lake - A serene lake surrounded by hills and agricultural fields.`;
    }

    if (cityName.includes("tenkasi")) {
      return `Top places to visit in Tenkasi:\n\n1. Kasi Viswanathar Temple - A beautiful temple with a gopuram visible from miles away.\n2. Courtallam Falls - Known as the 'Spa of South India' with five different falls having medicinal properties.\n3. Krishnapuram Palace - A historical palace with traditional Kerala architecture.\n4. Agasthiyar Falls - A scenic waterfall surrounded by dense forests.\n5. Thirumalai Kovil - A hill temple offering panoramic views of the surrounding area.`;
    }

    if (cityName.includes("dharmapuri")) {
      return `Top places to visit in Dharmapuri:\n\n1. Hogenakkal Falls - Known as the 'Niagara of India', famous for its medicinal baths and boat rides.\n2. Theerthamalai Temple - An ancient temple located on a small hill.\n3. Adhiyamankottai - The capital of the ancient Adhiyaman kingdom with historical ruins.\n4. Kottai Kovil - A fort temple with beautiful architecture.\n5. Vathalmalai - A hill village known for its coffee plantations and tribal culture.`;
    }

    if (cityName.includes("jayankondam")) {
      return `Top places to visit in Jayankondam:\n\n1. Gangaikonda Cholapuram Temple - A UNESCO World Heritage Site built by Rajendra Chola I in the 11th century.\n2. Gangaikonda Cholapuram Lake - A historic lake built by Rajendra Chola I.\n3. Sivan Koil - An ancient Shiva temple with beautiful sculptures.\n4. Brahmapureeswarar Temple - A temple dedicated to Lord Shiva with unique architecture.\n5. Nageswaran Temple - A temple known for its intricate stone carvings and historical significance.`;
    }

    if (cityName.includes("darasuram")) {
      return `Top places to visit in Darasuram:\n\n1. Airavatesvara Temple - A UNESCO World Heritage Site built by Rajaraja II with unique stone carvings.\n2. Patteeswaram Temple - An ancient temple dedicated to Goddess Patteeswaram Devi.\n3. Darasuram Museum - Houses artifacts and sculptures from the Chola period.\n4. Shiva Temples Circuit - Several smaller Shiva temples around Darasuram with historical significance.\n5. Swamimalai - A nearby town famous for bronze idol making, just 5 km from Darasuram.`;
    }

    if (cityName.includes("karaikudi") || cityName.includes("chettinad")) {
      return `Top places to visit in Karaikudi (Chettinad):\n\n1. Chettinad Mansions - Palatial homes with unique architecture blending European, East Asian, and Tamil styles.\n2. Athangudi Palace - Famous for its handmade Athangudi tiles and traditional architecture.\n3. Chettinad Raja's Palace (Kanadukathan) - One of the grandest mansions in the region open to visitors.\n4. Karpagavinayagar Temple - An ancient temple with beautiful carvings and architecture.\n5. Chettinad Culinary Experience - Try the famous spicy Chettinad cuisine at local restaurants.`;
    }
  }

  // Check for general heritage tourism questions
  if (lowerCaseMessage.includes("best time to visit") || lowerCaseMessage.includes("when to visit")) {
    return "The best time to visit Tamil Nadu's heritage sites is from October to March when the weather is pleasant. Avoid summer months (April-June) as temperatures can be extremely high.";
  }

  if (lowerCaseMessage.includes("how to reach") || lowerCaseMessage.includes("how to get to")) {
    return "Most heritage sites in Tamil Nadu are well-connected by road. Major cities like Chennai, Madurai, and Trichy have airports and railway stations. From there, you can hire taxis or take buses to reach specific heritage sites.";
  }

  if (lowerCaseMessage.includes("unesco") || lowerCaseMessage.includes("world heritage")) {
    return "Tamil Nadu has two UNESCO World Heritage Sites: 1) The Great Living Chola Temples (Brihadeeswarar Temple, Gangaikonda Cholapuram, and Airavatesvara Temple) and 2) The Group of Monuments at Mahabalipuram.";
  }

  if (lowerCaseMessage.includes("famous") || lowerCaseMessage.includes("popular") || lowerCaseMessage.includes("important")) {
    return "The most famous heritage sites in Tamil Nadu include Brihadeeswarar Temple (Thanjavur), Meenakshi Amman Temple (Madurai), Shore Temple (Mahabalipuram), Ramanathaswamy Temple (Rameswaram), and the temples of Kanchipuram.";
  }

  // Try fuzzy matching for city names
  const cityMatch = extractCityWithFuzzyMatch(lowerCaseMessage);
  if (cityMatch && tamilNaduPlaces && tamilNaduPlaces[cityMatch]) {
    return tamilNaduPlaces[cityMatch];
  }

  // Check in our expanded Tamil Nadu places list
  if (tamilNaduPlaces) {
    for (const place in tamilNaduPlaces) {
      if (lowerCaseMessage.includes(place)) {
        return tamilNaduPlaces[place];
      }
    }
  }

  // Then check in our simple tourist places list with fuzzy matching
  const touristPlaces = {
    "brihadeeswarar temple": "Brihadeeswarar Temple, also known as the Big Temple, is a UNESCO World Heritage Site located in Thanjavur. It is renowned for its architectural brilliance and historical significance.",
    "meenakshi amman temple": "Meenakshi Amman Temple in Madurai is famous for its stunning architecture and vibrant sculptures. It is a major pilgrimage site in Tamil Nadu.",
    "shore temple": "The Shore Temple at Mahabalipuram is a UNESCO World Heritage Site built by the Pallava dynasty. Standing on the shores of the Bay of Bengal, it's one of the oldest stone temples in South India.",
    "ramanathaswamy temple": "Ramanathaswamy Temple in Rameswaram is one of the twelve Jyotirlinga temples dedicated to Lord Shiva. It is known for its long corridors and sacred water tanks.",
    "kodaikanal": "Kodaikanal is a popular hill station in Tamil Nadu, known for its scenic beauty, serene lakes, and pleasant climate.",
    "ooty": "Ooty, also known as Udhagamandalam, is a famous hill station in Tamil Nadu, known for its tea gardens, botanical gardens, and the Nilgiri Mountain Railway.",
    "kanyakumari": "Kanyakumari, located at the southernmost tip of India, is famous for its stunning sunrise and sunset views, as well as the Vivekananda Rock Memorial.",
    "chennai": "Chennai, the capital of Tamil Nadu, is a major cultural, economic, and educational center. It's known for Marina Beach, Kapaleeshwarar Temple, Fort St. George, and its rich cultural heritage.",
    "nilgiris": "The Nilgiris (Blue Mountains) is a mountain range in Tamil Nadu known for its stunning landscapes, tea plantations, and hill stations like Ooty and Coonoor.",
    "dindigul": "Dindigul is known for its historic fort built by the Nayak rulers and later strengthened by Hyder Ali and Tipu Sultan. It's also famous for its locks and biryani.",
    "tenkasi": "Tenkasi is known for the Kasi Viswanathar Temple with its impressive gopuram and the nearby Courtallam Falls, known as the 'Spa of South India' for its medicinal properties.",
    "dharmapuri": "Dharmapuri is known for Hogenakkal Falls (the 'Niagara of India'), ancient temples, and tribal culture. The region is rich in agriculture and natural beauty.",
    "jayankondam": "Jayankondam is famous for the Gangaikonda Cholapuram Temple, a UNESCO World Heritage Site built by Rajendra Chola I in the 11th century, featuring exquisite stone carvings.",
    "darasuram": "Darasuram is home to the Airavatesvara Temple, a UNESCO World Heritage Site built by Rajaraja II in the 12th century, known for its intricate stone carvings and unique architecture.",
    "karaikudi": "Karaikudi is the main town in the Chettinad region, famous for its palatial mansions built by the Chettiar merchant community, unique architecture, and spicy Chettinad cuisine.",
    "palaces": "Tamil Nadu has several magnificent palaces including Thanjavur Palace, Thirumalai Nayakkar Palace in Madurai, and the Chettinad mansions in Karaikudi region.",
    "monuments": "Tamil Nadu has impressive monuments including Vellore Fort, Rock Fort in Trichy, Gingee Fort, Danish Fort in Tranquebar, and the sculptures of Mahabalipuram.",
    "museums": "Tamil Nadu's museums include Government Museum Chennai, Thanjavur Art Gallery, DakshinaChitra Museum, Gandhi Memorial Museum in Madurai, and Indo-French Historical Museum in Puducherry.",
    "yercaud": "Yercaud is a serene hill station in Salem district, known as the 'Jewel of the South'. It offers a pleasant climate year-round with attractions like Emerald Lake, Kiliyur Falls, and coffee plantations.",
    "yelagiri": "Yelagiri is a small hill station in Vellore district with attractions like Punganoor Lake Park, Jalagamparai Waterfalls, and Swamimalai Hills for trekking.",
    "kolli hills": "Kolli Hills in Namakkal district is known for its 70 hairpin bends and the Agaya Gangai waterfall. It's rich in medicinal herbs and offers pristine natural beauty.",
    "waterfalls": "Tamil Nadu has several beautiful waterfalls including Hogenakkal Falls, Agaya Gangai Falls, Kiliyur Falls, Thirparappu Falls, and Courtallam Falls.",
    "beaches": "Tamil Nadu has a beautiful coastline with beaches like Marina Beach, Covelong Beach, Mahabalipuram Beach, Rameshwaram Beach, and Elliot's Beach.",
    "kovalam beach": "Kovalam Beach (Covelong) near Chennai is famous for water sports like surfing, windsurfing, and swimming, with golden sands and clear waters.",
    "mahabalipuram beach": "Mahabalipuram Beach offers a unique blend of history, culture, and natural beauty with the famous Shore Temple on its coastline.",
    "rameshwaram beach": "Rameshwaram Beach has crystal clear waters and is considered holy by pilgrims who visit the nearby Ramanathaswamy Temple.",
    "elliot's beach": "Elliot's Beach (Besant Nagar Beach) in Chennai is a quieter alternative to Marina Beach with fewer crowds and a more relaxed atmosphere.",
    "agaya gangai falls": "Agaya Gangai Falls is a 300-foot waterfall in Kolli Hills requiring a trek of 1,300 steps to reach its base.",
    "kiliyur falls": "Kiliyur Falls is a seasonal waterfall in Yercaud that cascades down from the Servarayan Hills, best visited during and after the monsoon season.",
    "thirparappu falls": "Thirparappu Falls in Kanyakumari district is a man-made waterfall created by a small dam across the Kodayar River, surrounded by dense forests and hills.",
    "santhome basilica": "Santhome Basilica in Chennai is built over the tomb of St. Thomas the Apostle. It's one of only three churches in the world built over an apostle's tomb, featuring Neo-Gothic architecture with a stunning white façade and soaring spires.",
    "st. mary's church": "St. Mary's Church in Chennai is the oldest Anglican church in India and the oldest British building in Chennai, built between 1678-1680. Often referred to as the 'Westminster Abbey of the East', it houses several valuable artifacts including ancient tombstones with fascinating epitaphs.",
    "velankanni church": "Velankanni Church, officially the Basilica of Our Lady of Good Health, is known as the 'Lourdes of the East'. Located in Nagapattinam, this Catholic pilgrimage site attracts millions of devotees annually from various faiths and is known for miraculous healing tales.",
    "st. thomas mount church": "St. Thomas Mount Church in Chennai is built at the site where St. Thomas was martyred in 72 AD. Its most treasured possession is the ancient 'Bleeding Cross' carved by St. Thomas himself, which is said to have miraculously bled throughout history.",
    "christ the king church": "Christ the King Church in Pudukkottai is a magnificent Catholic church that blends Gothic and Indian architectural styles. It features soaring spires, pointed arches, and ribbed vaults characteristic of Gothic design while incorporating elements of local Tamil architectural traditions.",
    "st. francis xavier church": "St. Francis Xavier Church in Kottar, Kanyakumari is one of the oldest churches in Tamil Nadu, built by Portuguese missionaries in the 16th century. It features a blend of Portuguese, Gothic, and local Indian architectural styles with an impressive façade and detailed stonework."
  };

  // First check for direct location matches
  for (const place in touristPlaces) {
    if (lowerCaseMessage.includes(place)) {
      return touristPlaces[place];
    }
  }

  // Then try fuzzy matching on individual words
  const placeNames = Object.keys(touristPlaces);
  const words = lowerCaseMessage.split(/\s+/);

  for (const word of words) {
    if (word.length < 3) continue; // Allow shorter words to match locations like "ooty"

    // First check for specific hill locations to prevent matching with generic "hills" category
    if (lowerCaseMessage === "yercaud hills" || lowerCaseMessage === "ooty hills" ||
        lowerCaseMessage === "kodaikanal hills" || lowerCaseMessage === "yelagiri hills" ||
        lowerCaseMessage === "kolli hills") {
      // Return directly from the function to avoid further processing
      return getHeritageResponse(lowerCaseMessage, "en", []);
    }

    // Check for location keywords
    if (word === "ooty" || word === "madurai" || word === "chennai" ||
        word === "nilgiris" || word === "dindigul" || word === "tenkasi" ||
        word === "dharmapuri" || word === "kanyakumari" || word === "thanjavur" ||
        word === "jayankondam" || word === "darasuram" || word === "karaikudi" ||
        word === "chettinad" || word === "palace" || word === "palaces" ||
        word === "monument" || word === "monuments" || word === "museum" ||
        word === "museums" || word === "temple" || word === "temples" ||
        word === "yercaud" || word === "yelagiri" || word === "kolli" ||
        word === "waterfall" || word === "waterfalls" || word === "beach" ||
        word === "beaches" || word === "kovalam" || word === "mahabalipuram" ||
        word === "rameshwaram" || word === "elliot" || word === "agaya" ||
        word === "kiliyur" || word === "thirparappu" || word === "hill" ||
        word === "hills" || word === "hill station" || word === "hill stations" ||
        word === "church" || word === "churches" || word === "basilica" ||
        word === "cathedral" || word === "santhome" || word === "velankanni" ||
        word === "st. mary's" || word === "st. thomas" || word === "christ" ||
        word === "st. francis" || word === "xavier") {

      // Find the matching place
      for (const place in touristPlaces) {
        if (place.includes(word)) {
          return touristPlaces[place];
        }
      }

      // If no direct match in touristPlaces, provide a generic response about the location
      return `${word.charAt(0).toUpperCase() + word.slice(1)} is a notable location or attraction in Tamil Nadu. You can ask about specific places to visit in ${word} or ask about "${word} in Tamil Nadu" for more information.`;
    }

    // Try fuzzy matching for other words
    const match = findBestMatch(word, placeNames);
    if (match) {
      return touristPlaces[match];
    }
  }

  // Check for specific question types
  const questionType = identifyQuestionType(lowerCaseMessage);
  const siteName = identifySite(lowerCaseMessage);

  // If we identified both a question type and a site, provide a targeted response
  if (questionType && siteName && window.heritageSiteInfo[siteName]) {
    const site = window.heritageSiteInfo[siteName];

    switch(questionType) {
      case "what":
        return `${site.name} is ${site.description}`;
      case "where":
        return `${site.name} is located in ${site.location}, Tamil Nadu.`;
      case "when":
        return `${site.name} was built during the ${site.period}.`;
      case "who":
        return `${site.name} was built during the ${site.period}. ${site.description.split('.')[0]}.`;
      case "how":
        return `${site.significance}`;
      case "why":
        return `${site.name} is significant because ${site.significance}`;
      default:
        return `${site.name} (${site.location}): ${site.description}`;
    }
  }

  // First check in our heritage site database for general queries
  for (const siteKey in window.heritageSiteInfo) {
    if (lowerCaseMessage.includes(siteKey)) {
      const site = window.heritageSiteInfo[siteKey];
      return `${site.name} (${site.location}): ${site.description}`;
    }
  }

  // Default response if no match found
  return "I'm sorry, I don't have specific information about that. You can ask me about major cities and heritage sites in Tamil Nadu like Chennai, Thanjavur, Madurai, or Mahabalipuram.";
}

/**
 * Identify the type of question being asked
 * @param {string} message - The user's message
 * @return {string|null} - The identified question type or null
 */
function identifyQuestionType(message) {
  for (const type in questionTypes) {
    if (questionTypes[type].some(keyword => message.includes(keyword))) {
      return type;
    }
  }
  return null;
}

/**
 * Identify which heritage site is being asked about
 * @param {string} message - The user's message
 * @return {string|null} - The identified site key or null
 */
function identifySite(message) {
  // First check direct matches with site keys
  for (const siteKey in window.heritageSiteInfo) {
    if (message.includes(siteKey)) {
      return siteKey;
    }
  }

  // Check for specific church names with more flexible matching
  const churchNames = [
    "santhome basilica", "san thome basilica", "san thome cathedral", "santhome cathedral",
    "st. mary's church", "st marys church", "st mary church", "fort st george church",
    "velankanni church", "velankanni basilica", "our lady of good health",
    "st. thomas mount church", "st thomas mount", "st thomas mount church",
    "christ the king church", "christ king church", "pudukkottai church",
    "st. francis xavier church", "st francis xavier", "kottar church"
  ];

  for (const churchName of churchNames) {
    if (message.includes(churchName)) {
      // Map to the correct key in heritageSiteInfo
      if (churchName.includes("santhome") || churchName.includes("san thome")) {
        return "santhome basilica";
      } else if (churchName.includes("mary")) {
        return "st. mary's church";
      } else if (churchName.includes("velankanni") || churchName.includes("lady of good health")) {
        return "velankanni church";
      } else if (churchName.includes("thomas mount")) {
        return "st. thomas mount church";
      } else if (churchName.includes("christ") || churchName.includes("king")) {
        return "christ the king church";
      } else if (churchName.includes("francis") || churchName.includes("xavier") || churchName.includes("kottar")) {
        return "st. francis xavier church";
      }
    }
  }

  // Then check with keywords
  for (const siteKey in siteKeywords) {
    if (siteKeywords[siteKey].some(keyword => message.includes(keyword))) {
      // Map the keyword back to the actual site key in heritageSiteInfo
      for (const actualSiteKey in window.heritageSiteInfo) {
        if (actualSiteKey.includes(siteKey) || siteKey.includes(actualSiteKey)) {
          return actualSiteKey;
        }
      }
      return null;
    }
  }

  return null;
}

/**
 * Handle user input and display chatbot response
 * @param {string} userMessage - The message from the user
 */
function handleUserInput(userMessage) {
  const chatWindow = document.getElementById("liveChatMessages");

  // Display user message
  const userMessageElement = document.createElement("div");
  userMessageElement.className = "user-message";
  userMessageElement.textContent = userMessage;
  chatWindow.appendChild(userMessageElement);

  // Scroll to the bottom of the chat window
  chatWindow.scrollTop = chatWindow.scrollHeight;

  // Get initial response
  let response = getHeritageResponse(userMessage, "en", []);

  // Display initial response
  const botMessageElement = document.createElement("div");
  botMessageElement.className = "bot-message";
  botMessageElement.textContent = response;
  chatWindow.appendChild(botMessageElement);

  // Scroll to the bottom of the chat window
  chatWindow.scrollTop = chatWindow.scrollHeight;

  // If we want to use OpenAI for enhanced responses, we can do that separately
  if (shouldUseOpenAI(userMessage.toLowerCase())) {
    // Show loading indicator
    botMessageElement.textContent += " (Fetching more details...)";

    // Make API call
    const prompt = `Provide a brief, informative response about the following Tamil Nadu heritage query: "${userMessage}"`;
    fetchOpenAIResponse(prompt)
      .then(aiResponse => {
        // Update the bot message with AI response
        botMessageElement.textContent = aiResponse;
        // Scroll to the bottom again after update
        chatWindow.scrollTop = chatWindow.scrollHeight;
      })
      .catch(error => {
        console.error("Error fetching OpenAI response:", error);
        // Keep the original response if API call fails
      });
  }
}

/**
 * Determine if we should use OpenAI for this query
 */
function shouldUseOpenAI(message) {
  // Check if message contains any general heritage keywords
  return generalHeritageKeywords.some(keyword => message.includes(keyword));
}

/**
 * Initialize the chatbot functionality
 */
function initializeChatbot() {
  const liveChatInput = document.getElementById("liveChatInput");
  const liveChatSendButton = document.getElementById("liveChatSendButton");

  // Add event listener for the send button
  liveChatSendButton.addEventListener("click", () => {
    const userMessage = liveChatInput.value.trim();
    if (userMessage) {
      handleUserInput(userMessage);
      liveChatInput.value = ""; // Clear the input field
    }
  });

  // Add event listener for pressing Enter in the input field
  liveChatInput.addEventListener("keydown", (event) => {
    if (event.key === "Enter") {
      const userMessage = liveChatInput.value.trim();
      if (userMessage) {
        handleUserInput(userMessage);
        liveChatInput.value = ""; // Clear the input field
      }
    }
  });
}

// Initialize the chatbot when the DOM is fully loaded
document.addEventListener("DOMContentLoaded", initializeChatbot);
