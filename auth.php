<?php
require_once 'config.php';

header('Content-Type: application/json');

// Handle different authentication actions
$action = $_POST['action'] ?? $_GET['action'] ?? '';

switch ($action) {
    case 'signup':
        handleSignup();
        break;
    case 'login':
        handleLogin();
        break;
    case 'logout':
        handleLogout();
        break;
    case 'check_session':
        checkSession();
        break;
    case 'check_username':
        checkUsername();
        break;
    case 'check_email':
        checkEmail();
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
}

function handleSignup() {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        echo json_encode(['success' => false, 'message' => 'Invalid security token']);
        return;
    }
    
    // Get and validate input
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $fullName = trim($_POST['full_name'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $dateOfBirth = $_POST['date_of_birth'] ?? '';
    $gender = $_POST['gender'] ?? '';
    
    // Validation
    $errors = [];
    
    if (empty($username) || strlen($username) < 3) {
        $errors[] = 'Username must be at least 3 characters long';
    }
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email address';
    }
    
    if (strlen($password) < 8) {
        $errors[] = 'Password must be at least 8 characters long';
    }
    
    if ($password !== $confirmPassword) {
        $errors[] = 'Passwords do not match';
    }
    
    if (empty($fullName)) {
        $errors[] = 'Full name is required';
    }
    
    if (!empty($phone) && !preg_match('/^[0-9+\-\s()]{10,15}$/', $phone)) {
        $errors[] = 'Invalid phone number format';
    }
    
    if ($errors) {
        echo json_encode(['success' => false, 'message' => implode(', ', $errors)]);
        return;
    }
    
    $pdo = getDBConnection();
    if (!$pdo) {
        echo json_encode(['success' => false, 'message' => 'Database connection failed']);
        return;
    }
    
    try {
        // Check if username or email already exists
        $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
        $stmt->execute([$username, $email]);
        
        if ($stmt->fetch()) {
            echo json_encode(['success' => false, 'message' => 'Username or email already exists']);
            return;
        }
        
        // Hash password
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        // Insert new user
        $stmt = $pdo->prepare("
            INSERT INTO users (username, email, password, full_name, phone, date_of_birth, gender) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $username, 
            $email, 
            $hashedPassword, 
            $fullName, 
            $phone ?: null, 
            $dateOfBirth ?: null, 
            $gender ?: null
        ]);
        
        $userId = $pdo->lastInsertId();
        
        // Create session
        createUserSession($userId, $username, $email, $fullName);
        
        echo json_encode([
            'success' => true, 
            'message' => 'Account created successfully',
            'user' => [
                'id' => $userId,
                'username' => $username,
                'email' => $email,
                'full_name' => $fullName
            ]
        ]);
        
    } catch (PDOException $e) {
        error_log("Signup error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Registration failed. Please try again.']);
    }
}

function handleLogin() {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        echo json_encode(['success' => false, 'message' => 'Invalid security token']);
        return;
    }
    
    $usernameOrEmail = trim($_POST['username_email'] ?? '');
    $password = $_POST['password'] ?? '';
    $rememberMe = isset($_POST['remember_me']);
    
    if (empty($usernameOrEmail) || empty($password)) {
        echo json_encode(['success' => false, 'message' => 'Username/email and password are required']);
        return;
    }
    
    $pdo = getDBConnection();
    if (!$pdo) {
        echo json_encode(['success' => false, 'message' => 'Database connection failed']);
        return;
    }
    
    try {
        // Find user by username or email
        $stmt = $pdo->prepare("SELECT id, username, email, password, full_name, is_active FROM users WHERE (username = ? OR email = ?) AND is_active = 1");
        $stmt->execute([$usernameOrEmail, $usernameOrEmail]);
        $user = $stmt->fetch();
        
        if (!$user || !password_verify($password, $user['password'])) {
            echo json_encode(['success' => false, 'message' => 'Invalid credentials']);
            return;
        }
        
        // Create session
        createUserSession($user['id'], $user['username'], $user['email'], $user['full_name'], $rememberMe);
        
        echo json_encode([
            'success' => true, 
            'message' => 'Login successful',
            'user' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'email' => $user['email'],
                'full_name' => $user['full_name']
            ]
        ]);
        
    } catch (PDOException $e) {
        error_log("Login error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Login failed. Please try again.']);
    }
}

function createUserSession($userId, $username, $email, $fullName, $rememberMe = false) {
    $_SESSION['user_id'] = $userId;
    $_SESSION['username'] = $username;
    $_SESSION['email'] = $email;
    $_SESSION['full_name'] = $fullName;
    $_SESSION['logged_in'] = true;
    
    // Set session timeout
    $sessionTimeout = $rememberMe ? (30 * 24 * 60 * 60) : (24 * 60 * 60); // 30 days or 1 day
    $_SESSION['session_timeout'] = time() + $sessionTimeout;
    
    // Generate session token for database
    $sessionToken = bin2hex(random_bytes(32));
    $expiresAt = date('Y-m-d H:i:s', time() + $sessionTimeout);
    
    $pdo = getDBConnection();
    if ($pdo) {
        try {
            // Clean up old sessions
            $stmt = $pdo->prepare("DELETE FROM user_sessions WHERE user_id = ? OR expires_at < NOW()");
            $stmt->execute([$userId]);
            
            // Insert new session
            $stmt = $pdo->prepare("INSERT INTO user_sessions (user_id, session_token, expires_at) VALUES (?, ?, ?)");
            $stmt->execute([$userId, $sessionToken, $expiresAt]);
            
            $_SESSION['session_token'] = $sessionToken;
        } catch (PDOException $e) {
            error_log("Session creation error: " . $e->getMessage());
        }
    }
}

function handleLogout() {
    $sessionToken = $_SESSION['session_token'] ?? '';
    
    // Remove session from database
    if ($sessionToken) {
        $pdo = getDBConnection();
        if ($pdo) {
            try {
                $stmt = $pdo->prepare("DELETE FROM user_sessions WHERE session_token = ?");
                $stmt->execute([$sessionToken]);
            } catch (PDOException $e) {
                error_log("Logout error: " . $e->getMessage());
            }
        }
    }
    
    // Destroy session
    session_destroy();
    
    echo json_encode(['success' => true, 'message' => 'Logged out successfully']);
}

function checkSession() {
    if (isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true) {
        // Check session timeout
        if (isset($_SESSION['session_timeout']) && time() > $_SESSION['session_timeout']) {
            handleLogout();
            return;
        }
        
        echo json_encode([
            'success' => true,
            'logged_in' => true,
            'user' => [
                'id' => $_SESSION['user_id'],
                'username' => $_SESSION['username'],
                'email' => $_SESSION['email'],
                'full_name' => $_SESSION['full_name']
            ]
        ]);
    } else {
        echo json_encode(['success' => true, 'logged_in' => false]);
    }
}

function checkUsername() {
    $username = trim($_GET['username'] ?? '');
    
    if (empty($username)) {
        echo json_encode(['available' => false, 'message' => 'Username is required']);
        return;
    }
    
    $pdo = getDBConnection();
    if (!$pdo) {
        echo json_encode(['available' => false, 'message' => 'Database connection failed']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
        $stmt->execute([$username]);
        
        $available = !$stmt->fetch();
        echo json_encode(['available' => $available]);
        
    } catch (PDOException $e) {
        error_log("Username check error: " . $e->getMessage());
        echo json_encode(['available' => false, 'message' => 'Check failed']);
    }
}

function checkEmail() {
    $email = trim($_GET['email'] ?? '');
    
    if (empty($email)) {
        echo json_encode(['available' => false, 'message' => 'Email is required']);
        return;
    }
    
    $pdo = getDBConnection();
    if (!$pdo) {
        echo json_encode(['available' => false, 'message' => 'Database connection failed']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$email]);
        
        $available = !$stmt->fetch();
        echo json_encode(['available' => $available]);
        
    } catch (PDOException $e) {
        error_log("Email check error: " . $e->getMessage());
        echo json_encode(['available' => false, 'message' => 'Check failed']);
    }
}
?>
