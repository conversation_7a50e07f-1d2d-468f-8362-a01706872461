<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Shore Temple - Heritage Explorer</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
  <style>
    :root {
      --primary-color: #1a237e;
      --secondary-color: #457b9d;
      --accent-color: #e63946;
      --light-color: #f1faee;
      --dark-color: #1d3557;
      --success-color: #2e7d32;
      --warning-color: #ff9800;
      --danger-color: #c62828;
      --gray-100: #f8f9fa;
      --gray-200: #e9ecef;
      --gray-300: #dee2e6;
      --gray-400: #ced4da;
      --gray-500: #adb5bd;
      --gray-600: #6c757d;
      --gray-700: #495057;
      --gray-800: #343a40;
      --gray-900: #212529;
      --border-radius: 0.5rem;
      --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
      --transition: all 0.3s ease-in-out;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: var(--gray-800);
      background-color: var(--gray-100);
    }

    header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      text-align: center;
    }

    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      background-color: white;
      box-shadow: var(--box-shadow);
    }

    nav a {
      color: var(--gray-700);
      text-decoration: none;
      margin: 0 1rem;
      font-weight: 500;
      transition: var(--transition);
    }

    nav a:hover {
      color: var(--primary-color);
    }

    main {
      max-width: 1200px;
      margin: 2rem auto;
      padding: 0 1rem;
    }

    .site-details {
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    @media (min-width: 768px) {
      .site-details {
        grid-template-columns: 1fr 1fr;
      }
    }

    .site-image {
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
    }

    .site-image img {
      width: 100%;
      height: auto;
      display: block;
    }

    .site-info h1 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .site-info p {
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }

    .site-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .meta-item {
      display: flex;
      align-items: center;
      background-color: var(--gray-200);
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
    }

    .meta-item i {
      margin-right: 0.5rem;
      color: var(--secondary-color);
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      margin-top: 1.5rem;
    }

    .action-buttons button {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      background-color: var(--secondary-color);
      color: white;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
      display: flex;
      align-items: center;
    }

    .action-buttons button i {
      margin-right: 0.5rem;
    }

    .action-buttons button:hover {
      background-color: var(--primary-color);
    }

    .site-description {
      margin-bottom: 2rem;
    }

    .site-description h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .map-container {
      height: 400px;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      margin-bottom: 2rem;
    }

    .nearby-attractions {
      margin-bottom: 2rem;
    }

    .nearby-attractions h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .attractions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .attraction-card {
      background-color: white;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
    }

    .attraction-card:hover {
      transform: translateY(-5px);
    }

    .attraction-card img {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }

    .attraction-card-content {
      padding: 1rem;
    }

    .attraction-card-content h3 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    footer {
      background-color: var(--primary-color);
      color: white;
      text-align: center;
      padding: 1rem;
      margin-top: 2rem;
    }

    #backToTop {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      width: 3rem;
      height: 3rem;
      background-color: var(--primary-color);
      color: white;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
      display: none;
    }

    #backToTop:hover {
      background-color: var(--secondary-color);
    }

    /* Travel Information Section */
    .travel-info {
      margin-bottom: 2rem;
      background-color: white;
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      overflow: hidden;
    }

    .travel-info-header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .travel-info-header h2 {
      margin: 0;
      font-size: 1.5rem;
    }

    .travel-info-tabs {
      display: flex;
      background-color: var(--gray-200);
    }

    .travel-tab {
      padding: 1rem;
      cursor: pointer;
      flex: 1;
      text-align: center;
      font-weight: 500;
      transition: var(--transition);
      border-bottom: 3px solid transparent;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .travel-tab i {
      margin-right: 0.5rem;
    }

    .travel-tab.active {
      background-color: white;
      border-bottom-color: var(--accent-color);
    }

    .travel-tab:hover:not(.active) {
      background-color: var(--gray-300);
    }

    .travel-content {
      padding: 1.5rem;
    }

    .travel-panel {
      display: none;
    }

    .travel-panel.active {
      display: block;
    }

    /* Weather Panel */
    .weather-info {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      align-items: center;
    }

    .weather-current {
      display: flex;
      align-items: center;
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      flex: 1;
      min-width: 250px;
    }

    .weather-icon {
      font-size: 3rem;
      margin-right: 1rem;
      color: var(--secondary-color);
    }

    .weather-details h3 {
      margin: 0;
      color: var(--primary-color);
    }

    .weather-temp {
      font-size: 2rem;
      font-weight: bold;
    }

    .weather-desc {
      color: var(--gray-700);
    }

    .weather-forecast {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      flex: 2;
      min-width: 300px;
    }

    .forecast-day {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      text-align: center;
      flex: 1;
      min-width: 100px;
    }

    .forecast-day h4 {
      margin: 0 0 0.5rem 0;
      color: var(--primary-color);
    }

    .forecast-icon {
      font-size: 1.5rem;
      margin: 0.5rem 0;
      color: var(--secondary-color);
    }

    /* Transportation Panel */
    .transport-options {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .transport-option {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
      display: flex;
      flex-direction: column;
    }

    .transport-option h3 {
      display: flex;
      align-items: center;
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .transport-option h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .transport-option p {
      margin-bottom: 0.5rem;
    }

    .transport-option .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    /* Hours & Tickets Panel */
    .hours-container {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .opening-hours, .ticket-info {
      flex: 1;
      min-width: 250px;
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .opening-hours h3, .ticket-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .opening-hours h3 i, .ticket-info h3 i {
      margin-right: 0.5rem;
    }

    .hours-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .hours-list li {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .hours-list li:last-child {
      border-bottom: none;
    }

    .day {
      font-weight: 500;
    }

    .ticket-type {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .ticket-type:last-child {
      border-bottom: none;
    }

    .ticket-price {
      font-weight: bold;
    }

    /* Amenities Panel */
    .amenities-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .amenity-card {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .amenity-card h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .amenity-card h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .amenity-card p {
      margin-bottom: 0.5rem;
    }

    .amenity-card .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    .amenity-card .rating {
      color: var(--warning-color);
      margin-bottom: 0.5rem;
    }

    .weather-tips h3, .local-transport h3, .best-time h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .monument-features {
      margin-top: 1.5rem;
    }

    .monument-features h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .feature-item {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      margin-bottom: 1rem;
    }

    .feature-item h4 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
    }

    .feature-item h4 i {
      margin-right: 0.5rem;
    }

    .feature-details {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 0.5rem;
    }

    .feature-detail {
      display: flex;
      align-items: center;
      font-size: 0.9rem;
      color: var(--gray-700);
    }

    .feature-detail i {
      margin-right: 0.25rem;
      font-size: 0.8rem;
    }

    .photography-tips {
      margin-top: 1.5rem;
    }

    .photography-tips h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .photo-tip {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      margin-bottom: 1rem;
    }

    .photo-tip h4 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
    }

    .photo-tip h4 i {
      margin-right: 0.5rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>Heritage Explorer</h1>
    <p>Discover Tamil Nadu's rich heritage and culture</p>
  </header>

  <nav>
    <div>
      <a href="website.html"><i class="fas fa-home"></i> Home</a>
      <a href="#"><i class="fas fa-map-marked-alt"></i> Explore</a>
      <a href="#"><i class="fas fa-info-circle"></i> About</a>
    </div>
    <div>
      <a href="#" id="toggleLang">தமிழ்</a>
    </div>
  </nav>

  <main>
    <div class="site-details">
      <div class="site-image">
        <img src="https://www.holidify.com/images/cmsuploads/compressed/Shore_Temple_20190619141119.jpg" alt="Shore Temple">
      </div>
      <div class="site-info">
        <h1>Shore Temple</h1>
        <div class="site-meta">
          <div class="meta-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>Mahabalipuram, Tamil Nadu</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-monument"></i>
            <span>Monument</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-calendar-alt"></i>
            <span>Built in 8th century CE</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-award"></i>
            <span>UNESCO World Heritage Site</span>
          </div>
        </div>
        <p>The Shore Temple is an ancient granite temple complex standing on the shores of the Bay of Bengal in Mahabalipuram. Built during the Pallava dynasty in the 8th century, it's one of the oldest stone temples in South India and a UNESCO World Heritage Site, showcasing exquisite Dravidian architecture and stone carvings.</p>
        <div class="action-buttons">
          <button id="playAudioButton"><i class="fas fa-volume-up"></i> Listen</button>
          <button id="favoriteButton"><i class="far fa-heart"></i> Favorite</button>
          <button id="shareButton"><i class="fas fa-share-alt"></i> Share</button>
        </div>
      </div>
    </div>

    <div class="site-description">
      <h2>About Shore Temple</h2>
      <p>The Shore Temple is a magnificent ancient structure located on the shores of the Bay of Bengal in Mahabalipuram (also known as Mamallapuram), Tamil Nadu. Built during the reign of the Pallava dynasty king Narasimhavarman II (also known as Rajasimha) in the 8th century CE (around 700-728 CE), it is one of the oldest structural stone temples of South India and has been declared a UNESCO World Heritage Site as part of the "Group of Monuments at Mahabalipuram."</p>
      <p>The temple complex consists of three shrines, two dedicated to Lord Shiva and one to Lord Vishnu. The main shrine faces the east, towards the sea, and contains a Lingam (symbolic representation of Lord Shiva). The second Shiva shrine faces west, while the Vishnu shrine is positioned between them. The temple is renowned for its architectural style, which is a perfect blend of Dravidian and Nagara styles, showcasing the artistic excellence of the Pallava dynasty.</p>
      <p>What makes the Shore Temple particularly remarkable is its location and construction. Built directly on the shoreline, it has withstood the ravages of sea and wind for over 1,300 years. The temple is carved from a single large piece of granite, a testament to the exceptional skill of the artisans of that era. The complex is adorned with intricate carvings and sculptures, including a series of Nandi (bull) statues, the vehicle of Lord Shiva, which form a protective wall around the temple.</p>
      <p>The Shore Temple is not just an architectural marvel but also holds significant historical and cultural importance. It is believed to be the final of a series of seven temples, six of which are thought to have been submerged in the sea. This theory gained credence when the 2004 Indian Ocean tsunami temporarily revealed the outlines of what appeared to be ancient structures on the shore. Today, the Shore Temple stands as a symbol of the rich cultural heritage of Tamil Nadu and continues to attract thousands of visitors from around the world who come to admire its timeless beauty and historical significance.</p>
    </div>

    <div class="map-container">
      <!-- Map will be embedded here -->
      <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3890.8123456789!2d80.27!3d12.616!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3a53abe7eafaaaab%3A0xc5e3f3d4a3ce3ee7!2sShore%20Temple!5e0!3m2!1sen!2sin!4v1652345678901!5m2!1sen!2sin" width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy" title="Map of Shore Temple, Mahabalipuram"></iframe>
    </div>

    <!-- Travel Information Section -->
    <div class="travel-info">
      <div class="travel-info-header">
        <h2>Practical Travel Information</h2>
        <div class="last-updated">Last updated: <span id="lastUpdated">Today</span></div>
      </div>

      <div class="travel-info-tabs">
        <div class="travel-tab active" data-tab="weather">
          <i class="fas fa-cloud-sun"></i> Weather
        </div>
        <div class="travel-tab" data-tab="transport">
          <i class="fas fa-bus"></i> Transportation
        </div>
        <div class="travel-tab" data-tab="hours">
          <i class="far fa-clock"></i> Hours & Tickets
        </div>
        <div class="travel-tab" data-tab="amenities">
          <i class="fas fa-concierge-bell"></i> Nearby Amenities
        </div>
      </div>

      <div class="travel-content">
        <!-- Weather Panel -->
        <div class="travel-panel active" id="weatherPanel">
          <div class="weather-info">
            <div class="weather-current">
              <div class="weather-icon">
                <i class="fas fa-sun"></i>
              </div>
              <div class="weather-details">
                <h3>Current Weather</h3>
                <div class="weather-temp">30°C</div>
                <div class="weather-desc">Sunny</div>
                <div class="weather-extra">Humidity: 75% | Wind: 12 km/h</div>
              </div>
            </div>

            <div class="weather-forecast">
              <div class="forecast-day">
                <h4>Tomorrow</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun"></i></div>
                <div class="forecast-temp">29°C</div>
                <div class="forecast-desc">Partly Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Wed</h4>
                <div class="forecast-icon"><i class="fas fa-cloud"></i></div>
                <div class="forecast-temp">28°C</div>
                <div class="forecast-desc">Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Thu</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-rain"></i></div>
                <div class="forecast-temp">27°C</div>
                <div class="forecast-desc">Light Rain</div>
              </div>
            </div>
          </div>

          <div class="weather-tips">
            <h3>Weather Tips</h3>
            <p>Mahabalipuram experiences a tropical climate with hot summers and moderate winters. The region has three main seasons: summer (March-June), monsoon (July-November), and winter (December-February).</p>
            <p>The best time to visit the Shore Temple is from November to February when the weather is relatively cooler with temperatures ranging from 20°C to 30°C. This period offers comfortable conditions for exploring the temple complex and other monuments in Mahabalipuram. Summer months (March-June) can be extremely hot with temperatures often exceeding 35°C, making midday visits uncomfortable.</p>
            <p>The northeast monsoon (October-December) brings rainfall to the region, which can occasionally disrupt outdoor sightseeing. However, the rain also brings a special charm to the Shore Temple, as the granite structures take on a darker hue when wet, creating dramatic contrasts for photography.</p>
            <p>Morning hours (6:00 AM - 9:00 AM) and late afternoon to evening hours (4:00 PM - 6:00 PM) are the best times to visit the temple, as the midday sun can be quite intense. The temple's stone carvings are best viewed in the early morning or late afternoon light when the sun creates interesting shadows that highlight the intricate details. Always carry water, wear light cotton clothing, and use sun protection when visiting. A hat or umbrella is recommended for sun protection.</p>
          </div>
        </div>

        <!-- Transportation Panel -->
        <div class="travel-panel" id="transportPanel">
          <div class="transport-options">
            <div class="transport-option">
              <h3><i class="fas fa-plane"></i> By Air</h3>
              <p>Chennai International Airport is the nearest airport.</p>
              <p class="distance">55 km from Mahabalipuram (approx. 1.5 hours by car)</p>
              <p>Regular flights from all major Indian cities and international destinations.</p>
              <p>Pre-paid taxis available from the airport (₹1,500-2,000).</p>
              <p>App-based cabs like Ola and Uber operate from the airport.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-train"></i> By Train</h3>
              <p>Chengalpattu Railway Station is the nearest major station.</p>
              <p class="distance">29 km from Mahabalipuram (approx. 45 minutes by car)</p>
              <p>Connected to Chennai and other major cities by regular trains.</p>
              <p>Auto-rickshaws and taxis available from the station (₹500-700).</p>
              <p>Advance booking recommended for train tickets.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-bus"></i> By Bus</h3>
              <p>Well-connected by government and private buses from Chennai.</p>
              <p>Regular services from Chennai CMBT (every 30 minutes).</p>
              <p>Journey time: 2-2.5 hours depending on traffic.</p>
              <p>Fare: ₹70-150 depending on the bus type.</p>
              <p>AC and non-AC options available.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-car"></i> By Car</h3>
              <p>Well-connected by the East Coast Road (ECR) from Chennai.</p>
              <p>Distance from Chennai: 55 km (approx. 1.5 hours)</p>
              <p>The scenic coastal drive along ECR is a highlight in itself.</p>
              <p>Parking available near the temple complex (₹50 for cars).</p>
              <p>Self-drive car rentals available in Chennai.</p>
            </div>
          </div>

          <div class="local-transport">
            <h3>Local Transportation</h3>
            <p>Mahabalipuram is a compact town, and most of the major monuments, including the Shore Temple, are within walking distance of each other. The town center to the Shore Temple is about a 10-15 minute walk. For those who prefer not to walk, auto-rickshaws are readily available for short distances within the town (₹50-100 per trip).</p>
            <p>Bicycle rentals are a popular option for exploring Mahabalipuram at a leisurely pace. Several shops near the bus stand offer bicycles for rent (₹100-150 per day). This is an eco-friendly way to visit all the monuments and enjoy the coastal scenery.</p>
            <p>For visiting attractions that are further away, such as the Tiger Cave (5 km north) or Crocodile Bank (15 km north), hiring an auto-rickshaw for a half-day or full-day is recommended. Rates should be negotiated in advance (₹500-800 for half-day). Some hotels also arrange guided tours of all the monuments in Mahabalipuram, which is a convenient option for first-time visitors.</p>
          </div>
        </div>

        <!-- Hours & Tickets Panel -->
        <div class="travel-panel" id="hoursPanel">
          <div class="hours-container">
            <div class="opening-hours">
              <h3><i class="far fa-clock"></i> Opening Hours</h3>
              <ul class="hours-list">
                <li><span class="day">All days:</span> <span class="time">6:00 AM - 6:00 PM</span></li>
                <li><span class="day">Light & Sound Show:</span> <span class="time">6:30 PM - 7:30 PM (English)</span></li>
                <li><span class="day">Light & Sound Show:</span> <span class="time">7:45 PM - 8:45 PM (Tamil)</span></li>
              </ul>
              <p class="hours-note">The Shore Temple is open all days of the week, including public holidays. The Light & Sound Show operates daily except during heavy rainfall. It's advisable to arrive at least 30 minutes before closing time to fully appreciate the monument. The temple complex can get crowded during weekends and public holidays, so weekday visits are recommended for a more peaceful experience.</p>
            </div>

            <div class="ticket-info">
              <h3><i class="fas fa-ticket-alt"></i> Ticket Information</h3>
              <div class="ticket-type">
                <span>Indian Nationals:</span>
                <span class="ticket-price">₹40 per person</span>
              </div>
              <div class="ticket-type">
                <span>Foreign Nationals:</span>
                <span class="ticket-price">₹600 per person</span>
              </div>
              <div class="ticket-type">
                <span>Light & Sound Show:</span>
                <span class="ticket-price">₹150 per person</span>
              </div>
              <div class="ticket-type">
                <span>Still Camera Fee:</span>
                <span class="ticket-price">Free</span>
              </div>
              <div class="ticket-type">
                <span>Video Camera Fee:</span>
                <span class="ticket-price">₹25</span>
              </div>
              <p class="ticket-note">Children below 15 years enter free. A single ticket provides access to all ASI-protected monuments in Mahabalipuram, including the Shore Temple, Pancha Rathas, and Arjuna's Penance. Tickets can be purchased at the entrance or online through the ASI website. The ticket is valid for one day. Credit cards are accepted at the ticket counter.</p>
            </div>
          </div>

          <div class="monument-features">
            <h3>Key Monument Features</h3>
            <div class="feature-item">
              <h4><i class="fas fa-landmark"></i> Main Temple Complex</h4>
              <div class="feature-details">
                <span class="feature-detail"><i class="fas fa-ruler-vertical"></i> Height: Approx. 18 meters</span>
                <span class="feature-detail"><i class="fas fa-calendar-alt"></i> Built: 8th century CE</span>
              </div>
              <p>The Shore Temple complex consists of three temples: two Shiva temples facing east and west, and a small Vishnu temple between them. The main shrine faces the sea and contains a Lingam (symbolic representation of Lord Shiva). The temple is built in the Dravidian architectural style with a pyramidal structure.</p>
            </div>

            <div class="feature-item">
              <h4><i class="fas fa-chess-rook"></i> Sculptures and Carvings</h4>
              <div class="feature-details">
                <span class="feature-detail"><i class="fas fa-star"></i> Highlight: Nandi Bulls and Miniature Shrines</span>
              </div>
              <p>The temple is adorned with intricate carvings, including a series of Nandi (bull) statues that form a protective wall around the temple. The outer walls feature beautiful sculptures of deities, mythological figures, and scenes from daily life. The miniature shrines carved into the temple's base are particularly noteworthy for their detailed craftsmanship.</p>
            </div>

            <div class="feature-item">
              <h4><i class="fas fa-water"></i> Seaside Location</h4>
              <div class="feature-details">
                <span class="feature-detail"><i class="fas fa-map-marker-alt"></i> Setting: Bay of Bengal shoreline</span>
              </div>
              <p>What makes the Shore Temple unique is its location right on the shoreline of the Bay of Bengal. The temple has withstood the ravages of sea and wind for over 1,300 years. The constant exposure to salt water and sea breeze has eroded some of the finer details of the carvings, giving the temple a weathered appearance that adds to its historical charm.</p>
            </div>
          </div>

          <div class="photography-tips">
            <h3>Photography Tips</h3>
            <div class="photo-tip">
              <h4><i class="fas fa-camera"></i> Best Time for Photos</h4>
              <p>Early morning (6:00 AM - 8:00 AM) offers the best light for photography, with the rising sun casting a golden glow on the eastern face of the temple. Late afternoon (4:00 PM - 6:00 PM) is also good, with warm light illuminating the western side. Avoid midday when harsh sunlight creates strong shadows and washes out details.</p>
            </div>
            <div class="photo-tip">
              <h4><i class="fas fa-compass"></i> Best Angles</h4>
              <p>The most iconic shots of the Shore Temple are taken from the beach side, capturing the temple with the sea in the background. For a different perspective, try shooting from the northern side to capture the entire complex. Close-up shots of the carvings are best taken in the morning light when the shadows enhance the details.</p>
            </div>
            <div class="photo-tip">
              <h4><i class="fas fa-lightbulb"></i> Special Conditions</h4>
              <p>After rain, the wet granite takes on a darker hue, creating dramatic contrasts. During the full moon nights, the temple is beautifully illuminated, offering a magical atmosphere for night photography (tripod recommended). The Light & Sound Show provides colorful illumination of the temple, creating unique photo opportunities.</p>
            </div>
          </div>

          <div class="best-time">
            <h3>Best Time to Visit</h3>
            <p>The ideal time to visit the Shore Temple is from November to February when the weather is pleasant with temperatures ranging from 20°C to 30°C. This period offers comfortable conditions for exploring the temple complex and other monuments in Mahabalipuram.</p>
            <p>For the best experience, visit early in the morning (6:00 AM - 9:00 AM) when the temple is less crowded and the light is perfect for photography. Alternatively, late afternoon (4:00 PM - 6:00 PM) offers beautiful lighting conditions as the sun begins to set.</p>
            <p>If you're interested in the Light & Sound Show, plan to stay until evening. The show narrates the history of Mahabalipuram and the Pallava dynasty through colorful illumination of the temple and an engaging audio narrative. Advance booking for the show is recommended during peak tourist season (December-January).</p>
          </div>
        </div>

        <!-- Amenities Panel -->
        <div class="travel-panel" id="amenitiesPanel">
          <div class="amenities-grid">
            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Radisson Blu Resort Temple Bay</strong></p>
              <p class="distance">1.5 km from Shore Temple</p>
              <p class="rating">★★★★★ (4.5/5)</p>
              <p>Luxury beachfront resort with pool villas and cottages.</p>
              <p>Price range: ₹12,000 - ₹25,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Grande Bay Resort & Spa</strong></p>
              <p class="distance">2 km from Shore Temple</p>
              <p class="rating">★★★★☆ (4.3/5)</p>
              <p>Mid-range resort with pool and spa facilities.</p>
              <p>Price range: ₹6,000 - ₹12,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Seashore Hotel</strong></p>
              <p class="distance">0.5 km from Shore Temple</p>
              <p class="rating">★★★☆☆ (3.8/5)</p>
              <p>Budget-friendly option with basic amenities.</p>
              <p>Price range: ₹1,500 - ₹3,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Moonrakers</strong></p>
              <p class="distance">0.8 km from Shore Temple</p>
              <p class="rating">★★★★★ (4.6/5)</p>
              <p>Popular seafood restaurant with beach views.</p>
              <p>Price range: ₹1,000 - ₹1,500 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>The Wharf</strong></p>
              <p class="distance">1.2 km from Shore Temple</p>
              <p class="rating">★★★★☆ (4.2/5)</p>
              <p>Multi-cuisine restaurant with outdoor seating.</p>
              <p>Price range: ₹800 - ₹1,200 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-shopping-bag"></i> Shopping</h3>
              <p><strong>Mahabalipuram Craft Market</strong></p>
              <p class="distance">0.3 km from Shore Temple</p>
              <p class="rating">★★★★☆ (4.0/5)</p>
              <p>Local market selling stone sculptures, handicrafts, and souvenirs.</p>
              <p>Price range: ₹100 - ₹10,000 depending on the item</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-first-aid"></i> Medical Facilities</h3>
              <p><strong>Government Hospital</strong></p>
              <p class="distance">1 km from Shore Temple</p>
              <p>Basic medical facilities for minor emergencies.</p>
              <p>For serious medical issues, hospitals in Chennai (55 km) are recommended.</p>
              <p>Emergency contact: 108</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-info-circle"></i> Tourist Information</h3>
              <p><strong>Tamil Nadu Tourism Office</strong></p>
              <p class="distance">0.5 km from Shore Temple</p>
              <p>Provides maps, brochures, and guided tour information.</p>
              <p>Languages: Tamil, English, Hindi</p>
              <p>Contact: +91 44 2744 2232</p>
            </div>
          </div>

          <div class="best-time">
            <h3>Visitor Tips</h3>
            <p>Wear comfortable footwear as you'll be walking on stone surfaces and possibly sand. The area around the Shore Temple can get hot, so carry a water bottle, wear a hat, and apply sunscreen. Photography is allowed in most areas, but flash photography is prohibited inside the temple to preserve the ancient carvings.</p>
            <p>Consider hiring a local guide (available at the entrance for ₹300-500) to fully appreciate the historical and architectural significance of the monument. Guides are knowledgeable about the symbolism of the carvings and can provide interesting insights that aren't mentioned in guidebooks.</p>
            <p>Combine your visit to the Shore Temple with other nearby monuments like Arjuna's Penance, Pancha Rathas, and Krishna's Butter Ball, all of which are covered under the same ticket. A full day is ideal to explore all the monuments in Mahabalipuram at a leisurely pace.</p>
            <p>Mobile network coverage is good in Mahabalipuram, with all major providers offering 4G services. Most hotels and many cafes offer Wi-Fi. ATMs are available in the town center, but it's advisable to carry cash for small purchases from local vendors. Credit cards are accepted at most hotels and larger restaurants.</p>
          </div>
        </div>
      </div>
    </div>

    <div class="nearby-attractions">
      <h2>Nearby Attractions</h2>
      <div class="attractions-grid">
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Arjunas_Penance_20190212182003.jpg" alt="Arjuna's Penance">
          <div class="attraction-card-content">
            <h3>Arjuna's Penance</h3>
            <p>A massive open-air bas-relief monolith depicting scenes from Hindu mythology, including the story of Arjuna's penance to obtain Lord Shiva's weapon.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Five_Rathas_20190212182004.jpg" alt="Pancha Rathas">
          <div class="attraction-card-content">
            <h3>Pancha Rathas</h3>
            <p>Five monolithic rock-cut temples in the shape of chariots, each carved from a single stone and named after the Pandavas and Draupadi from the Mahabharata.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Krishna_Butter_Ball_20190212182005.jpg" alt="Krishna's Butter Ball">
          <div class="attraction-card-content">
            <h3>Krishna's Butter Ball</h3>
            <p>A giant natural rock boulder perched on a slope, seemingly defying gravity for centuries and named after the Hindu god Krishna's love for butter.</p>
          </div>
        </div>
      </div>
    </div>
  </main>

  <footer>
    <p>&copy; 2025 Heritage Explorer | Explore Tamil Nadu</p>
  </footer>

  <div id="backToTop">
    <i class="fas fa-arrow-up"></i>
  </div>

  <script>
    // Back to Top Button
    const backToTop = document.getElementById("backToTop");
    window.addEventListener("scroll", () => {
      if (window.scrollY > 300) {
        backToTop.style.display = "flex";
      } else {
        backToTop.style.display = "none";
      }
    });
    backToTop.addEventListener("click", () => {
      window.scrollTo({ top: 0, behavior: "smooth" });
    });

    // Audio Playback
    document.getElementById("playAudioButton").addEventListener("click", () => {
      const description = document.querySelector(".site-info p").textContent;
      const utterance = new SpeechSynthesisUtterance(description);
      utterance.lang = "en-US";
      speechSynthesis.speak(utterance);
    });

    // Favorite Button
    document.getElementById("favoriteButton").addEventListener("click", function() {
      const icon = this.querySelector("i");
      if (icon.classList.contains("far")) {
        icon.classList.remove("far");
        icon.classList.add("fas");
        alert("Added to favorites!");
      } else {
        icon.classList.remove("fas");
        icon.classList.add("far");
        alert("Removed from favorites!");
      }
    });

    // Share Button
    document.getElementById("shareButton").addEventListener("click", () => {
      const shareData = {
        title: "Shore Temple - Heritage Explorer",
        text: "Check out this ancient temple by the sea in Tamil Nadu!",
        url: window.location.href
      };

      if (navigator.share) {
        navigator.share(shareData)
          .then(() => console.log("Shared successfully"))
          .catch((error) => console.error("Error sharing:", error));
      } else {
        navigator.clipboard.writeText(shareData.url)
          .then(() => alert("URL copied to clipboard"))
          .catch((err) => console.error("Could not copy text: ", err));
      }
    });

    // Initialize the travel info tabs
    document.addEventListener("DOMContentLoaded", function() {
      // Set the last updated date
      const today = new Date();
      document.getElementById("lastUpdated").textContent = today.toLocaleDateString();

      // Tab functionality
      const tabs = document.querySelectorAll(".travel-tab");
      tabs.forEach(tab => {
        tab.addEventListener("click", function() {
          // Remove active class from all tabs
          tabs.forEach(t => t.classList.remove("active"));

          // Add active class to clicked tab
          this.classList.add("active");

          // Hide all panels
          const panels = document.querySelectorAll(".travel-panel");
          panels.forEach(panel => panel.classList.remove("active"));

          // Show the corresponding panel
          const tabId = this.getAttribute("data-tab");
          document.getElementById(tabId + "Panel").classList.add("active");
        });
      });

      // Fetch weather data (mock)
      fetchWeatherData();
    });

    // Weather API Integration (Mock)
    function fetchWeatherData() {
      // Coordinates for Mahabalipuram
      const lat = 12.616;
      const lon = 80.27;

      // Mock weather data
      const mockWeatherData = {
        current: {
          temp: 30,
          humidity: 75,
          wind_speed: 12,
          weather: [{ main: "Clear", description: "Sunny", icon: "01d" }]
        },
        daily: [
          {
            dt: Date.now() + 86400000, // Tomorrow
            temp: { day: 29 },
            weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
          },
          {
            dt: Date.now() + 172800000, // Day after tomorrow
            temp: { day: 28 },
            weather: [{ main: "Clouds", description: "Cloudy", icon: "03d" }]
          },
          {
            dt: Date.now() + 259200000, // 3 days from now
            temp: { day: 27 },
            weather: [{ main: "Rain", description: "Light Rain", icon: "10d" }]
          }
        ]
      };

      updateWeatherUI(mockWeatherData);
    }

    function updateWeatherUI(data) {
      // Update current weather
      document.querySelector(".weather-temp").textContent = `${data.current.temp}°C`;
      document.querySelector(".weather-desc").textContent = data.current.weather[0].description;
      document.querySelector(".weather-extra").textContent = `Humidity: ${data.current.humidity}% | Wind: ${data.current.wind_speed} km/h`;

      // Update weather icon
      const weatherIconElement = document.querySelector(".weather-icon i");
      updateWeatherIcon(weatherIconElement, data.current.weather[0].main);

      // Update forecast
      const forecastDays = document.querySelectorAll(".forecast-day");
      data.daily.forEach((day, index) => {
        if (index < forecastDays.length) {
          const dayElement = forecastDays[index];
          const date = new Date(day.dt);
          const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });

          dayElement.querySelector("h4").textContent = index === 0 ? "Tomorrow" : dayName;
          dayElement.querySelector(".forecast-temp").textContent = `${day.temp.day}°C`;
          dayElement.querySelector(".forecast-desc").textContent = day.weather[0].description;

          // Update forecast icon
          const iconElement = dayElement.querySelector(".forecast-icon i");
          updateWeatherIcon(iconElement, day.weather[0].main);
        }
      });
    }

    function updateWeatherIcon(iconElement, weatherType) {
      // Remove all existing weather classes
      iconElement.className = "";

      // Add the appropriate weather icon class
      switch (weatherType) {
        case "Clear":
          iconElement.classList.add("fas", "fa-sun");
          break;
        case "Clouds":
          iconElement.classList.add("fas", "fa-cloud");
          break;
        case "Rain":
        case "Drizzle":
          iconElement.classList.add("fas", "fa-cloud-rain");
          break;
        case "Thunderstorm":
          iconElement.classList.add("fas", "fa-bolt");
          break;
        case "Snow":
          iconElement.classList.add("fas", "fa-snowflake");
          break;
        case "Mist":
        case "Fog":
        case "Haze":
          iconElement.classList.add("fas", "fa-smog");
          break;
        default:
          iconElement.classList.add("fas", "fa-cloud-sun");
      }
    }
  </script>

  <!-- Language Toggle Script -->
  <script src="language-toggle.js"></script>
</body>
</html>
