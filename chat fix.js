/**
 * Heritage Chatbot - Enhanced functionality for Tamil Nadu cultural heritage information
 * This module provides detailed responses about Tamil Nadu's cultural heritage sites
 */

// Define question types for better response targeting
const questionTypes = {
  "what": ["what is", "what's", "tell me about", "describe", "information about", "details about", "facts about"],
  "where": ["where is", "where's", "location of", "situated", "located"],
  "when": ["when was", "when is", "history of", "built", "constructed", "established"],
  "who": ["who built", "who constructed", "who designed", "who created", "built by", "designed by"],
  "how": ["how was", "how is", "how to", "how can", "how do"],
  "why": ["why is", "why was", "why do", "reason for", "significance of", "importance of"]
};

// Define site keywords for better site identification
const siteKeywords = {
  "brihadeeswarar": ["big temple", "thanjavur temple", "raja raja chola", "brihadeeswara"],
  "meenakshi": ["madurai temple", "meenakshi amman"],
  "shore temple": ["mahabalipuram", "mamallapuram", "sea shore temple"],
  "ram<PERSON><PERSON><PERSON><PERSON>": ["rameswaram temple", "ramanathaswamy"]
};

// Define general heritage keywords
const generalHeritageKeywords = [
  "heritage", "culture", "history", "architecture", "temple", "monument", 
  "unesco", "world heritage", "historical", "ancient", "traditional"
];

// Heritage sites database with detailed information
// Make it accessible globally so it can be used by other scripts
window.heritageSiteInfo = {
  "brihadeeswarar temple": {
    name: "Brihadeeswarar Temple",
    location: "Thanjavur",
    period: "Chola dynasty (11th century)",
    description: "The Brihadeeswarar Temple, also known as the 'Big Temple,' is a UNESCO World Heritage Site built by Raja Raja Chola I. It features a 216-foot tall vimana (tower) and is one of the largest temples in India. The temple is dedicated to Lord Shiva and is renowned for its architectural grandeur and intricate sculptures.",
    significance: "It represents the pinnacle of Chola architecture and is famous for its massive Nandi statue carved from a single stone. The temple's dome is made from a single granite block weighing approximately 80 tons.",
    visitingInfo: "Open daily from 6:00 AM to 8:30 PM. The best time to visit is during the morning or evening to avoid the heat.",
    facts: [
      "The shadow of the temple's tower never falls on the ground at noon.",
      "The temple was built in just 7 years, which was a remarkable achievement for that time.",
      "The temple has been functioning continuously for over 1000 years.",
      "It was the tallest temple in India when it was built."
    ]
  },
  "meenakshi amman temple": {
    name: "Meenakshi Amman Temple",
    location: "Madurai",
    period: "Nayak dynasty (16th-17th century)",
    description: "The Meenakshi Amman Temple is a historic Hindu temple dedicated to Goddess Meenakshi (a form of Parvati) and Lord Sundareswarar (a form of Shiva). The temple is known for its towering gopurams (gateway towers) adorned with thousands of colorful sculptures.",
    significance: "It is one of the most important temples for Tamil Hindus and represents the Dravidian architectural style at its finest. The temple complex covers 14 acres and has 14 gateway towers."
  }
};

/**
 * Find the best match for a word in a list of strings using fuzzy matching
 * @param {string} word - The word to match
 * @param {Array<string>} list - The list of strings to match against
 * @return {string|null} - The best match or null if no match found
 */
function findBestMatch(word, list) {
  // Simple fuzzy matching - check if the word is contained in any of the strings
  for (const item of list) {
    if (item.includes(word) || word.includes(item)) {
      return item;
    }
  }
  
  // Check for common misspellings or partial matches
  for (const item of list) {
    // Split both strings into characters
    const wordChars = word.split('');
    const itemChars = item.split('');
    
    // Count matching characters
    let matchCount = 0;
    for (const char of wordChars) {
      if (itemChars.includes(char)) {
        matchCount++;
      }
    }
    
    // If more than 70% of characters match, consider it a match
    if (matchCount / wordChars.length > 0.7) {
      return item;
    }
  }
  
  return null;
}

/**
 * Extract city name from a message using fuzzy matching
 * @param {string} message - The user's message
 * @return {string|null} - The extracted city name or null
 */
function extractCityWithFuzzyMatch(message) {
  const tamilNaduCities = [
    "chennai", "madurai", "coimbatore", "trichy", "salem", "tirunelveli",
    "vellore", "thanjavur", "tanjore", "kanyakumari", "ooty", "nilgiris",
    "kodaikanal", "rameswaram", "mahabalipuram", "kanchipuram", "dindigul",
    "tenkasi", "dharmapuri"
  ];
  
  // First check for direct matches
  for (const city of tamilNaduCities) {
    if (message.includes(city)) {
      return city;
    }
  }
  
  // Then try fuzzy matching on individual words
  const words = message.split(/\s+/);
  for (const word of words) {
    if (word.length < 3) continue;
    
    for (const city of tamilNaduCities) {
      // Check if the word is similar to the city name
      if (city.includes(word) || word.includes(city)) {
        return city;
      }
      
      // Check for common misspellings
      const wordChars = word.split('');
      const cityChars = city.split('');
      
      let matchCount = 0;
      for (const char of wordChars) {
        if (cityChars.includes(char)) {
          matchCount++;
        }
      }
      
      if (matchCount / wordChars.length > 0.7) {
        return city;
      }
    }
  }
  
  return null;
}

/**
 * Get a response from the heritage chatbot
 * @param {string} userMessage - The message from the user
 * @param {string} language - The language code (e.g., 'en')
 * @param {Array} conversationHistory - The conversation history
 * @return {string} - The chatbot's response
 */
function getHeritageResponse(userMessage, language, conversationHistory) {
  const lowerCaseMessage = userMessage.toLowerCase().trim();

  // DIRECT MATCHES FOR SPECIFIC PLACES
  if (lowerCaseMessage === "ooty") {
    return `Ooty (Udhagamandalam), a popular hill station in the Nilgiri Hills, is known for its pleasant climate and scenic beauty. Top places to visit in Ooty:\n\n1. Botanical Gardens - Established in 1848, featuring a fossil tree trunk estimated to be 20 million years old.\n2. Ooty Lake - An artificial lake created by John Sullivan in 1824, offering boating facilities.\n3. Doddabetta Peak - The highest peak in the Nilgiri mountains with a telescope house.\n4. Nilgiri Mountain Railway - A UNESCO World Heritage Site, this toy train offers breathtaking views.\n5. Rose Garden - Asia's largest rose garden with over 20,000 varieties of roses.`;
  }

  if (lowerCaseMessage === "madurai") {
    return `Madurai, one of the oldest continuously inhabited cities in the world, is known as the Temple City, with the magnificent Meenakshi Amman Temple at its heart. Top places to visit in Madurai:\n\n1. Meenakshi Amman Temple - The iconic temple with 14 gopurams (gateway towers) and thousands of sculptures.\n2. Thirumalai Nayakkar Palace - A 17th-century palace showcasing Dravidian and Islamic architectural styles.\n3. Gandhi Memorial Museum - Houses the blood-stained cloth worn by Gandhi when he was assassinated.\n4. Vandiyur Mariamman Teppakulam - A massive temple tank used for the float festival.\n5. Alagar Koil - A Vishnu temple located 21 km from Madurai with a sacred spring.`;
  }

  if (lowerCaseMessage === "kanyakumari") {
    return `Kanyakumari, the southernmost tip of mainland India, is where the Arabian Sea, Bay of Bengal, and Indian Ocean meet. Top places to visit in Kanyakumari:\n\n1. Vivekananda Rock Memorial - Built on a rock where Swami Vivekananda meditated in 1892.\n2. Thiruvalluvar Statue - A 133-foot tall stone sculpture of the Tamil poet and philosopher.\n3. Kanyakumari Temple - Ancient temple dedicated to Goddess Parvati as Devi Kanya Kumari.\n4. Sunset Point - Famous spot to witness the sunset and sunrise over the ocean.\n5. Gandhi Memorial Mandapam - Built where the ashes of Mahatma Gandhi were kept before immersion.`;
  }

  // DIRECT MATCHES FOR CATEGORIES
  if (lowerCaseMessage === "beaches" || lowerCaseMessage === "beach") {
    return `Tamil Nadu has a beautiful coastline with several beaches. Here are some popular beaches:\n\n1. Marina Beach (Chennai) - The second-longest urban beach in the world.\n2. Covelong Beach (Kovalam) - Known for water sports and surfing.\n3. Mahabalipuram Beach - Historic beach with shore temples.\n4. Rameshwaram Beach - A sacred beach with clear waters.\n5. Kanyakumari Beach - Where three oceans meet, famous for sunrise and sunset views.`;
  }

  if (lowerCaseMessage === "hills" || lowerCaseMessage === "hill stations" || lowerCaseMessage === "hill station") {
    return `Tamil Nadu has several beautiful hill stations. Here are the most popular ones:\n\n1. Ooty - Known for its botanical gardens and pleasant climate.\n2. Kodaikanal - Famous for its star-shaped lake and cool climate.\n3. Yelagiri - A smaller, less crowded hill station with trekking opportunities.\n4. Kolli Hills - Known for its 70 hairpin bends and Agaya Gangai waterfall.\n5. Yercaud - A serene hill station with coffee plantations and orange groves.`;
  }

  if (lowerCaseMessage === "temples" || lowerCaseMessage === "temple") {
    return `Tamil Nadu is famous for its magnificent temples. Here are some notable temples:\n\n1. Brihadeeswarar Temple (Thanjavur) - A UNESCO World Heritage Site built by Raja Raja Chola I.\n2. Meenakshi Amman Temple (Madurai) - Known for its colorful gopurams and thousands of sculptures.\n3. Shore Temple (Mahabalipuram) - A UNESCO site with beautiful seaside location.\n4. Ramanathaswamy Temple (Rameswaram) - Famous for its long corridors and 22 holy water tanks.\n5. Ekambareswarar Temple (Kanchipuram) - One of the five major Shiva temples with a 3,500-year-old mango tree.`;
  }

  // PATTERN MATCHING FOR COMPLEX QUERIES
  if (lowerCaseMessage.includes("places to visit in") || 
      lowerCaseMessage.includes("places in") || 
      lowerCaseMessage.includes("must visit places in") ||
      lowerCaseMessage.includes("tourist places in")) {
    
    let cityName = "";
    if (lowerCaseMessage.includes("places to visit in")) {
      cityName = lowerCaseMessage.split("places to visit in")[1].trim();
    } else if (lowerCaseMessage.includes("must visit places in")) {
      cityName = lowerCaseMessage.split("must visit places in")[1].trim();
    } else if (lowerCaseMessage.includes("tourist places in")) {
      cityName = lowerCaseMessage.split("tourist places in")[1].trim();
    } else {
      cityName = lowerCaseMessage.split("places in")[1].trim();
    }
    
    // Check for common cities
    if (cityName.includes("madurai") || cityName.includes("madura")) {
      return `Top places to visit in Madurai:\n\n1. Meenakshi Amman Temple - The iconic temple with 14 gopurams (gateway towers) and thousands of sculptures.\n2. Thirumalai Nayakkar Palace - A 17th-century palace showcasing Dravidian and Islamic architectural styles.\n3. Gandhi Memorial Museum - Houses the blood-stained cloth worn by Gandhi when he was assassinated.\n4. Vandiyur Mariamman Teppakulam - A massive temple tank used for the float festival.\n5. Alagar Koil - A Vishnu temple located 21 km from Madurai with a sacred spring.`;
    }
    
    if (cityName.includes("chennai")) {
      return `Top places to visit in Chennai:\n\n1. Marina Beach - The second-longest urban beach in the world.\n2. Fort St. George - The first English fortress in India, now housing the Tamil Nadu government secretariat.\n3. Kapaleeshwarar Temple - A 7th-century Dravidian-style temple dedicated to Lord Shiva.\n4. Government Museum - One of the oldest museums in India with archaeological collections.\n5. San Thome Basilica - Built over the tomb of St. Thomas the Apostle.`;
    }
    
    if (cityName.includes("thanjavur") || cityName.includes("tanjore")) {
      return `Top places to visit in Thanjavur:\n\n1. Brihadeeswarar Temple - The UNESCO World Heritage Site with its 216-foot tall vimana.\n2. Thanjavur Palace - Built by the Nayaks and later by the Marathas, housing the Saraswathi Mahal Library.\n3. Saraswathi Mahal Library - One of the oldest libraries in Asia with rare manuscripts.\n4. Thanjavur Art Gallery - Houses bronze statues from the Chola period.\n5. Schwartz Church - Built in 1779 by Raja Serfoji in honor of Rev. C.V. Schwartz.`;
    }
    
    if (cityName.includes("ooty") || cityName.includes("nilgiris")) {
      return `Top places to visit in Ooty (Nilgiris):\n\n1. Botanical Gardens - Established in 1848, featuring a fossil tree trunk estimated to be 20 million years old.\n2. Ooty Lake - An artificial lake created by John Sullivan in 1824, offering boating facilities.\n3. Doddabetta Peak - The highest peak in the Nilgiri mountains with a telescope house.\n4. Nilgiri Mountain Railway - A UNESCO World Heritage Site, this toy train offers breathtaking views.\n5. Rose Garden - Asia's largest rose garden with over 20,000 varieties of roses.`;
    }
    
    if (cityName.includes("kanyakumari") || cityName.includes("kaniyakumari")) {
      return `Top places to visit in Kanyakumari:\n\n1. Vivekananda Rock Memorial - Built on a rock where Swami Vivekananda meditated in 1892.\n2. Thiruvalluvar Statue - A 133-foot tall stone sculpture of the Tamil poet and philosopher.\n3. Kanyakumari Temple - Ancient temple dedicated to Goddess Parvati as Devi Kanya Kumari.\n4. Sunset Point - Famous spot to witness the sunset and sunrise over the ocean.\n5. Gandhi Memorial Mandapam - Built where the ashes of Mahatma Gandhi were kept before immersion.`;
    }
    
    if (cityName.includes("dindigul")) {
      return `Top places to visit in Dindigul:\n\n1. Dindigul Fort - A 17th-century fort built by the Nayak rulers and later strengthened by Hyder Ali and Tipu Sultan.\n2. Kodaikanal - A beautiful hill station just 100 km from Dindigul.\n3. Palani Murugan Temple - One of the six abodes of Lord Murugan, located 40 km from Dindigul.\n4. Kamarajar Lake - A scenic lake with boating facilities and a children's park.\n5. Athoor Lake - A serene lake surrounded by hills and agricultural fields.`;
    }
    
    if (cityName.includes("tenkasi")) {
      return `Top places to visit in Tenkasi:\n\n1. Kasi Viswanathar Temple - A beautiful temple with a gopuram visible from miles away.\n2. Courtallam Falls - Known as the 'Spa of South India' with five different falls having medicinal properties.\n3. Krishnapuram Palace - A historical palace with traditional Kerala architecture.\n4. Agasthiyar Falls - A scenic waterfall surrounded by dense forests.\n5. Thirumalai Kovil - A hill temple offering panoramic views of the surrounding area.`;
    }
    
    if (cityName.includes("dharmapuri")) {
      return `Top places to visit in Dharmapuri:\n\n1. Hogenakkal Falls - Known as the 'Niagara of India', famous for its medicinal baths and boat rides.\n2. Theerthamalai Temple - An ancient temple located on a small hill.\n3. Adhiyamankottai - The capital of the ancient Adhiyaman kingdom with historical ruins.\n4. Kottai Kovil - A fort temple with beautiful architecture.\n5. Vathalmalai - A hill village known for its coffee plantations and tribal culture.`;
    }
  }

  // Check for general heritage tourism questions
  if (lowerCaseMessage.includes("best time to visit") || lowerCaseMessage.includes("when to visit")) {
    return "The best time to visit Tamil Nadu's heritage sites is from October to March when the weather is pleasant. Avoid summer months (April-June) as temperatures can be extremely high.";
  }

  if (lowerCaseMessage.includes("how to reach") || lowerCaseMessage.includes("how to get to")) {
    return "Most heritage sites in Tamil Nadu are well-connected by road. Major cities like Chennai, Madurai, and Trichy have airports and railway stations. From there, you can hire taxis or take buses to reach specific heritage sites.";
  }

  if (lowerCaseMessage.includes("unesco") || lowerCaseMessage.includes("world heritage")) {
    return "Tamil Nadu has two UNESCO World Heritage Sites: 1) The Great Living Chola Temples (Brihadeeswarar Temple, Gangaikonda Cholapuram, and Airavatesvara Temple) and 2) The Group of Monuments at Mahabalipuram.";
  }

  if (lowerCaseMessage.includes("famous") || lowerCaseMessage.includes("popular") || lowerCaseMessage.includes("important")) {
    return "The most famous heritage sites in Tamil Nadu include Brihadeeswarar Temple (Thanjavur), Meenakshi Amman Temple (Madurai), Shore Temple (Mahabalipuram), Ramanathaswamy Temple (Rameswaram), and the temples of Kanchipuram.";
  }

  // Try fuzzy matching for city names
  const cityMatch = extractCityWithFuzzyMatch(lowerCaseMessage);
  if (cityMatch && tamilNaduPlaces && tamilNaduPlaces[cityMatch]) {
    return tamilNaduPlaces[cityMatch];
  }

  // Check in our expanded Tamil Nadu places list
  if (tamilNaduPlaces) {
    for (const place in tamilNaduPlaces) {
      if (lowerCaseMessage.includes(place)) {
        return tamilNaduPlaces[place];
      }
    }
  }

  // Then check in our simple tourist places list with fuzzy matching
  const touristPlaces = {
    "brihadeeswarar temple": "Brihadeeswarar Temple, also known as the Big Temple, is a UNESCO World Heritage Site located in Thanjavur. It is renowned for its architectural brilliance and historical significance.",
    "meenakshi amman temple": "Meenakshi Amman Temple in Madurai is famous for its stunning architecture and vibrant sculptures. It is a major pilgrimage site in Tamil Nadu.",
    "shore temple": "The Shore Temple at Mahabalipuram is a UNESCO World Heritage Site built by the Pallava dynasty. Standing on the shores of the Bay of Bengal, it's one of the oldest stone temples in South India.",
    "ramanathaswamy temple": "Ramanathaswamy Temple in Rameswaram is one of the twelve Jyotirlinga temples dedicated to Lord Shiva. It is known for its long corridors and sacred water tanks.",
    "kodaikanal": "Kodaikanal is a popular hill station in Tamil Nadu, known for its scenic beauty, serene lakes, and pleasant climate.",
    "ooty": "Ooty, also known as Udhagamandalam, is a famous hill station in Tamil Nadu, known for its tea gardens, botanical gardens, and the Nilgiri Mountain Railway.",
    "kanyakumari": "Kanyakumari, located at the southernmost tip of India, is famous for its stunning sunrise and sunset views, as well as the Vivekananda Rock Memorial."
  };

  // First check for direct location matches
  for (const place in touristPlaces) {
    if (lowerCaseMessage.includes(place)) {
      return touristPlaces[place];
    }
  }

  // Then try fuzzy matching on individual words
  const placeNames = Object.keys(touristPlaces);
  const words = lowerCaseMessage.split(/\s+/);
  
  for (const word of words) {
    if (word.length < 3) continue; // Allow shorter words to match locations like "ooty"

    // Check for location keywords
    if (word === "ooty" || word === "madurai" || word === "chennai" || 
        word === "nilgiris" || word === "dindigul" || word === "tenkasi" || 
        word === "dharmapuri" || word === "kanyakumari" || word === "thanjavur") {
      
      // Find the matching place
      for (const place in touristPlaces) {
        if (place.includes(word)) {
          return touristPlaces[place];
        }
      }
      
      // If no direct match in touristPlaces, provide a generic response about the location
      return `${word.charAt(0).toUpperCase() + word.slice(1)} is a notable location in Tamil Nadu. You can ask about specific places to visit in ${word}.`;
    }
    
    // Try fuzzy matching for other words
    const match = findBestMatch(word, placeNames);
    if (match) {
      return touristPlaces[match];
    }
  }

  // Check for specific question types
  const questionType = identifyQuestionType(lowerCaseMessage);
  const siteName = identifySite(lowerCaseMessage);
  
  // If we identified both a question type and a site, provide a targeted response
  if (questionType && siteName && window.heritageSiteInfo[siteName]) {
    const site = window.heritageSiteInfo[siteName];
    
    switch(questionType) {
      case "what":
        return `${site.name} is ${site.description}`;
      case "where":
        return `${site.name} is located in ${site.location}, Tamil Nadu.`;
      case "when":
        return `${site.name} was built during the ${site.period}.`;
      case "who":
        return `${site.name} was built during the ${site.period}. ${site.description.split('.')[0]}.`;
      case "how":
        return `${site.significance}`;
      case "why":
        return `${site.name} is significant because ${site.significance}`;
      default:
        return `${site.name} (${site.location}): ${site.description}`;
    }
  }
  
  // First check in our heritage site database for general queries
  for (const siteKey in window.heritageSiteInfo) {
    if (lowerCaseMessage.includes(siteKey)) {
      const site = window.heritageSiteInfo[siteKey];
      return `${site.name} (${site.location}): ${site.description}`;
    }
  }

  // Default response if no match found
  return "I'm sorry, I don't have specific information about that. You can ask me about major cities and heritage sites in Tamil Nadu like Chennai, Thanjavur, Madurai, or Mahabalipuram.";
}

/**
 * Identify the type of question being asked
 * @param {string} message - The user's message
 * @return {string|null} - The identified question type or null
 */
function identifyQuestionType(message) {
  for (const type in questionTypes) {
    if (questionTypes[type].some(keyword => message.includes(keyword))) {
      return type;
    }
  }
  return null;
}

/**
 * Identify which heritage site is being asked about
 * @param {string} message - The user's message
 * @return {string|null} - The identified site key or null
 */
function identifySite(message) {
  // First check direct matches with site keys
  for (const siteKey in window.heritageSiteInfo) {
    if (message.includes(siteKey)) {
      return siteKey;
    }
  }

  // Then check with keywords
  for (const siteKey in siteKeywords) {
    if (siteKeywords[siteKey].some(keyword => message.includes(keyword))) {
      // Map the keyword back to the actual site key in heritageSiteInfo
      for (const actualSiteKey in window.heritageSiteInfo) {
        if (actualSiteKey.includes(siteKey) || siteKey.includes(actualSiteKey)) {
          return actualSiteKey;
        }
      }
      return null;
    }
  }

  return null;
}

/**
 * Handle user input and display chatbot response
 * @param {string} userMessage - The message from the user
 */
function handleUserInput(userMessage) {
  const chatWindow = document.getElementById("liveChatMessages");

  // Display user message
  const userMessageElement = document.createElement("div");
  userMessageElement.className = "user-message";
  userMessageElement.textContent = userMessage;
  chatWindow.appendChild(userMessageElement);

  // Scroll to the bottom of the chat window
  chatWindow.scrollTop = chatWindow.scrollHeight;

  // Get initial response
  let response = getHeritageResponse(userMessage, "en", []);

  // Display initial response
  const botMessageElement = document.createElement("div");
  botMessageElement.className = "bot-message";
  botMessageElement.textContent = response;
  chatWindow.appendChild(botMessageElement);

  // Scroll to the bottom of the chat window
  chatWindow.scrollTop = chatWindow.scrollHeight;

  // If we want to use OpenAI for enhanced responses, we can do that separately
  if (shouldUseOpenAI(userMessage.toLowerCase())) {
    // Show loading indicator
    botMessageElement.textContent += " (Fetching more details...)";

    // Make API call
    const prompt = `Provide a brief, informative response about the following Tamil Nadu heritage query: "${userMessage}"`;
    fetchOpenAIResponse(prompt)
      .then(aiResponse => {
        // Update the bot message with AI response
        botMessageElement.textContent = aiResponse;
        // Scroll to the bottom again after update
        chatWindow.scrollTop = chatWindow.scrollHeight;
      })
      .catch(error => {
        console.error("Error fetching OpenAI response:", error);
        // Keep the original response if API call fails
      });
  }
}

/**
 * Determine if we should use OpenAI for this query
 */
function shouldUseOpenAI(message) {
  // Check if message contains any general heritage keywords
  return generalHeritageKeywords.some(keyword => message.includes(keyword));
}

/**
 * Initialize the chatbot functionality
 */
function initializeChatbot() {
  const liveChatInput = document.getElementById("liveChatInput");
  const liveChatSendButton = document.getElementById("liveChatSendButton");

  // Add event listener for the send button
  liveChatSendButton.addEventListener("click", () => {
    const userMessage = liveChatInput.value.trim();
    if (userMessage) {
      handleUserInput(userMessage);
      liveChatInput.value = ""; // Clear the input field
    }
  });

  // Add event listener for pressing Enter in the input field
  liveChatInput.addEventListener("keydown", (event) => {
    if (event.key === "Enter") {
      const userMessage = liveChatInput.value.trim();
      if (userMessage) {
        handleUserInput(userMessage);
        liveChatInput.value = ""; // Clear the input field
      }
    }
  });
}

// Initialize the chatbot when the DOM is fully loaded
document.addEventListener("DOMContentLoaded", initializeChatbot);
