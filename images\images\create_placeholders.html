<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Create Placeholder Images</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    canvas {
      border: 1px solid #ccc;
      margin-bottom: 10px;
    }
    .image-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }
    .placeholder {
      text-align: center;
    }
    button {
      padding: 10px 15px;
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin-bottom: 20px;
    }
    button:hover {
      background-color: #45a049;
    }
  </style>
</head>
<body>
  <h1>Heritage Explorer Placeholder Images</h1>
  <p>Click the button below to generate placeholder images for all heritage sites:</p>
  <button id="generateBtn">Generate All Placeholder Images</button>
  
  <div class="image-container" id="imageContainer"></div>

  <script>
    const sites = [
      { name: "Brihadeeswarar Temple", filename: "brihadeeswara_temple.jpg", color: "#E57373" },
      { name: "Meenakshi Amman Temple", filename: "meenakshi_amman_temple.jpg", color: "#81C784" },
      { name: "Shore Temple", filename: "shore_temple.jpg", color: "#64B5F6" },
      { name: "Thanjavur Palace", filename: "thanjavur_palace.jpg", color: "#FFD54F" },
      { name: "Ramanathaswamy Temple", filename: "ramanathaswamy_temple.jpg", color: "#BA68C8" },
      { name: "Gangaikonda Cholapuram", filename: "gangaikonda_cholapuram.jpg", color: "#4DB6AC" },
      { name: "Arjuna's Penance", filename: "arjunas_penance.jpg", color: "#FF8A65" },
      { name: "Airavatesvara Temple", filename: "airavatesvara_temple.jpg", color: "#7986CB" },
      { name: "Rock Fort Temple", filename: "rockfort_temple.jpg", color: "#A1887F" },
      { name: "Vellore Fort", filename: "vellore_fort.jpg", color: "#90A4AE" },
      { name: "Chettinad Mansions", filename: "chettinad_mansions.jpg", color: "#AED581" },
      { name: "Padmanabhapuram Palace", filename: "padmanabhapuram_palace.jpg", color: "#FFB74D" }
    ];

    function createPlaceholder(site) {
      const width = 400;
      const height = 300;
      
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');
      
      // Fill background
      ctx.fillStyle = site.color;
      ctx.fillRect(0, 0, width, height);
      
      // Add text
      ctx.fillStyle = '#fff';
      ctx.font = 'bold 24px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(site.name, width / 2, height / 2);
      
      // Add border
      ctx.strokeStyle = '#fff';
      ctx.lineWidth = 5;
      ctx.strokeRect(10, 10, width - 20, height - 20);
      
      return canvas;
    }

    function downloadImage(canvas, filename) {
      const link = document.createElement('a');
      link.download = filename;
      link.href = canvas.toDataURL('image/jpeg', 0.8);
      link.click();
    }

    document.getElementById('generateBtn').addEventListener('click', () => {
      const container = document.getElementById('imageContainer');
      container.innerHTML = '';
      
      sites.forEach(site => {
        const canvas = createPlaceholder(site);
        
        const div = document.createElement('div');
        div.className = 'placeholder';
        
        const downloadBtn = document.createElement('button');
        downloadBtn.textContent = `Download ${site.filename}`;
        downloadBtn.addEventListener('click', () => downloadImage(canvas, site.filename));
        
        div.appendChild(canvas);
        div.appendChild(downloadBtn);
        container.appendChild(div);
      });
    });
  </script>
</body>
</html>
