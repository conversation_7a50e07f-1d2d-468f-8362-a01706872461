<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Chettinad Palace - Heritage Explorer</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
  <style>
    :root {
      --primary-color: #1a237e;
      --secondary-color: #457b9d;
      --accent-color: #e63946;
      --light-color: #f1faee;
      --dark-color: #1d3557;
      --success-color: #2e7d32;
      --warning-color: #ff9800;
      --danger-color: #c62828;
      --gray-100: #f8f9fa;
      --gray-200: #e9ecef;
      --gray-300: #dee2e6;
      --gray-400: #ced4da;
      --gray-500: #adb5bd;
      --gray-600: #6c757d;
      --gray-700: #495057;
      --gray-800: #343a40;
      --gray-900: #212529;
      --border-radius: 0.5rem;
      --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
      --transition: all 0.3s ease-in-out;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: var(--gray-800);
      background-color: var(--gray-100);
    }

    header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      text-align: center;
    }

    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      background-color: white;
      box-shadow: var(--box-shadow);
    }

    nav a {
      color: var(--gray-700);
      text-decoration: none;
      margin: 0 1rem;
      font-weight: 500;
      transition: var(--transition);
    }

    nav a:hover {
      color: var(--primary-color);
    }

    main {
      max-width: 1200px;
      margin: 2rem auto;
      padding: 0 1rem;
    }

    .site-details {
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    @media (min-width: 768px) {
      .site-details {
        grid-template-columns: 1fr 1fr;
      }
    }

    .site-image {
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
    }

    .site-image img {
      width: 100%;
      height: auto;
      display: block;
    }

    .site-info h1 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .site-info p {
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }

    .site-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .meta-item {
      display: flex;
      align-items: center;
      background-color: var(--gray-200);
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
    }

    .meta-item i {
      margin-right: 0.5rem;
      color: var(--secondary-color);
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      margin-top: 1.5rem;
    }

    .action-buttons button {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      background-color: var(--secondary-color);
      color: white;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
      display: flex;
      align-items: center;
    }

    .action-buttons button i {
      margin-right: 0.5rem;
    }

    .action-buttons button:hover {
      background-color: var(--primary-color);
    }

    .site-description {
      margin-bottom: 2rem;
    }

    .site-description h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .map-container {
      height: 400px;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      margin-bottom: 2rem;
    }

    .nearby-attractions {
      margin-bottom: 2rem;
    }

    .nearby-attractions h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .attractions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .attraction-card {
      background-color: white;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
    }

    .attraction-card:hover {
      transform: translateY(-5px);
    }

    .attraction-card img {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }

    .attraction-card-content {
      padding: 1rem;
    }

    .attraction-card-content h3 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    footer {
      background-color: var(--primary-color);
      color: white;
      text-align: center;
      padding: 1rem;
      margin-top: 2rem;
    }

    #backToTop {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      width: 3rem;
      height: 3rem;
      background-color: var(--primary-color);
      color: white;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
      display: none;
    }

    #backToTop:hover {
      background-color: var(--secondary-color);
    }

    /* Travel Information Section */
    .travel-info {
      margin-bottom: 2rem;
      background-color: white;
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      overflow: hidden;
    }

    .travel-info-header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .travel-info-header h2 {
      margin: 0;
      font-size: 1.5rem;
    }

    .travel-info-tabs {
      display: flex;
      background-color: var(--gray-200);
    }

    .travel-tab {
      padding: 1rem;
      cursor: pointer;
      flex: 1;
      text-align: center;
      font-weight: 500;
      transition: var(--transition);
      border-bottom: 3px solid transparent;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .travel-tab i {
      margin-right: 0.5rem;
    }

    .travel-tab.active {
      background-color: white;
      border-bottom-color: var(--accent-color);
    }

    .travel-tab:hover:not(.active) {
      background-color: var(--gray-300);
    }

    .travel-content {
      padding: 1.5rem;
    }

    .travel-panel {
      display: none;
    }

    .travel-panel.active {
      display: block;
    }

    /* Weather Panel */
    .weather-info {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      align-items: center;
    }

    .weather-current {
      display: flex;
      align-items: center;
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      flex: 1;
      min-width: 250px;
    }

    .weather-icon {
      font-size: 3rem;
      margin-right: 1rem;
      color: var(--secondary-color);
    }

    .weather-details h3 {
      margin: 0;
      color: var(--primary-color);
    }

    .weather-temp {
      font-size: 2rem;
      font-weight: bold;
    }

    .weather-desc {
      color: var(--gray-700);
    }

    .weather-forecast {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      flex: 2;
      min-width: 300px;
    }

    .forecast-day {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      text-align: center;
      flex: 1;
      min-width: 100px;
    }

    .forecast-day h4 {
      margin: 0 0 0.5rem 0;
      color: var(--primary-color);
    }

    .forecast-icon {
      font-size: 1.5rem;
      margin: 0.5rem 0;
      color: var(--secondary-color);
    }

    /* Transportation Panel */
    .transport-options {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .transport-option {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
      display: flex;
      flex-direction: column;
    }

    .transport-option h3 {
      display: flex;
      align-items: center;
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .transport-option h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .transport-option p {
      margin-bottom: 0.5rem;
    }

    .transport-option .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    /* Hours & Tickets Panel */
    .hours-container {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .opening-hours, .ticket-info {
      flex: 1;
      min-width: 250px;
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .opening-hours h3, .ticket-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .opening-hours h3 i, .ticket-info h3 i {
      margin-right: 0.5rem;
    }

    .hours-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .hours-list li {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .hours-list li:last-child {
      border-bottom: none;
    }

    .day {
      font-weight: 500;
    }

    .ticket-type {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .ticket-type:last-child {
      border-bottom: none;
    }

    .ticket-price {
      font-weight: bold;
    }

    /* Amenities Panel */
    .amenities-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .amenity-card {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .amenity-card h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .amenity-card h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .amenity-card p {
      margin-bottom: 0.5rem;
    }

    .amenity-card .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    .amenity-card .rating {
      color: var(--warning-color);
      margin-bottom: 0.5rem;
    }

    .weather-tips h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .local-transport h3, .best-time h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>Heritage Explorer</h1>
    <p>Discover Tamil Nadu's rich heritage and culture</p>
  </header>

  <nav>
    <div>
      <a href="website.html"><i class="fas fa-home"></i> Home</a>
      <a href="#"><i class="fas fa-map-marked-alt"></i> Explore</a>
      <a href="#"><i class="fas fa-info-circle"></i> About</a>
    </div>
    <div>
      <a href="#" id="toggleLang">தமிழ்</a>
    </div>
  </nav>

  <main>
    <div class="site-details">
      <div class="site-image">
        <img src="https://www.shutterstock.com/image-photo/karaikkudi-india-103106-kanadukathan-palace-600nw-1978887176.jpg" alt="Chettinad Palace">
      </div>
      <div class="site-info">
        <h1>Chettinad Palace</h1>
        <div class="site-meta">
          <div class="meta-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>Karaikudi, Tamil Nadu</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-place-of-worship"></i>
            <span>Palace</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-calendar-alt"></i>
            <span>Built in early 20th century</span>
          </div>
        </div>
        <p>Chettinad Palace, also known as the Chettinad Mansion, is a magnificent heritage structure showcasing the opulent lifestyle of the Chettiars, a prosperous merchant community. The palace features imported materials, intricate woodwork, colorful tiles, and exquisite craftsmanship that reflects the community's wealth and global connections.</p>
        <div class="action-buttons">
          <button id="playAudioButton"><i class="fas fa-volume-up"></i> Listen</button>
          <button id="favoriteButton"><i class="far fa-heart"></i> Favorite</button>
          <button id="shareButton"><i class="fas fa-share-alt"></i> Share</button>
        </div>
      </div>
    </div>

    <div class="site-description">
      <h2>About Chettinad Palace</h2>
      <p>Chettinad Palace is a grand mansion located in the Chettinad region of Tamil Nadu, specifically in Karaikudi. It stands as a testament to the wealth and cultural sophistication of the Nattukottai Chettiars, a prosperous community of merchants and bankers who amassed their fortune through trade with Southeast Asia and beyond during the 19th and early 20th centuries.</p>
      <p>The palace is renowned for its architectural grandeur, which is a unique blend of various styles including Tamil, European, and East Asian influences. The most striking feature of the palace is its use of materials imported from around the world. The mansion boasts of Italian marble floors, teakwood from Burma, chandeliers from Czechoslovakia, tiles from Japan and Europe, and mirrors from Belgium. The entrance to the palace is particularly impressive, with massive wooden doors adorned with intricate carvings.</p>
      <p>The interior of the palace is equally magnificent, featuring spacious courtyards, high ceilings, and elaborate pillars. The walls are adorned with vibrant frescoes and paintings depicting scenes from Hindu mythology and daily life. The palace also houses a collection of antique furniture, vintage photographs, and artifacts that provide insights into the lifestyle of the Chettiar community. The kitchen area, with its traditional setup and utensils, offers a glimpse into the culinary heritage of the region.</p>
      <p>Today, Chettinad Palace serves as a cultural heritage site and a popular tourist destination. It offers visitors a chance to step back in time and experience the opulence and grandeur of a bygone era. The palace is also a venue for cultural events and has been featured in several Tamil films due to its picturesque setting. The surrounding Chettinad region is known for its spicy cuisine, handwoven cotton sarees, and antique shops, making it a comprehensive cultural experience for visitors.</p>
    </div>

    <div class="map-container">
      <!-- Map will be embedded here -->
      <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3938.8123456789!2d78.78!3d10.074!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3b0066d1a3c8d7c9%3A0x4c0d0a7fc1d72eba!2sChettinad%20Palace!5e0!3m2!1sen!2sin!4v1652345678901!5m2!1sen!2sin" width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy" title="Map of Chettinad Palace"></iframe>
    </div>

    <!-- Travel Information Section -->
    <div class="travel-info">
      <div class="travel-info-header">
        <h2>Practical Travel Information</h2>
        <div class="last-updated">Last updated: <span id="lastUpdated">Today</span></div>
      </div>

      <div class="travel-info-tabs">
        <div class="travel-tab active" data-tab="weather">
          <i class="fas fa-cloud-sun"></i> Weather
        </div>
        <div class="travel-tab" data-tab="transport">
          <i class="fas fa-bus"></i> Transportation
        </div>
        <div class="travel-tab" data-tab="hours">
          <i class="far fa-clock"></i> Hours & Visits
        </div>
        <div class="travel-tab" data-tab="amenities">
          <i class="fas fa-concierge-bell"></i> Nearby Amenities
        </div>
      </div>

      <div class="travel-content">
        <!-- Weather Panel -->
        <div class="travel-panel active" id="weatherPanel">
          <div class="weather-info">
            <div class="weather-current">
              <div class="weather-icon">
                <i class="fas fa-sun"></i>
              </div>
              <div class="weather-details">
                <h3>Current Weather</h3>
                <div class="weather-temp">33°C</div>
                <div class="weather-desc">Sunny</div>
                <div class="weather-extra">Humidity: 70% | Wind: 10 km/h</div>
              </div>
            </div>

            <div class="weather-forecast">
              <div class="forecast-day">
                <h4>Tomorrow</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun"></i></div>
                <div class="forecast-temp">32°C</div>
                <div class="forecast-desc">Partly Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Wed</h4>
                <div class="forecast-icon"><i class="fas fa-cloud"></i></div>
                <div class="forecast-temp">31°C</div>
                <div class="forecast-desc">Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Thu</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun"></i></div>
                <div class="forecast-temp">33°C</div>
                <div class="forecast-desc">Mostly Sunny</div>
              </div>
            </div>
          </div>

          <div class="weather-tips">
            <h3>Weather Tips</h3>
            <p>The Chettinad region experiences a hot and dry climate for most of the year. Temperatures range from 25°C to 40°C, with summer months (March-June) being particularly hot and humid. The best time to visit is from October to March when the weather is relatively cooler and more comfortable for exploring the palace and surrounding heritage houses.</p>
            <p>The monsoon season (July-September) brings moderate rainfall to the region, which can occasionally disrupt travel plans. If visiting during summer, it's advisable to plan your outdoor activities in the early morning or late afternoon to avoid the midday heat. The palace interiors remain relatively cool due to their traditional architectural design with high ceilings and thick walls. Light cotton clothing, sunglasses, hats, and sunscreen are recommended year-round, along with carrying sufficient water to stay hydrated.</p>
          </div>
        </div>

        <!-- Transportation Panel -->
        <div class="travel-panel" id="transportPanel">
          <div class="transport-options">
            <div class="transport-option">
              <h3><i class="fas fa-plane"></i> By Air</h3>
              <p>Madurai Airport is the nearest airport.</p>
              <p class="distance">85 km from Karaikudi (approx. 2 hours by car)</p>
              <p>Regular flights from Chennai, Bangalore, and other major cities.</p>
              <p>Taxi services available from the airport (₹2,000-2,500).</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-train"></i> By Train</h3>
              <p>Karaikudi Junction Railway Station is well-connected.</p>
              <p class="distance">5 km from Chettinad Palace (15 minutes by auto-rickshaw)</p>
              <p>Regular trains from Chennai, Madurai, Trichy, and Rameswaram.</p>
              <p>Auto-rickshaws and taxis available outside the station.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-bus"></i> By Bus</h3>
              <p>Regular state-run and private buses operate to Karaikudi.</p>
              <p>Direct buses from Chennai (8 hours), Madurai (2 hours), Trichy (3 hours).</p>
              <p>The bus stand is located about 4 km from the palace.</p>
              <p>Auto-rickshaws available from the bus stand (₹80-100).</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-car"></i> By Car</h3>
              <p>Well-connected by roads from major cities.</p>
              <p>From Chennai: NH-32 and NH-226 (420 km, approx. 8 hours)</p>
              <p>From Madurai: NH-38 (85 km, approx. 2 hours)</p>
              <p>From Trichy: NH-210 (110 km, approx. 2.5 hours)</p>
            </div>
          </div>

          <div class="local-transport">
            <h3>Local Transportation</h3>
            <p>Within Karaikudi and the Chettinad region, auto-rickshaws are the primary mode of transportation. They typically charge a fixed rate rather than by meter, so negotiate the fare before starting your journey. For exploring the various heritage mansions and attractions scattered across the Chettinad region, hiring a car with a driver for the day is the most convenient option (₹2,000-3,000 per day). Many hotels and guesthouses can arrange this service. Some travelers also rent bicycles or motorcycles to explore the nearby villages at a leisurely pace. The roads in the region are generally in good condition, though some smaller village roads may be narrow.</p>
          </div>
        </div>

        <!-- Hours & Visits Panel -->
        <div class="travel-panel" id="hoursPanel">
          <div class="hours-container">
            <div class="opening-hours">
              <h3><i class="far fa-clock"></i> Visiting Hours</h3>
              <ul class="hours-list">
                <li><span class="day">All Days:</span> <span class="time">9:00 AM - 5:00 PM</span></li>
                <li><span class="day">Last Entry:</span> <span class="time">4:30 PM</span></li>
                <li><span class="day">Best Time to Visit:</span> <span class="time">Morning (9:00 AM - 11:00 AM)</span></li>
                <li><span class="day">Time Required:</span> <span class="time">1-2 hours</span></li>
                <li><span class="day">Photography:</span> <span class="time">Permitted (some restrictions apply)</span></li>
              </ul>
              <p class="hours-note">As Chettinad Palace is a privately owned property, visiting hours may occasionally change for special events or maintenance. It's advisable to confirm the timings before planning your visit. Some sections of the palace may be restricted as they are still used by the family. Guided tours are available and recommended to fully appreciate the historical and architectural significance of the palace.</p>
            </div>

            <div class="ticket-info">
              <h3><i class="fas fa-ticket-alt"></i> Entry Information</h3>
              <div class="ticket-type">
                <span>Indian Nationals:</span>
                <span class="ticket-price">₹100 per person</span>
              </div>
              <div class="ticket-type">
                <span>Foreign Nationals:</span>
                <span class="ticket-price">₹300 per person</span>
              </div>
              <div class="ticket-type">
                <span>Children (below 12 years):</span>
                <span class="ticket-price">₹50</span>
              </div>
              <div class="ticket-type">
                <span>Guided Tour:</span>
                <span class="ticket-price">₹200 additional</span>
              </div>
              <p class="ticket-note">Tickets can be purchased at the entrance. Only cash payment is accepted. The guided tour is highly recommended as it provides valuable insights into the history, architecture, and cultural significance of the palace. Group discounts are available for parties of 10 or more people. Special photography permits for professional cameras may be required (₹500).</p>
            </div>
          </div>

          <div class="best-time">
            <h3>Best Time to Visit</h3>
            <p>The best time to visit Chettinad Palace is from October to March when the weather is pleasant with temperatures ranging from 20°C to 30°C. This period offers comfortable conditions for exploring both the palace and the surrounding Chettinad region. Early mornings are ideal for photography as the natural light beautifully illuminates the colorful tiles and intricate woodwork of the palace. Weekdays are less crowded than weekends, allowing for a more peaceful experience. If possible, try to avoid visiting during the peak summer months (April-June) when temperatures can soar above 40°C. The annual Chettinad Festival, usually held in January, is a great time to visit as you can experience the local culture, cuisine, and traditions alongside your palace visit.</p>
          </div>
        </div>

        <!-- Amenities Panel -->
        <div class="travel-panel" id="amenitiesPanel">
          <div class="amenities-grid">
            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>The Bangala</strong></p>
              <p class="distance">3 km from palace</p>
              <p class="rating">★★★★☆ (4.5/5)</p>
              <p>Heritage hotel with traditional Chettinad architecture and cuisine.</p>
              <p>Price range: ₹5,000 - ₹8,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Chidambara Vilas</strong></p>
              <p class="distance">7 km from palace</p>
              <p class="rating">★★★★★ (4.7/5)</p>
              <p>Luxury heritage hotel in a restored 110-year-old mansion.</p>
              <p>Price range: ₹8,000 - ₹12,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Chettinad Mansion</strong></p>
              <p class="distance">2 km from palace</p>
              <p class="rating">★★★☆☆ (3.8/5)</p>
              <p>Budget-friendly heritage guesthouse with authentic ambiance.</p>
              <p>Price range: ₹2,000 - ₹3,500 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>The Bangala Restaurant</strong></p>
              <p class="distance">3 km from palace</p>
              <p class="rating">★★★★★ (4.8/5)</p>
              <p>Famous for authentic Chettinad cuisine served on banana leaves.</p>
              <p>Price range: ₹800 - ₹1,200 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Karaikudi Mess</strong></p>
              <p class="distance">4 km from palace</p>
              <p class="rating">★★★★☆ (4.2/5)</p>
              <p>Local restaurant serving traditional Chettinad dishes.</p>
              <p>Price range: ₹300 - ₹500 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-shopping-bag"></i> Shopping</h3>
              <p><strong>Chettinad Antiques Market</strong></p>
              <p class="distance">5 km from palace</p>
              <p>Shops selling antique furniture, artifacts, and collectibles.</p>
              <p>Must-buy: Athangudi tiles, Kandangi sarees, and brass items</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-first-aid"></i> Medical Facilities</h3>
              <p><strong>Government Hospital</strong></p>
              <p class="distance">4 km from palace</p>
              <p>Basic medical facilities for emergencies.</p>
              <p>For serious medical issues, Madurai (85 km) has better facilities.</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-info-circle"></i> Tourist Information</h3>
              <p><strong>Tamil Nadu Tourism Office</strong></p>
              <p class="distance">4 km from palace (in Karaikudi town)</p>
              <p>Provides maps, brochures, and guided tour information.</p>
              <p>Contact: +91 4565 235 710</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="nearby-attractions">
      <h2>Nearby Attractions</h2>
      <div class="attractions-grid">
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Chettinad_Houses_20190212182003.jpg" alt="Chettinad Heritage Houses">
          <div class="attraction-card-content">
            <h3>Chettinad Heritage Houses</h3>
            <p>Magnificent mansions with unique architecture featuring imported materials, intricate woodwork, and colorful tiles that showcase the wealth of the Chettiar community.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Athangudi_Palace_Tiles_20190212182004.jpg" alt="Athangudi Palace Tiles Factory">
          <div class="attraction-card-content">
            <h3>Athangudi Palace Tiles Factory</h3>
            <p>A traditional handmade tile factory where visitors can observe the unique process of creating the famous colorful Athangudi tiles that adorn Chettinad mansions.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Chettinad_Cuisine_20190212182005.jpg" alt="Chettinad Cuisine Restaurants">
          <div class="attraction-card-content">
            <h3>Chettinad Cuisine Restaurants</h3>
            <p>Restaurants serving authentic Chettinad cuisine, known for its aromatic spices, fresh ingredients, and unique cooking techniques that create bold and flavorful dishes.</p>
          </div>
        </div>
      </div>
    </div>
  </main>

  <footer>
    <p>&copy; 2025 Heritage Explorer | Explore Tamil Nadu</p>
  </footer>

  <div id="backToTop">
    <i class="fas fa-arrow-up"></i>
  </div>

  <script>
    // Back to Top Button
    const backToTop = document.getElementById("backToTop");
    window.addEventListener("scroll", () => {
      if (window.scrollY > 300) {
        backToTop.style.display = "flex";
      } else {
        backToTop.style.display = "none";
      }
    });
    backToTop.addEventListener("click", () => {
      window.scrollTo({ top: 0, behavior: "smooth" });
    });

    // Audio Playback
    document.getElementById("playAudioButton").addEventListener("click", () => {
      const description = document.querySelector(".site-info p").textContent;
      const utterance = new SpeechSynthesisUtterance(description);
      utterance.lang = "en-US";
      speechSynthesis.speak(utterance);
    });

    // Favorite Button
    document.getElementById("favoriteButton").addEventListener("click", function() {
      const icon = this.querySelector("i");
      if (icon.classList.contains("far")) {
        icon.classList.remove("far");
        icon.classList.add("fas");
        alert("Added to favorites!");
      } else {
        icon.classList.remove("fas");
        icon.classList.add("far");
        alert("Removed from favorites!");
      }
    });

    // Share Button
    document.getElementById("shareButton").addEventListener("click", () => {
      const shareData = {
        title: "Chettinad Palace - Heritage Explorer",
        text: "Check out this magnificent palace in Tamil Nadu!",
        url: window.location.href
      };

      if (navigator.share) {
        navigator.share(shareData)
          .then(() => console.log("Shared successfully"))
          .catch((error) => console.error("Error sharing:", error));
      } else {
        navigator.clipboard.writeText(shareData.url)
          .then(() => alert("URL copied to clipboard"))
          .catch((err) => console.error("Could not copy text: ", err));
      }
    });

    // Initialize the travel info tabs
    document.addEventListener("DOMContentLoaded", function() {
      // Set the last updated date
      const today = new Date();
      document.getElementById("lastUpdated").textContent = today.toLocaleDateString();

      // Tab functionality
      const tabs = document.querySelectorAll(".travel-tab");
      tabs.forEach(tab => {
        tab.addEventListener("click", function() {
          // Remove active class from all tabs
          tabs.forEach(t => t.classList.remove("active"));

          // Add active class to clicked tab
          this.classList.add("active");

          // Hide all panels
          const panels = document.querySelectorAll(".travel-panel");
          panels.forEach(panel => panel.classList.remove("active"));

          // Show the corresponding panel
          const tabId = this.getAttribute("data-tab");
          document.getElementById(tabId + "Panel").classList.add("active");
        });
      });

      // Fetch weather data (mock)
      fetchWeatherData();
    });

    // Weather API Integration (Mock)
    function fetchWeatherData() {
      // Coordinates for Karaikudi/Chettinad region
      const lat = 10.0740;
      const lon = 78.7800;

      // Mock weather data
      const mockWeatherData = {
        current: {
          temp: 33,
          humidity: 70,
          wind_speed: 10,
          weather: [{ main: "Clear", description: "Sunny", icon: "01d" }]
        },
        daily: [
          {
            dt: Date.now() + 86400000, // Tomorrow
            temp: { day: 32 },
            weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
          },
          {
            dt: Date.now() + 172800000, // Day after tomorrow
            temp: { day: 31 },
            weather: [{ main: "Clouds", description: "Cloudy", icon: "03d" }]
          },
          {
            dt: Date.now() + 259200000, // 3 days from now
            temp: { day: 33 },
            weather: [{ main: "Clear", description: "Mostly Sunny", icon: "01d" }]
          }
        ]
      };

      updateWeatherUI(mockWeatherData);
    }

    function updateWeatherUI(data) {
      // Update current weather
      document.querySelector(".weather-temp").textContent = `${data.current.temp}°C`;
      document.querySelector(".weather-desc").textContent = data.current.weather[0].description;
      document.querySelector(".weather-extra").textContent = `Humidity: ${data.current.humidity}% | Wind: ${data.current.wind_speed} km/h`;

      // Update weather icon
      const weatherIconElement = document.querySelector(".weather-icon i");
      updateWeatherIcon(weatherIconElement, data.current.weather[0].main);

      // Update forecast
      const forecastDays = document.querySelectorAll(".forecast-day");
      data.daily.forEach((day, index) => {
        if (index < forecastDays.length) {
          const dayElement = forecastDays[index];
          const date = new Date(day.dt);
          const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });

          dayElement.querySelector("h4").textContent = index === 0 ? "Tomorrow" : dayName;
          dayElement.querySelector(".forecast-temp").textContent = `${day.temp.day}°C`;
          dayElement.querySelector(".forecast-desc").textContent = day.weather[0].description;

          // Update forecast icon
          const iconElement = dayElement.querySelector(".forecast-icon i");
          updateWeatherIcon(iconElement, day.weather[0].main);
        }
      });
    }

    function updateWeatherIcon(iconElement, weatherType) {
      // Remove all existing weather classes
      iconElement.className = "";

      // Add the appropriate weather icon class
      switch (weatherType) {
        case "Clear":
          iconElement.classList.add("fas", "fa-sun");
          break;
        case "Clouds":
          iconElement.classList.add("fas", "fa-cloud");
          break;
        case "Rain":
        case "Drizzle":
          iconElement.classList.add("fas", "fa-cloud-rain");
          break;
        case "Thunderstorm":
          iconElement.classList.add("fas", "fa-bolt");
          break;
        case "Snow":
          iconElement.classList.add("fas", "fa-snowflake");
          break;
        case "Mist":
        case "Fog":
        case "Haze":
          iconElement.classList.add("fas", "fa-smog");
          break;
        default:
          iconElement.classList.add("fas", "fa-cloud-sun");
      }
    }
  </script>

  <!-- Language Toggle Script -->
  <script src="language-toggle.js"></script>
</body>
</html>
