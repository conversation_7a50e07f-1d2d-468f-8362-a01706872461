/* Development Mode Styles */

/* Alert styles for development mode */
.alert.warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border: 2px solid #ffc107;
    border-radius: 8px;
    padding: 15px 20px;
    margin: 15px 0;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.2);
    animation: slideIn 0.3s ease-out;
}

.alert.warning::before {
    content: "⚠️ ";
    font-size: 1.2em;
    margin-right: 8px;
}

/* Development mode banner */
.dev-mode-banner {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 20px;
    text-align: center;
    font-size: 0.9em;
    font-weight: 500;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    animation: slideDown 0.5s ease-out;
}

.dev-mode-banner .close-btn {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: white;
    font-size: 1.2em;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: background 0.3s ease;
}

.dev-mode-banner .close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Adjust body padding when banner is shown */
body.dev-mode {
    padding-top: 45px;
}

/* Success message styles */
.alert.success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 2px solid #28a745;
    border-radius: 8px;
    padding: 15px 20px;
    margin: 15px 0;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
    animation: slideIn 0.3s ease-out;
}

.alert.success::before {
    content: "✅ ";
    font-size: 1.2em;
    margin-right: 8px;
}

/* Error message styles */
.alert.error {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border: 2px solid #dc3545;
    border-radius: 8px;
    padding: 15px 20px;
    margin: 15px 0;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.2);
    animation: slideIn 0.3s ease-out;
}

.alert.error::before {
    content: "❌ ";
    font-size: 1.2em;
    margin-right: 8px;
}

/* Info message styles */
.alert.info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
    border: 2px solid #17a2b8;
    border-radius: 8px;
    padding: 15px 20px;
    margin: 15px 0;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.2);
    animation: slideIn 0.3s ease-out;
}

.alert.info::before {
    content: "ℹ️ ";
    font-size: 1.2em;
    margin-right: 8px;
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

/* Development mode indicator */
.dev-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(102, 126, 234, 0.9);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: 500;
    z-index: 1000;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 1;
    }
}

/* Button loading state improvements */
.btn.loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
}

.btn.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    color: white;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Form field validation improvements */
.form-group input.success {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-group input.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.validation-message.success {
    color: #28a745;
    font-size: 0.875em;
    margin-top: 5px;
}

.validation-message.error {
    color: #dc3545;
    font-size: 0.875em;
    margin-top: 5px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dev-mode-banner {
        font-size: 0.8em;
        padding: 6px 15px;
    }
    
    body.dev-mode {
        padding-top: 40px;
    }
    
    .dev-indicator {
        bottom: 15px;
        right: 15px;
        font-size: 0.7em;
        padding: 6px 12px;
    }
    
    .alert {
        padding: 12px 15px;
        font-size: 0.9em;
    }
}
