{"name": "heritage-explorer", "version": "1.0.0", "description": "Heritage Explorer - Tamil Nadu Tourism Website with Authentication System", "main": "dev-server.js", "scripts": {"dev": "node dev-server.js", "start": "node dev-server.js", "build": "echo 'No build process needed for this PHP project'", "setup": "node setup-project.js", "xampp": "powershell -ExecutionPolicy Bypass -File run-terminal.ps1", "test": "node test-server.js", "serve": "node dev-server.js --port 3000", "serve:prod": "node dev-server.js --port 8080", "clean": "echo 'Cleaning temporary files...' && node -e \"console.log('Clean completed')\"", "help": "node -e \"console.log('\\n🏛️ Heritage Explorer Commands:\\n\\n📦 pnpm dev     - Start development server\\n🚀 pnpm setup   - Setup project with XAMPP\\n🔧 pnpm xampp   - Run XAMPP setup script\\n🧪 pnpm test    - Test server functionality\\n📋 pnpm help    - Show this help\\n')\""}, "keywords": ["heritage", "tourism", "tamil-nadu", "php", "mysql", "authentication", "travel", "cultural-sites"], "author": "Heritage Explorer Team", "license": "MIT", "engines": {"node": ">=14.0.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "body-parser": "^1.20.2"}, "repository": {"type": "git", "url": "https://github.com/heritage-explorer/heritage-explorer.git"}, "bugs": {"url": "https://github.com/heritage-explorer/heritage-explorer/issues"}, "homepage": "https://heritage-explorer.github.io", "config": {"port": 3000, "host": "localhost"}, "pnpm": {"overrides": {"semver": "^7.5.4"}}}