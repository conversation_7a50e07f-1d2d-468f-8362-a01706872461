* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  html {
    scroll-behavior: smooth;
  }
  body {
    font-family: 'Poppins', sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
  }
  img {
    display: block;
    width: 100%;
    height: auto;
  }
  
  /* Header Styles */
  header {
    background: linear-gradient(135deg, #8c42e6, #5bb3f6);
    color: #fff;
    text-align: center;
    padding: 2rem 1rem;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  }
  header h1 {
    font-size: 2.8rem;
    margin-bottom: 0.5rem;
    letter-spacing: 1px;
  }
  header p {
    font-size: 1.2rem;
    margin-bottom: 1rem;
  }
  nav.language-toggle {
    margin-top: 1rem;
  }
  nav.language-toggle button {
    background: #fff;
    color: #1976d2;
    padding: 0.5rem 1.5rem;
    border: none;
    border-radius: 25px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-right: 0.5rem;
  }
  nav.language-toggle button:hover {
    background-color: #e3f2fd;
  }
  #favoritesButton {
    background: #fff;
    color: #1976d2;
    padding: 0.5rem 1.5rem;
    border: none;
    border-radius: 25px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }
  #favoritesButton:hover {
    background-color: #e3f2fd;
  }
  
  /* Search Container */
  .search-container {
    max-width: 600px;
    margin: 2rem auto;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    padding: 0 1rem;
  }
  #searchBar {
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    border: 2px solid #ddd;
    border-radius: 30px;
    outline: none;
    transition: border-color 0.3s ease;
  }
  #searchBar:focus {
    border-color: #1976d2;
  }
  #micButton, #currentLocationButton {
    background: #1976d2;
    color: #fff;
    border: none;
    border-radius: 50%;
    padding: 0.75rem;
    cursor: pointer;
    font-size: 1.2rem;
    transition: background-color 0.3s ease;
  }
  #micButton:hover, #currentLocationButton:hover {
    background-color: #1565c0;
  }
  #currentLocationButton {
    border-radius: 25px;
    font-size: 1rem;
    padding: 0.5rem 1rem;
  }
  
  /* Spinner for Geolocation Loading */
  .spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #1976d2;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
    display: none;
  }
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  /* Location Filter Dropdown */
  .location-filter {
    max-width: 600px;
    margin: 1rem auto;
    text-align: center;
  }
  .location-filter select {
    padding: 0.5rem;
    font-size: 1rem;
    border: 1px solid #ddd;
    border-radius: 5px;
  }
  
  /* Heritage Site Cards */
  .site-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    padding: 0 2rem 2rem;
  }
  .site-card {
    background: #fff;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  .site-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.15);
  }
  .site-card-content {
    padding: 1rem 1.5rem;
  }
  .site-card-content h3 {
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
    color: #1976d2;
  }
  .site-card-content p {
    font-size: 1rem;
    color: #666;
    margin-bottom: 1rem;
  }
  /* Buttons on Site Cards */
  .viewButton, .nearbyButton, .favoriteButton, .shareButton, .reviewButton {
    background: #1976d2;
    color: #fff;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s ease;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
  }
  .viewButton:hover, .nearbyButton:hover, .favoriteButton:hover, .shareButton:hover, .reviewButton:hover {
    background-color: #1565c0;
  }
  
  /* Modal Styles */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 900;
    display: none;
  }
  .modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 500px;
    z-index: 1000;
    display: none;
    animation: fadeIn 0.4s ease;
  }
  @keyframes fadeIn {
    from { opacity: 0; transform: translate(-50%, -45%); }
    to { opacity: 1; transform: translate(-50%, -50%); }
  }
  .modal h2 {
    margin-bottom: 1rem;
    font-size: 2rem;
    color: #1976d2;
  }
  .modal p {
    margin-bottom: 1.5rem;
    color: #555;
    font-size: 1rem;
  }
  .modal button {
    background: #1976d2;
    color: #fff;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s ease;
    margin-right: 1rem;
  }
  .modal button:hover {
    background-color: #1565c0;
  }
  
  /* Nearby Modal Specific Styles */
  .nearbyItem {
    padding: 0.5rem 0;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .nearbyItem:last-child {
    border-bottom: none;
  }
  
  /* Favorites Modal Styles */
  .favoritesItem {
    padding: 0.5rem 0;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .favoritesItem:last-child {
    border-bottom: none;
  }
  
  /* Reviews Modal Styles */
  .reviewsItem {
    padding: 0.5rem 0;
    border-bottom: 1px solid #ddd;
  }
  .reviewsItem:last-child {
    border-bottom: none;
  }
  #newReview {
    width: 100%;
    height: 80px;
    margin-top: 1rem;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
  }
  
  /* Chatbot Styles */
  #chatbotContainer {
    position: fixed;
    bottom: 20px;
    right: 80px;
    width: 320px;
    max-height: 400px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    display: none;
    flex-direction: column;
    overflow: hidden;
    z-index: 1000;
  }
  #chatbotHeader {
    background: #1976d2;
    color: #fff;
    padding: 0.75rem;
    text-align: center;
    font-size: 1.2rem;
  }
  #chatbotMessages {
    padding: 1rem;
    overflow-y: auto;
    flex-grow: 1;
    background: #f9f9f9;
  }
  #chatbotMessages div {
    margin-bottom: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    max-width: 80%;
    word-wrap: break-word;
  }
  #chatbotMessages div.user {
    background: #d1eaff;
    margin-left: auto;
    text-align: right;
  }
  #chatbotMessages div.bot {
    background: #e8f5e9;
    margin-right: auto;
    text-align: left;
  }
  #chatbotInputContainer {
    display: flex;
    border-top: 1px solid #ddd;
  }
  #chatbotInput {
    flex: 1;
    padding: 0.75rem;
    border: none;
    font-size: 1rem;
    outline: none;
  }
  #chatbotSendButton {
    background: #1976d2;
    color: #fff;
    border: none;
    padding: 0 1rem;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s ease;
  }
  #chatbotSendButton:hover {
    background-color: #1565c0;
  }
  #chatbotToggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #1976d2;
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 55px;
    height: 55px;
    cursor: pointer;
    font-size: 1.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    transition: background-color 0.3s ease;
  }
  #chatbotToggle:hover {
    background-color: #1565c0;
  }
  
  /* Back-to-Top Button */
  #backToTop {
    position: fixed;
    bottom: 90px;
    right: 20px;
    background: #1976d2;
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    cursor: pointer;
    font-size: 1.2rem;
    display: none;
    justify-content: center;
    align-items: center;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    transition: background-color 0.3s ease;
  }
  #backToTop:hover {
    background-color: #1565c0;
  }
  
  /* Footer Styles */
  footer {
    background: linear-gradient(135deg, #8c42e6, #5bb3f6);
    color: #fff;
    text-align: center;
    padding: 1rem 0;
    font-size: 1rem;
    margin-top: 2rem;
  }
  
  /* Responsive Design */
  @media (max-width: 768px) {
    header h1 {
      font-size: 2rem;
    }
    .search-container {
      flex-direction: column;
    }
    #micButton, #currentLocationButton {
      margin-top: 0.5rem;
    }
    #chatbotContainer {
      width: 90%;
      right: 5%;
    }
  }