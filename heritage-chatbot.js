/**
 * Heritage Explorer Chatbot
 * Combined functionality from chat bot.js and chat fix.js
 */

// Site keywords for better matching
const siteKeywords = {
  "brihadeeswarar temple": ["big temple", "thanjavur temple", "rajarajeswaram", "peruvudaiyar kovil", "brihadeeswara", "thanjai periya kovil"],
  "meenakshi amman temple": ["meenakshi", "madurai temple", "fish-eyed goddess", "meenakshi sundareswarar"],
  "shore temple": ["mahabalipuram temple", "mamallapuram", "seven pagodas"],
  "gangaikonda cholapuram": ["gangaikonda", "gangaikondacholapuram", "brihadisvara temple"],
  "airavatesvara temple": ["darasuram temple", "airavateswara", "dharasuram"],
  "vivekananda rock memorial": ["kanyakumari rock", "vivekananda memorial", "meditation rock"],
  "gingee fort": ["senji fort", "gingee fortress", "krishnagiri fort", "rajagiri fort"],
  "chettinad palace": ["chettinad mansion", "karaikudi palace", "chettinad heritage"],
  "madurai palace": ["thirumalai nayakkar mahal", "thirumalai nayak palace"],
  "thanjavur palace": ["thanjavur maratha palace", "saraswathi mahal", "saraswati mahal library"],
  "kodaikanal": ["princess of hill stations", "kodai lake", "kodai hills", "kodai"],
  "ooty": ["udhagamandalam", "nilgiri hills", "ooty lake", "nilgiri mountain railway"],
  "yercaud": ["shevaroy hills", "yercaud lake", "lady's seat"],
  "kolli hills": ["kolli malai", "mountain of death", "agaya gangai"],
  "valparai": ["anamalai hills", "valparai tea estates"],
  "dakshinachitra museum": ["dakshinachitra heritage museum", "living museum", "south indian heritage"],
  "government museum chennai": ["egmore museum", "chennai museum", "madras museum"],
  "thanjavur art gallery": ["thanjavur royal palace museum", "saraswathi mahal art gallery"],
  "marina beach": ["chennai beach", "longest urban beach", "marina"],
  "covelong beach": ["kovalam beach tamil nadu", "covelong point"],
  "elliots beach": ["besant nagar beach", "bessie beach", "elliot's beach"],
  "silver beach": ["cuddalore beach", "silver beach cuddalore"]
};

// Heritage information for detailed responses
const heritageInfo = {
  "temples": {
    "general": "Tamil Nadu is famous for its magnificent temples with Dravidian architecture. The state has over 33,000 ancient temples, many of which are architectural marvels.",
    "count": "There are over 33,000 ancient temples in Tamil Nadu.",
    "famous": "The most famous temples include Brihadeeswarar Temple in Thanjavur, Meenakshi Amman Temple in Madurai, and Shore Temple in Mahabalipuram.",
    "architecture": "Tamil Nadu temples feature Dravidian architecture characterized by pyramid-shaped towers, elaborate carvings, and stone sculptures.",
    "unesco": "Several temple complexes in Tamil Nadu are UNESCO World Heritage Sites, including the Great Living Chola Temples and the Group of Monuments at Mahabalipuram."
  },
  "beaches": {
    "general": "Tamil Nadu has a coastline of about 1,076 km along the Bay of Bengal, featuring several beautiful beaches.",
    "count": "Tamil Nadu has more than 25 significant beaches along its coastline.",
    "famous": "The most famous beaches include Marina Beach in Chennai (the second-longest urban beach in the world), Covelong Beach, Elliots Beach, and Silver Beach in Cuddalore.",
    "activities": "Visitors can enjoy swimming (in designated areas), sunbathing, beach sports, horse riding, and water sports at many Tamil Nadu beaches.",
    "best_time": "The best time to visit Tamil Nadu beaches is from November to February when the weather is pleasant."
  },
  "hill_stations": {
    "general": "Tamil Nadu's hill stations offer cool retreats from the tropical heat, featuring lush landscapes and colonial heritage.",
    "count": "Tamil Nadu has more than 10 popular hill stations.",
    "famous": "The most famous hill stations include Ooty (Udhagamandalam), Kodaikanal, Yercaud, Kolli Hills, and Valparai.",
    "activities": "Visitors can enjoy trekking, boating in lakes, visiting botanical gardens, and experiencing the cool climate and scenic beauty.",
    "best_time": "The best time to visit Tamil Nadu hill stations is from March to June when the weather is pleasant and not too cold."
  },
  "monuments": {
    "general": "Tamil Nadu has numerous historical monuments showcasing its rich cultural heritage and architectural brilliance.",
    "count": "There are hundreds of historical monuments across Tamil Nadu.",
    "famous": "Famous monuments include Vivekananda Rock Memorial in Kanyakumari, Gingee Fort, and various colonial structures.",
    "architecture": "The monuments display a mix of Dravidian, colonial, and Indo-Saracenic architectural styles.",
    "preservation": "Many monuments are protected by the Archaeological Survey of India and are being preserved for their historical significance."
  },
  "palaces": {
    "general": "Tamil Nadu's palaces reflect the grandeur of various dynasties that ruled the region, including the Cholas, Pandyas, Nayaks, and Marathas.",
    "count": "There are more than 15 significant palaces in Tamil Nadu.",
    "famous": "Famous palaces include Thirumalai Nayakkar Mahal in Madurai, Thanjavur Palace, and Chettinad Palace.",
    "architecture": "The palaces showcase a blend of Dravidian, Islamic, and European architectural elements.",
    "current_use": "Many palaces now serve as museums, government offices, or tourist attractions."
  },
  "museums": {
    "general": "Tamil Nadu's museums house rich collections of artifacts, art, and historical items that showcase the state's cultural heritage.",
    "count": "There are more than 20 significant museums across Tamil Nadu.",
    "famous": "Famous museums include Government Museum in Chennai, DakshinaChitra Museum, and Thanjavur Art Gallery.",
    "collections": "The museums house collections of bronze sculptures, stone carvings, paintings, coins, and archaeological artifacts.",
    "visiting_hours": "Most museums are open from 9:30 AM to 5:00 PM and are closed on certain government holidays."
  }
};

// Place descriptions for detailed information
const placeDescriptions = {
  "brihadeeswarar temple": "Built by Raja Raja Chola I in 1010 CE, this UNESCO World Heritage Site is one of the largest temples in India. The temple is dedicated to Lord Shiva and features a 216-foot tall vimana (tower) made from a single granite block weighing 80 tons. The temple complex covers 25 acres and is surrounded by a moat.",
  "meenakshi amman temple": "This historic Hindu temple is dedicated to Goddess Meenakshi (a form of Parvati) and Lord Sundareswarar (a form of Shiva). The temple features 14 gateway towers (gopurams), with the tallest being 170 feet high. The temple has a sacred tank called the Golden Lotus Tank and a hall with 1,000 pillars.",
  "shore temple": "Built in the 8th century during the reign of Narasimhavarman II, this UNESCO World Heritage Site is one of the oldest structural stone temples in South India. Located in Mahabalipuram, it faces the Bay of Bengal and features intricate carvings of Shiva, Parvati, and Vishnu.",
  "vivekananda rock memorial": "Built in 1970 on a rock island off the coast of Kanyakumari, this memorial marks the spot where Swami Vivekananda meditated in 1892. The memorial consists of two main structures - the Vivekananda Mandapam and the Shripada Mandapam.",
  "gingee fort": "Also known as Senji Fort, this 13th-century fort complex was called the 'Troy of the East' by the British. It consists of three fortified hills - Krishnagiri, Rajagiri, and Chandrayanadurg - connected by walls. The fort features temples, granaries, and a seven-story marriage hall.",
  "chettinad palace": "Located in Karaikudi, this palace showcases the opulent lifestyle of the Chettiar community. Built in the early 20th century, it features a blend of European and traditional Tamil architecture with imported materials from around the world.",
  "madurai palace": "Thirumalai Nayakkar Mahal was built in 1636 by King Thirumalai Nayak. Only one-quarter of the original palace remains today, featuring massive pillars, stucco work, and a large courtyard. The palace is known for its sound and light show that narrates the history of Madurai.",
  "thanjavur palace": "Built by the Nayaks and later expanded by the Marathas, this palace complex houses the Saraswathi Mahal Library with over 30,000 rare manuscripts. The palace features Darbar Hall, Bell Tower, and Saraswathi Mahal Art Gallery.",
  "kodaikanal": "Known as the 'Princess of Hill Stations,' Kodaikanal sits at an elevation of 7,200 feet in the Palani Hills. It features a star-shaped lake, beautiful waterfalls like Silver Cascade and Bear Shola, and scenic viewpoints like Pillar Rocks and Coaker's Walk.",
  "ooty": "Established as a British summer resort in the 19th century, Ooty (Udhagamandalam) is located in the Nilgiri Hills at an elevation of 7,500 feet. It features the Botanical Gardens, Ooty Lake, and the Nilgiri Mountain Railway (a UNESCO World Heritage Site).",
  "kolli hills": "Known as 'Mountain of Death' due to its 70 hairpin bends, Kolli Hills rises to 4,300 feet. It features the Agaya Gangai waterfall, Arapaleeswarar Temple, and medicinal herb farms. The hills are mentioned in ancient Tamil literature.",
  "dakshinachitra museum": "Located near Chennai, this living museum showcases the art, architecture, and folk traditions of South India. It features reconstructed traditional houses from different South Indian states, craft demonstrations, and cultural performances.",
  "thanjavur art gallery": "Located in the Thanjavur Palace complex, this gallery houses a collection of bronze and stone sculptures from the 7th to 18th centuries. It also features Chola and Nayak paintings, including the famous Thanjavur paintings with gold leaf work.",
  "marina beach": "Stretching 13 km along the Bay of Bengal in Chennai, Marina Beach is the second-longest urban beach in the world. It features the iconic lighthouse, statues of Tamil icons, and the Indo-Saracenic style buildings of the University of Madras.",
  "covelong beach": "Located 40 km from Chennai, this former fishing village is now a popular surfing destination. The beach features the historic Covelong Fort (now a luxury resort) built by the Nawab of Carnatic in the 18th century.",
  "elliots beach": "Located in the Besant Nagar area of Chennai, this beach is less crowded than Marina Beach. It features the Karl Schmidt Memorial, water sports facilities, and a vibrant food scene with cafes and restaurants.",
  "silver beach": "Located in Cuddalore, this 2 km long beach is known for its silver-like sand. It features the historic Cuddalore Fort, a children's park, and boating facilities."
};

// Common questions and answers for quick responses
const commonQnA = {
  "best time to visit": "The best time to visit Tamil Nadu is from October to March when the weather is pleasant. For hill stations like Ooty and Kodaikanal, April to June is ideal.",
  "how to reach": "Tamil Nadu is well-connected by air (international airports in Chennai, Coimbatore, Madurai, and Trichy), rail (major stations across the state), and road (extensive bus network and highways).",
  "local food": "Tamil Nadu is famous for its South Indian cuisine including idli, dosa, vada, sambar, rasam, and filter coffee. Don't miss regional specialties like Chettinad cuisine, Madurai Jigarthanda, and Tamil Nadu's variety of sweets.",
  "accommodation": "Tamil Nadu offers a wide range of accommodation options from luxury hotels to budget guesthouses. Major cities and tourist destinations have good hotel infrastructure.",
  "transportation": "Within cities, options include auto-rickshaws, taxis, app-based cabs, and public buses. For intercity travel, state-run TNSTC buses, private buses, trains, and taxis are available.",
  "shopping": "Tamil Nadu is known for silk sarees (especially Kanchipuram silk), bronze sculptures, Thanjavur paintings, wooden toys, palm leaf crafts, and handloom textiles.",
  "festivals": "Major festivals include Pongal (January), Tamil New Year (April), Madurai Chithirai Festival, Mahamaham Festival in Kumbakonam (once in 12 years), and various temple festivals throughout the year.",
  "languages": "Tamil is the official language. English is widely understood in urban areas and tourist destinations. Some people also speak Hindi, Telugu, Malayalam, and Kannada.",
  "weather": "Tamil Nadu has a tropical climate. Coastal areas are hot and humid with temperatures between 25°C-40°C. Hill stations are cooler with temperatures between 5°C-25°C depending on the season.",
  "culture": "Tamil Nadu has one of the oldest continuous civilizations in India. It's known for classical dance (Bharatanatyam), music (Carnatic), literature, and traditional arts and crafts."
};

// Function to get the best match using fuzzy matching
function getBestMatch(userInput, possibleMatches) {
  userInput = userInput.toLowerCase();
  let bestMatch = null;
  let highestScore = 0;

  for (const match of Object.keys(possibleMatches)) {
    // Check exact match first
    if (userInput.includes(match)) {
      return match;
    }

    // Check keywords
    const keywords = possibleMatches[match];
    for (const keyword of keywords) {
      if (userInput.includes(keyword)) {
        return match;
      }
    }

    // Calculate similarity score
    let score = 0;
    const matchWords = match.split(' ');
    const inputWords = userInput.split(' ');

    for (const matchWord of matchWords) {
      if (matchWord.length <= 3) continue; // Skip short words

      for (const inputWord of inputWords) {
        if (inputWord.length <= 3) continue; // Skip short words

        if (matchWord.includes(inputWord) || inputWord.includes(matchWord)) {
          score += 1;
        } else if (levenshteinDistance(matchWord, inputWord) <= 2) {
          score += 0.5;
        }
      }
    }

    if (score > highestScore) {
      highestScore = score;
      bestMatch = match;
    }
  }

  return highestScore >= 1 ? bestMatch : null;
}

// Levenshtein distance for fuzzy matching
function levenshteinDistance(a, b) {
  const matrix = [];

  // Initialize matrix
  for (let i = 0; i <= b.length; i++) {
    matrix[i] = [i];
  }

  for (let j = 0; j <= a.length; j++) {
    matrix[0][j] = j;
  }

  // Fill matrix
  for (let i = 1; i <= b.length; i++) {
    for (let j = 1; j <= a.length; j++) {
      if (b.charAt(i-1) === a.charAt(j-1)) {
        matrix[i][j] = matrix[i-1][j-1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i-1][j-1] + 1, // substitution
          matrix[i][j-1] + 1,   // insertion
          matrix[i-1][j] + 1    // deletion
        );
      }
    }
  }

  return matrix[b.length][a.length];
}

// Function to identify question type
function identifyQuestionType(userInput) {
  userInput = userInput.toLowerCase();

  // Check for category questions
  const categories = {
    "temple": ["temple", "temples", "kovil", "koil", "கோவில்", "கோயில்"],
    "beach": ["beach", "beaches", "seashore", "coast", "கடற்கரை"],
    "hill station": ["hill", "hills", "mountain", "mountains", "மலை"],
    "monument": ["monument", "monuments", "memorial", "நினைவுச்சின்னம்"],
    "palace": ["palace", "palaces", "mahal", "அரண்மனை"],
    "museum": ["museum", "museums", "gallery", "அருங்காட்சியகம்"]
  };

  for (const [category, keywords] of Object.entries(categories)) {
    for (const keyword of keywords) {
      if (userInput.includes(keyword)) {
        return { type: "category", category: category };
      }
    }
  }

  // Check for specific place
  const matchedPlace = getBestMatch(userInput, siteKeywords);
  if (matchedPlace) {
    return { type: "place", place: matchedPlace };
  }

  // Check for common questions
  for (const question of Object.keys(commonQnA)) {
    if (userInput.includes(question)) {
      return { type: "common", question: question };
    }
  }

  // Check for greetings
  if (userInput.match(/\b(hi|hello|hey|greetings|வணக்கம்)\b/)) {
    return { type: "greeting" };
  }

  // Check for thank you
  if (userInput.match(/\b(thanks|thank you|thank|நன்றி)\b/)) {
    return { type: "thanks" };
  }

  // Default
  return { type: "unknown" };
}

// Main function to get chatbot response
function getChatbotResponse(userInput, language = "en") {
  const questionInfo = identifyQuestionType(userInput);

  switch (questionInfo.type) {
    case "greeting":
      return language === "en"
        ? "Hello! I'm your Tamil Nadu Heritage Explorer assistant. How can I help you discover the rich cultural heritage of Tamil Nadu today?"
        : "வணக்கம்! நான் உங்கள் தமிழ்நாடு பாரம்பரிய ஆய்வாளர் உதவியாளர். இன்று தமிழ்நாட்டின் வளமான கலாச்சார பாரம்பரியத்தைக் கண்டறிய நான் உங்களுக்கு எவ்வாறு உதவ முடியும்?";

    case "thanks":
      return language === "en"
        ? "You're welcome! Feel free to ask if you have any more questions about Tamil Nadu's heritage."
        : "வரவேற்கிறேன்! தமிழ்நாட்டின் பாரம்பரியம் குறித்து உங்களுக்கு மேலும் கேள்விகள் இருந்தால் கேட்கவும்.";

    case "category":
      const categoryKey = questionInfo.category.replace(" ", "_");
      if (language === "en") {
        const categoryInfo = heritageInfo[categoryKey + "s"] || {};
        const places = getCategoryPlaces(questionInfo.category);
        return `${categoryInfo.general || `Tamil Nadu has several beautiful ${questionInfo.category}s.`} ${categoryInfo.famous || ''} Some notable ${questionInfo.category}s include: ${places.join(", ")}. Would you like to know more about any specific one?`;
      } else {
        const places = getCategoryPlaces(questionInfo.category);
        return `தமிழ்நாட்டில் பல அழகான ${questionInfo.category}கள் உள்ளன. குறிப்பிடத்தக்க ${questionInfo.category}களில் ${places.join(", ")} ஆகியவை அடங்கும். ஏதேனும் ஒரு குறிப்பிட்ட இடத்தைப் பற்றி மேலும் அறிய விரும்புகிறீர்களா?`;
      }

    case "place":
      if (language === "en") {
        return placeDescriptions[questionInfo.place] || `I don't have detailed information about ${questionInfo.place} yet, but it's a beautiful heritage site in Tamil Nadu.`;
      } else {
        return `${questionInfo.place} தமிழ்நாட்டின் ஒரு அழகான பாரம்பரிய தளமாகும். இது பற்றிய விரிவான தகவல்களை விரைவில் சேர்ப்போம்.`;
      }

    case "common":
      return language === "en"
        ? commonQnA[questionInfo.question]
        : `${questionInfo.question} பற்றிய தகவல் தமிழில் விரைவில் சேர்க்கப்படும்.`;

    case "unknown":
    default:
      return language === "en"
        ? "I'm here to help you discover Tamil Nadu's heritage sites. You can ask about temples, beaches, hill stations, monuments, museums, or palaces. Or ask about a specific site like Brihadeeswarar Temple or Marina Beach!"
        : "தமிழக பாரம்பரிய இடங்களைக் கண்டறிய நான் உங்களுக்கு உதவுகிறேன். கோவில்கள், கடற்கரைகள், மலை நிலையங்கள், நினைவுச்சின்னங்கள், அருங்காட்சியகங்கள் அல்லது அரண்மனைகள் பற்றி கேட்கலாம். அல்லது பிரகதீஸ்வரர் கோவில் அல்லது மெரினா கடற்கரை போன்ற குறிப்பிட்ட இடத்தைப் பற்றி கேளுங்கள்!";
  }
}

// Helper function to get places in a category
function getCategoryPlaces(category) {
  const places = [];

  // Map category to its plural form used in heritageInfo
  const categoryKey = category.replace(" ", "_") + "s";

  // Get places from placeDescriptions that match the category
  for (const [place, description] of Object.entries(placeDescriptions)) {
    if (category === "temple" && place.includes("temple")) {
      places.push(capitalizeWords(place));
    } else if (category === "beach" && place.includes("beach")) {
      places.push(capitalizeWords(place));
    } else if (category === "hill station" && ["kodaikanal", "ooty", "yercaud", "kolli hills", "valparai"].includes(place)) {
      places.push(capitalizeWords(place));
    } else if (category === "monument" && ["vivekananda rock memorial", "gingee fort"].includes(place)) {
      places.push(capitalizeWords(place));
    } else if (category === "palace" && place.includes("palace")) {
      places.push(capitalizeWords(place));
    } else if (category === "museum" && place.includes("museum")) {
      places.push(capitalizeWords(place));
    }
  }

  return places;
}

// Helper function to capitalize words
function capitalizeWords(str) {
  return str.replace(/\b\w/g, char => char.toUpperCase());
}

// Export the main function for use in the website
window.getHeritageChatbotResponse = getChatbotResponse;

// Log to confirm script is loaded
console.log("Heritage Chatbot script loaded successfully!");
