# Heritage Explorer - PowerShell Runner
# Run with: powershell -ExecutionPolicy Bypass -File run-terminal.ps1

param(
    [switch]$Force,
    [switch]$SkipBrowser
)

# Colors for output
$colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Cyan = "Cyan"
}

function Write-Status {
    param(
        [string]$Type,
        [string]$Message
    )
    
    switch ($Type) {
        "OK" { Write-Host "[OK] $Message" -ForegroundColor Green }
        "ERROR" { Write-Host "[ERROR] $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "[WARNING] $Message" -ForegroundColor Yellow }
        "INFO" { Write-Host "[INFO] $Message" -ForegroundColor Blue }
    }
}

# Clear screen
Clear-Host

Write-Host "==========================================" -ForegroundColor Blue
Write-Host "   Heritage Explorer - PowerShell Runner" -ForegroundColor Blue
Write-Host "==========================================" -ForegroundColor Blue
Write-Host

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if ($isAdmin) {
    Write-Status "INFO" "Running with administrator privileges"
} else {
    Write-Status "WARNING" "Not running as administrator - some operations may fail"
}

# Step 1: Check XAMPP installation
Write-Host
Write-Host "[STEP 1] Checking XAMPP installation..."

$xamppPath = "C:\xampp"
$xamppControl = "$xamppPath\xampp-control.exe"

if (Test-Path $xamppControl) {
    Write-Status "OK" "XAMPP found at $xamppPath"
} else {
    Write-Status "ERROR" "XAMPP not found!"
    Write-Host
    Write-Host "Please install XAMPP first:"
    Write-Host "1. Download from: https://www.apachefriends.org/"
    Write-Host "2. Install to C:\xampp\"
    Write-Host "3. Run this script again"
    Write-Host
    Write-Status "INFO" "Opening download page..."
    Start-Process "https://www.apachefriends.org/download.html"
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 2: Create project directory
Write-Host
Write-Host "[STEP 2] Creating project directory..."

$projectDir = "$xamppPath\htdocs\heritage-explorer"

if (!(Test-Path $projectDir)) {
    try {
        New-Item -ItemType Directory -Path $projectDir -Force | Out-Null
        Write-Status "OK" "Created $projectDir"
    } catch {
        Write-Status "ERROR" "Failed to create project directory: $($_.Exception.Message)"
        exit 1
    }
} else {
    Write-Status "OK" "Directory already exists"
}

# Step 3: Copy project files
Write-Host
Write-Host "[STEP 3] Copying project files..."

$currentDir = Get-Location
Write-Status "INFO" "Current directory: $currentDir"
Write-Status "INFO" "Target directory: $projectDir"

try {
    # Copy all files from current directory to project directory
    Get-ChildItem -Path $currentDir -Recurse | ForEach-Object {
        $targetPath = $_.FullName.Replace($currentDir, $projectDir)
        if ($_.PSIsContainer) {
            if (!(Test-Path $targetPath)) {
                New-Item -ItemType Directory -Path $targetPath -Force | Out-Null
            }
        } else {
            $targetDir = Split-Path $targetPath -Parent
            if (!(Test-Path $targetDir)) {
                New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
            }
            Copy-Item $_.FullName $targetPath -Force
        }
    }
    Write-Status "OK" "Files copied successfully"
} catch {
    Write-Status "WARNING" "Some files may not have been copied: $($_.Exception.Message)"
}

# Step 4: Start XAMPP services
Write-Host
Write-Host "[STEP 4] Starting XAMPP services..."

# Function to start XAMPP service
function Start-XamppService {
    param([string]$ServiceName)
    
    try {
        $process = Start-Process -FilePath $xamppControl -ArgumentList "-s$ServiceName" -PassThru -WindowStyle Hidden
        Start-Sleep -Seconds 2
        Write-Status "OK" "$ServiceName start command executed"
    } catch {
        Write-Status "WARNING" "Could not start $ServiceName automatically: $($_.Exception.Message)"
    }
}

# Start Apache
Write-Status "INFO" "Starting Apache..."
Start-XamppService "apache"

# Start MySQL
Write-Status "INFO" "Starting MySQL..."
Start-XamppService "mysql"

# Alternative method: Start XAMPP Control Panel
Write-Status "INFO" "Starting XAMPP Control Panel..."
try {
    Start-Process $xamppControl -WindowStyle Normal
    Write-Status "OK" "XAMPP Control Panel opened"
} catch {
    Write-Status "WARNING" "Could not open XAMPP Control Panel"
}

# Step 5: Wait for services
Write-Host
Write-Host "[STEP 5] Waiting for services to start..."
Start-Sleep -Seconds 5

# Step 6: Test server connection
Write-Host
Write-Host "[STEP 6] Testing server connection..."

try {
    $response = Invoke-WebRequest -Uri "http://localhost/heritage-explorer/website.html" -TimeoutSec 10 -ErrorAction Stop
    if ($response.StatusCode -eq 200) {
        Write-Status "OK" "Server is responding"
    } else {
        Write-Status "WARNING" "Server responded with status: $($response.StatusCode)"
    }
} catch {
    Write-Status "WARNING" "Server may not be ready yet: $($_.Exception.Message)"
}

# Step 7: Setup database
Write-Host
Write-Host "[STEP 7] Setting up database..."

if (!$SkipBrowser) {
    Write-Status "INFO" "Opening database setup in browser..."
    Start-Process "http://localhost/heritage-explorer/setup-database.php"
    
    Write-Host
    Write-Host "Please wait for database setup to complete, then press Enter to continue..."
    Read-Host
}

# Step 8: Launch project
Write-Host
Write-Host "[STEP 8] Launching project..."

if (!$SkipBrowser) {
    Start-Sleep -Seconds 2
    Start-Process "http://localhost/heritage-explorer/website.html"
}

# Final output
Write-Host
Write-Host "==========================================" -ForegroundColor Green
Write-Host "   PROJECT LAUNCHED SUCCESSFULLY!" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green
Write-Host

Write-Host "Your Heritage Explorer is now running at:"
Write-Host "http://localhost/heritage-explorer/website.html" -ForegroundColor Cyan
Write-Host

Write-Host "Available endpoints:"
Write-Host "- Main Site: http://localhost/heritage-explorer/website.html"
Write-Host "- Login:     http://localhost/heritage-explorer/login.html"
Write-Host "- Signup:    http://localhost/heritage-explorer/signup.html"
Write-Host "- DB Test:   http://localhost/heritage-explorer/test-connection.php"
Write-Host "- Status:    http://localhost/heritage-explorer/project-status.html"
Write-Host

Write-Host "Admin credentials:"
Write-Host "Username: admin"
Write-Host "Password: admin123"
Write-Host

Write-Host "XAMPP Control Panel is open - make sure Apache and MySQL are running (green status)"
Write-Host

if (!$SkipBrowser) {
    Read-Host "Press Enter to exit"
}
