#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Clear screen
clear

echo -e "${BLUE}"
echo "=========================================="
echo "   Heritage Explorer - Terminal Runner"
echo "=========================================="
echo -e "${NC}"

# Function to print colored output
print_status() {
    case $1 in
        "OK") echo -e "${GREEN}[OK]${NC} $2" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $2" ;;
        "WARNING") echo -e "${YELLOW}[WARNING]${NC} $2" ;;
        "INFO") echo -e "${BLUE}[INFO]${NC} $2" ;;
    esac
}

# Check if running on macOS or Linux
OS="$(uname -s)"
case "${OS}" in
    Linux*)     MACHINE=Linux;;
    Darwin*)    MACHINE=Mac;;
    *)          MACHINE="UNKNOWN:${OS}"
esac

print_status "INFO" "Detected OS: $MACHINE"

# Step 1: Check for web server
echo
echo "[STEP 1] Checking for web server..."

if command -v apache2 &> /dev/null; then
    print_status "OK" "Apache2 found"
    WEB_SERVER="apache2"
elif command -v httpd &> /dev/null; then
    print_status "OK" "Apache httpd found"
    WEB_SERVER="httpd"
elif command -v nginx &> /dev/null; then
    print_status "OK" "Nginx found"
    WEB_SERVER="nginx"
else
    print_status "ERROR" "No web server found!"
    echo
    echo "Please install a web server:"
    if [[ "$MACHINE" == "Mac" ]]; then
        echo "1. Install XAMPP: https://www.apachefriends.org/"
        echo "2. Or install via Homebrew: brew install apache2 mysql php"
    else
        echo "1. Ubuntu/Debian: sudo apt install apache2 mysql-server php"
        echo "2. CentOS/RHEL: sudo yum install httpd mysql-server php"
        echo "3. Or install XAMPP: https://www.apachefriends.org/"
    fi
    exit 1
fi

# Step 2: Check for MySQL
echo
echo "[STEP 2] Checking for MySQL..."

if command -v mysql &> /dev/null; then
    print_status "OK" "MySQL found"
elif command -v mariadb &> /dev/null; then
    print_status "OK" "MariaDB found"
else
    print_status "WARNING" "MySQL not found in PATH"
fi

# Step 3: Check for PHP
echo
echo "[STEP 3] Checking for PHP..."

if command -v php &> /dev/null; then
    PHP_VERSION=$(php -v | head -n1 | cut -d' ' -f2)
    print_status "OK" "PHP found (version: $PHP_VERSION)"
else
    print_status "ERROR" "PHP not found!"
    echo
    echo "Please install PHP:"
    if [[ "$MACHINE" == "Mac" ]]; then
        echo "brew install php"
    else
        echo "sudo apt install php (Ubuntu/Debian)"
        echo "sudo yum install php (CentOS/RHEL)"
    fi
    exit 1
fi

# Step 4: Determine web root directory
echo
echo "[STEP 4] Setting up project directory..."

if [[ "$MACHINE" == "Mac" ]]; then
    if [ -d "/Applications/XAMPP" ]; then
        WEB_ROOT="/Applications/XAMPP/htdocs"
    else
        WEB_ROOT="/usr/local/var/www"
    fi
else
    if [ -d "/opt/lampp" ]; then
        WEB_ROOT="/opt/lampp/htdocs"
    elif [ -d "/var/www/html" ]; then
        WEB_ROOT="/var/www/html"
    else
        WEB_ROOT="/var/www"
    fi
fi

PROJECT_DIR="$WEB_ROOT/heritage-explorer"

print_status "INFO" "Web root: $WEB_ROOT"
print_status "INFO" "Project directory: $PROJECT_DIR"

# Create project directory
if [ ! -d "$PROJECT_DIR" ]; then
    sudo mkdir -p "$PROJECT_DIR" 2>/dev/null || mkdir -p "$PROJECT_DIR"
    if [ $? -eq 0 ]; then
        print_status "OK" "Created project directory"
    else
        print_status "ERROR" "Failed to create project directory"
        print_status "INFO" "Try running with sudo or check permissions"
        exit 1
    fi
else
    print_status "OK" "Project directory exists"
fi

# Step 5: Copy project files
echo
echo "[STEP 5] Copying project files..."

# Copy all files from current directory to project directory
sudo cp -r ./* "$PROJECT_DIR/" 2>/dev/null || cp -r ./* "$PROJECT_DIR/"
if [ $? -eq 0 ]; then
    print_status "OK" "Files copied successfully"
else
    print_status "WARNING" "Some files may not have been copied"
    print_status "INFO" "You may need to copy files manually with appropriate permissions"
fi

# Set proper permissions
sudo chmod -R 755 "$PROJECT_DIR" 2>/dev/null || chmod -R 755 "$PROJECT_DIR"
sudo chown -R www-data:www-data "$PROJECT_DIR" 2>/dev/null || true

# Step 6: Start services
echo
echo "[STEP 6] Starting web services..."

# Start web server
case $WEB_SERVER in
    "apache2")
        sudo systemctl start apache2 2>/dev/null || sudo service apache2 start 2>/dev/null
        if [ $? -eq 0 ]; then
            print_status "OK" "Apache2 started"
        else
            print_status "WARNING" "Could not start Apache2 automatically"
        fi
        ;;
    "httpd")
        sudo systemctl start httpd 2>/dev/null || sudo service httpd start 2>/dev/null
        if [ $? -eq 0 ]; then
            print_status "OK" "Apache httpd started"
        else
            print_status "WARNING" "Could not start Apache httpd automatically"
        fi
        ;;
    "nginx")
        sudo systemctl start nginx 2>/dev/null || sudo service nginx start 2>/dev/null
        if [ $? -eq 0 ]; then
            print_status "OK" "Nginx started"
        else
            print_status "WARNING" "Could not start Nginx automatically"
        fi
        ;;
esac

# Start MySQL
sudo systemctl start mysql 2>/dev/null || sudo service mysql start 2>/dev/null || sudo systemctl start mariadb 2>/dev/null
if [ $? -eq 0 ]; then
    print_status "OK" "MySQL/MariaDB started"
else
    print_status "WARNING" "Could not start MySQL automatically"
fi

# Step 7: Wait for services
echo
echo "[STEP 7] Waiting for services to start..."
sleep 3

# Step 8: Test server
echo
echo "[STEP 8] Testing server connection..."

if curl -s http://localhost/heritage-explorer/website.html > /dev/null 2>&1; then
    print_status "OK" "Server is responding"
else
    print_status "WARNING" "Server may not be ready yet"
fi

# Step 9: Setup database
echo
echo "[STEP 9] Setting up database..."
print_status "INFO" "Opening database setup in browser..."

# Try to open browser
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost/heritage-explorer/setup-database.php
elif command -v open &> /dev/null; then
    open http://localhost/heritage-explorer/setup-database.php
else
    print_status "INFO" "Please open: http://localhost/heritage-explorer/setup-database.php"
fi

# Step 10: Launch project
echo
echo "[STEP 10] Launching project..."
sleep 2

if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost/heritage-explorer/website.html
elif command -v open &> /dev/null; then
    open http://localhost/heritage-explorer/website.html
else
    print_status "INFO" "Please open: http://localhost/heritage-explorer/website.html"
fi

echo
echo -e "${GREEN}"
echo "=========================================="
echo "   PROJECT LAUNCHED SUCCESSFULLY!"
echo "=========================================="
echo -e "${NC}"
echo
echo "Your Heritage Explorer is now running at:"
echo "http://localhost/heritage-explorer/website.html"
echo
echo "Available endpoints:"
echo "- Main Site: http://localhost/heritage-explorer/website.html"
echo "- Login:     http://localhost/heritage-explorer/login.html"
echo "- Signup:    http://localhost/heritage-explorer/signup.html"
echo "- DB Test:   http://localhost/heritage-explorer/test-connection.php"
echo "- Status:    http://localhost/heritage-explorer/project-status.html"
echo
echo "Admin credentials:"
echo "Username: admin"
echo "Password: admin123"
echo
echo "Press Enter to exit..."
read
