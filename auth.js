// Authentication JavaScript for Heritage Explorer

// Global variables
let csrfToken = '';

// Initialize authentication system
document.addEventListener('DOMContentLoaded', function() {
    fetchCSRFToken();
    checkUserSession();
});

// Fetch CSRF token
async function fetchCSRFToken() {
    try {
        const response = await fetch('config.php');
        if (response.ok) {
            // CSRF token is generated in PHP session
            console.log('CSRF token initialized');
        }
    } catch (error) {
        console.error('Error fetching CSRF token:', error);
    }
}

// Check if user is logged in
async function checkUserSession() {
    try {
        const response = await fetch('auth.php?action=check_session');
        const data = await response.json();
        
        if (data.success && data.logged_in) {
            // User is logged in
            updateUIForLoggedInUser(data.user);
        } else {
            // User is not logged in
            updateUIForLoggedOutUser();
        }
    } catch (error) {
        console.error('Error checking session:', error);
    }
}

// Update UI for logged in user
function updateUIForLoggedInUser(user) {
    // Update navigation or user info if on main page
    const userInfo = document.getElementById('userInfo');
    if (userInfo) {
        userInfo.innerHTML = `
            <span>Welcome, ${user.full_name}!</span>
            <button onclick="logout()" class="logout-btn">Logout</button>
        `;
    }
}

// Update UI for logged out user
function updateUIForLoggedOutUser() {
    const userInfo = document.getElementById('userInfo');
    if (userInfo) {
        userInfo.innerHTML = `
            <a href="login.html" class="login-link">Login</a>
            <a href="signup.html" class="signup-link">Sign Up</a>
        `;
    }
}

// Initialize login form
function initializeLoginForm() {
    const loginForm = document.getElementById('loginForm');
    if (!loginForm) return;

    loginForm.addEventListener('submit', handleLogin);
    
    // Add real-time validation
    const usernameEmail = document.getElementById('usernameEmail');
    const password = document.getElementById('password');
    
    usernameEmail.addEventListener('blur', validateUsernameEmail);
    password.addEventListener('input', validatePassword);
}

// Initialize signup form
function initializeSignupForm() {
    const signupForm = document.getElementById('signupForm');
    if (!signupForm) return;

    signupForm.addEventListener('submit', handleSignup);
    
    // Add real-time validation
    const fullName = document.getElementById('fullName');
    const username = document.getElementById('username');
    const email = document.getElementById('email');
    const phone = document.getElementById('phone');
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirmPassword');
    
    fullName.addEventListener('blur', validateFullName);
    username.addEventListener('blur', validateUsername);
    email.addEventListener('blur', validateEmail);
    phone.addEventListener('blur', validatePhone);
    password.addEventListener('input', validatePasswordStrength);
    confirmPassword.addEventListener('blur', validateConfirmPassword);
}

// Handle login form submission
async function handleLogin(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    formData.append('action', 'login');
    formData.append('csrf_token', csrfToken);
    
    const loginButton = document.getElementById('loginButton');
    const alertMessage = document.getElementById('alertMessage');
    
    // Show loading state
    setButtonLoading(loginButton, true);
    hideAlert(alertMessage);
    
    try {
        const response = await fetch('auth.php', {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert(alertMessage, 'Login successful! Redirecting...', 'success');
            setTimeout(() => {
                window.location.href = 'website.html';
            }, 1500);
        } else {
            showAlert(alertMessage, data.message, 'error');
        }
    } catch (error) {
        console.error('Login error:', error);
        showAlert(alertMessage, 'Login failed. Please try again.', 'error');
    } finally {
        setButtonLoading(loginButton, false);
    }
}

// Handle signup form submission
async function handleSignup(event) {
    event.preventDefault();
    
    const form = event.target;
    
    // Validate form before submission
    if (!validateSignupForm()) {
        return;
    }
    
    const formData = new FormData(form);
    formData.append('action', 'signup');
    formData.append('csrf_token', csrfToken);
    
    const signupButton = document.getElementById('signupButton');
    const alertMessage = document.getElementById('alertMessage');
    
    // Show loading state
    setButtonLoading(signupButton, true);
    hideAlert(alertMessage);
    
    try {
        const response = await fetch('auth.php', {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert(alertMessage, 'Account created successfully! Redirecting...', 'success');
            setTimeout(() => {
                window.location.href = 'website.html';
            }, 1500);
        } else {
            showAlert(alertMessage, data.message, 'error');
        }
    } catch (error) {
        console.error('Signup error:', error);
        showAlert(alertMessage, 'Registration failed. Please try again.', 'error');
    } finally {
        setButtonLoading(signupButton, false);
    }
}

// Logout function
async function logout() {
    try {
        const response = await fetch('auth.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=logout'
        });
        
        const data = await response.json();
        
        if (data.success) {
            window.location.href = 'website.html';
        }
    } catch (error) {
        console.error('Logout error:', error);
    }
}

// Validation functions
function validateFullName() {
    const fullName = document.getElementById('fullName');
    const message = document.getElementById('fullNameMessage');
    
    if (fullName.value.trim().length < 2) {
        setFieldError(fullName, message, 'Full name must be at least 2 characters long');
        return false;
    }
    
    setFieldSuccess(fullName, message, '');
    return true;
}

async function validateUsername() {
    const username = document.getElementById('username');
    const message = document.getElementById('usernameMessage');
    
    if (username.value.trim().length < 3) {
        setFieldError(username, message, 'Username must be at least 3 characters long');
        return false;
    }
    
    // Check username availability
    try {
        const response = await fetch(`auth.php?action=check_username&username=${encodeURIComponent(username.value)}`);
        const data = await response.json();
        
        if (!data.available) {
            setFieldError(username, message, 'Username is already taken');
            return false;
        }
        
        setFieldSuccess(username, message, 'Username is available');
        return true;
    } catch (error) {
        console.error('Username validation error:', error);
        return false;
    }
}

async function validateEmail() {
    const email = document.getElementById('email');
    const message = document.getElementById('emailMessage');
    
    if (!isValidEmail(email.value)) {
        setFieldError(email, message, 'Please enter a valid email address');
        return false;
    }
    
    // Check email availability
    try {
        const response = await fetch(`auth.php?action=check_email&email=${encodeURIComponent(email.value)}`);
        const data = await response.json();
        
        if (!data.available) {
            setFieldError(email, message, 'Email is already registered');
            return false;
        }
        
        setFieldSuccess(email, message, 'Email is available');
        return true;
    } catch (error) {
        console.error('Email validation error:', error);
        return false;
    }
}

function validatePhone() {
    const phone = document.getElementById('phone');
    const message = document.getElementById('phoneMessage');
    
    if (phone.value && !isValidPhone(phone.value)) {
        setFieldError(phone, message, 'Please enter a valid phone number');
        return false;
    }
    
    setFieldSuccess(phone, message, '');
    return true;
}

function validatePasswordStrength() {
    const password = document.getElementById('password');
    const strengthFill = document.getElementById('strengthFill');
    const strengthText = document.getElementById('strengthText');
    const message = document.getElementById('passwordMessage');
    
    const strength = getPasswordStrength(password.value);
    
    // Update strength indicator
    strengthFill.className = `strength-fill ${strength.level}`;
    strengthText.textContent = strength.text;
    
    if (password.value.length < 8) {
        setFieldError(password, message, 'Password must be at least 8 characters long');
        return false;
    }
    
    if (strength.level === 'weak') {
        setFieldError(password, message, 'Password is too weak');
        return false;
    }
    
    setFieldSuccess(password, message, '');
    return true;
}

function validateConfirmPassword() {
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirmPassword');
    const message = document.getElementById('confirmPasswordMessage');
    
    if (password.value !== confirmPassword.value) {
        setFieldError(confirmPassword, message, 'Passwords do not match');
        return false;
    }
    
    setFieldSuccess(confirmPassword, message, 'Passwords match');
    return true;
}

function validateUsernameEmail() {
    const usernameEmail = document.getElementById('usernameEmail');
    
    if (usernameEmail.value.trim().length < 3) {
        usernameEmail.classList.add('error');
        return false;
    }
    
    usernameEmail.classList.remove('error');
    usernameEmail.classList.add('success');
    return true;
}

function validatePassword() {
    const password = document.getElementById('password');
    
    if (password.value.length < 8) {
        password.classList.add('error');
        return false;
    }
    
    password.classList.remove('error');
    password.classList.add('success');
    return true;
}

// Validate entire signup form
function validateSignupForm() {
    const fullNameValid = validateFullName();
    const passwordValid = validatePasswordStrength();
    const confirmPasswordValid = validateConfirmPassword();
    const phoneValid = validatePhone();
    
    const agreeTerms = document.getElementById('agreeTerms');
    if (!agreeTerms.checked) {
        showAlert(document.getElementById('alertMessage'), 'Please agree to the terms and conditions', 'error');
        return false;
    }
    
    return fullNameValid && passwordValid && confirmPasswordValid && phoneValid;
}

// Utility functions
function setFieldError(field, message, text) {
    field.classList.remove('success');
    field.classList.add('error');
    if (message) {
        message.textContent = text;
        message.classList.remove('success');
        message.classList.add('error');
    }
}

function setFieldSuccess(field, message, text) {
    field.classList.remove('error');
    field.classList.add('success');
    if (message) {
        message.textContent = text;
        message.classList.remove('error');
        message.classList.add('success');
    }
}

function showAlert(alertElement, message, type) {
    alertElement.textContent = message;
    alertElement.className = `alert ${type}`;
    alertElement.style.display = 'block';
}

function hideAlert(alertElement) {
    alertElement.style.display = 'none';
}

function setButtonLoading(button, loading) {
    if (loading) {
        button.classList.add('loading');
        button.disabled = true;
    } else {
        button.classList.remove('loading');
        button.disabled = false;
    }
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidPhone(phone) {
    const phoneRegex = /^[+]?[\d\s\-\(\)]{10,15}$/;
    return phoneRegex.test(phone);
}

function getPasswordStrength(password) {
    let score = 0;
    
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;
    
    if (score < 3) {
        return { level: 'weak', text: 'Weak password' };
    } else if (score < 4) {
        return { level: 'fair', text: 'Fair password' };
    } else if (score < 6) {
        return { level: 'good', text: 'Good password' };
    } else {
        return { level: 'strong', text: 'Strong password' };
    }
}
