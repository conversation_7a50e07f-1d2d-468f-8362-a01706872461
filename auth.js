// Authentication JavaScript for Heritage Explorer

// Global variables
let csrfToken = '';

// Initialize authentication system
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on the right server
    const isDevServer = window.location.port === '3000' && window.location.hostname === 'localhost';
    const isStaticServer = window.location.hostname === '127.0.0.1' || window.location.port === '5500';
    const isXamppServer = window.location.hostname === 'localhost' && !window.location.port;

    if (isStaticServer) {
        console.error('🚨 STATIC FILE SERVER DETECTED!');
        console.error('You are using a static file server. PHP authentication will not work.');
        console.error('Please follow these steps:');
        console.error('1. Install XAMPP from https://www.apachefriends.org/');
        console.error('2. Start Apache and MySQL services');
        console.error('3. Copy files to C:\\xampp\\htdocs\\heritage-explorer\\');
        console.error('4. Access via http://localhost/heritage-explorer/website.html');

        // Show user-friendly error message
        showServerError();
        return;
    }

    if (isDevServer) {
        console.log('🚀 Development server detected - using mocked authentication');
        // Set development mode flag
        window.isDevelopmentMode = true;
    }

    if (isXamppServer) {
        console.log('🐘 XAMPP server detected - using full PHP authentication');
        window.isDevelopmentMode = false;
    }

    fetchCSRFToken();
    checkUserSession();

    // Initialize form handlers if on login/signup pages
    initializeAuthForms();
});

// Fetch CSRF token
async function fetchCSRFToken() {
    try {
        const response = await fetch('config.php');
        if (response.ok) {
            // CSRF token is generated in PHP session
            console.log('CSRF token initialized');
        }
    } catch (error) {
        console.error('Error fetching CSRF token:', error);
    }
}

// Check if user is logged in
async function checkUserSession() {
    try {
        const response = await fetch('auth.php?action=check_session');

        // Check if response is ok and content type is JSON
        if (!response.ok) {
            if (window.isDevelopmentMode) {
                console.log('🚀 Development mode: Mocking logged out user');
                updateUIForLoggedOutUser();
                return;
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            if (window.isDevelopmentMode) {
                console.log('🚀 Development mode: Using mocked authentication');
                updateUIForLoggedOutUser();
                return;
            }
            console.error('⚠️ Server returned non-JSON response. Make sure you are accessing through http://localhost/heritage-explorer/ and PHP is working.');
            updateUIForLoggedOutUser();
            return;
        }

        const data = await response.json();

        if (data.success && data.logged_in) {
            // User is logged in
            updateUIForLoggedInUser(data.user);
        } else {
            // User is not logged in
            updateUIForLoggedOutUser();
        }
    } catch (error) {
        if (window.isDevelopmentMode) {
            console.log('🚀 Development mode: Session check failed, showing logged out state');
            updateUIForLoggedOutUser();
            return;
        }
        console.error('Error checking session:', error);
        console.error('🔧 Make sure you are accessing the site through http://localhost/heritage-explorer/ and not through a live server extension');
        updateUIForLoggedOutUser();
    }
}

// Update UI for logged in user
function updateUIForLoggedInUser(user) {
    // Update navigation or user info if on main page
    const userInfo = document.getElementById('userInfo');
    if (userInfo) {
        userInfo.innerHTML = `
            <span>Welcome, ${user.full_name}!</span>
            <button onclick="logout()" class="logout-btn">Logout</button>
        `;
    }
}

// Update UI for logged out user
function updateUIForLoggedOutUser() {
    const userInfo = document.getElementById('userInfo');
    if (userInfo) {
        userInfo.innerHTML = `
            <a href="login.html" class="login-link">Login</a>
            <a href="signup.html" class="signup-link">Sign Up</a>
        `;
    }
}

// Initialize login form
function initializeLoginForm() {
    const loginForm = document.getElementById('loginForm');
    if (!loginForm) return;

    loginForm.addEventListener('submit', handleLogin);

    // Add real-time validation
    const usernameEmail = document.getElementById('usernameEmail');
    const password = document.getElementById('password');

    usernameEmail.addEventListener('blur', validateUsernameEmail);
    password.addEventListener('input', validatePassword);
}

// Initialize signup form
function initializeSignupForm() {
    const signupForm = document.getElementById('signupForm');
    if (!signupForm) return;

    signupForm.addEventListener('submit', handleSignup);

    // Add real-time validation
    const fullName = document.getElementById('fullName');
    const username = document.getElementById('username');
    const email = document.getElementById('email');
    const phone = document.getElementById('phone');
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirmPassword');

    fullName.addEventListener('blur', validateFullName);
    username.addEventListener('blur', validateUsername);
    email.addEventListener('blur', validateEmail);
    phone.addEventListener('blur', validatePhone);
    password.addEventListener('input', validatePasswordStrength);
    confirmPassword.addEventListener('blur', validateConfirmPassword);
}

// Handle login form submission
async function handleLogin(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    formData.append('action', 'login');
    formData.append('csrf_token', csrfToken);

    const loginButton = document.getElementById('loginButton');
    const alertMessage = document.getElementById('alertMessage');

    // Show loading state
    setButtonLoading(loginButton, true);
    hideAlert(alertMessage);

    // Handle development mode
    if (window.isDevelopmentMode) {
        console.log('🚀 Development mode: Mocking login attempt');
        setTimeout(() => {
            showAlert(alertMessage, 'Development mode - Login mocked. Use XAMPP for real authentication.', 'warning');
            setButtonLoading(loginButton, false);
        }, 1000);
        return;
    }

    try {
        const response = await fetch('auth.php', {
            method: 'POST',
            body: formData
        });

        const data = await response.json();

        if (data.success) {
            showAlert(alertMessage, 'Login successful! Redirecting...', 'success');
            setTimeout(() => {
                window.location.href = 'website.html';
            }, 1500);
        } else {
            showAlert(alertMessage, data.message, 'error');
        }
    } catch (error) {
        console.error('Login error:', error);
        showAlert(alertMessage, 'Login failed. Please try again.', 'error');
    } finally {
        setButtonLoading(loginButton, false);
    }
}

// Handle signup form submission
async function handleSignup(event) {
    event.preventDefault();

    const form = event.target;

    // Validate form before submission
    if (!validateSignupForm()) {
        return;
    }

    const formData = new FormData(form);
    formData.append('action', 'signup');
    formData.append('csrf_token', csrfToken);

    const signupButton = document.getElementById('signupButton');
    const alertMessage = document.getElementById('alertMessage');

    // Show loading state
    setButtonLoading(signupButton, true);
    hideAlert(alertMessage);

    // Handle development mode
    if (window.isDevelopmentMode) {
        console.log('🚀 Development mode: Mocking signup attempt');
        setTimeout(() => {
            showAlert(alertMessage, 'Development mode - Signup mocked. Use XAMPP for real user registration.', 'warning');
            setButtonLoading(signupButton, false);
        }, 1000);
        return;
    }

    try {
        const response = await fetch('auth.php', {
            method: 'POST',
            body: formData
        });

        const data = await response.json();

        if (data.success) {
            showAlert(alertMessage, 'Account created successfully! Redirecting...', 'success');
            setTimeout(() => {
                window.location.href = 'website.html';
            }, 1500);
        } else {
            showAlert(alertMessage, data.message, 'error');
        }
    } catch (error) {
        console.error('Signup error:', error);
        showAlert(alertMessage, 'Registration failed. Please try again.', 'error');
    } finally {
        setButtonLoading(signupButton, false);
    }
}

// Logout function
async function logout() {
    try {
        const response = await fetch('auth.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=logout'
        });

        const data = await response.json();

        if (data.success) {
            window.location.href = 'website.html';
        }
    } catch (error) {
        console.error('Logout error:', error);
    }
}

// Validation functions
function validateFullName() {
    const fullName = document.getElementById('fullName');
    const message = document.getElementById('fullNameMessage');

    if (fullName.value.trim().length < 2) {
        setFieldError(fullName, message, 'Full name must be at least 2 characters long');
        return false;
    }

    setFieldSuccess(fullName, message, '');
    return true;
}

async function validateUsername() {
    const username = document.getElementById('username');
    const message = document.getElementById('usernameMessage');

    if (username.value.trim().length < 3) {
        setFieldError(username, message, 'Username must be at least 3 characters long');
        return false;
    }

    // Check username availability
    try {
        const response = await fetch(`auth.php?action=check_username&username=${encodeURIComponent(username.value)}`);

        // Check if response is ok and content type is JSON
        if (!response.ok) {
            if (window.isDevelopmentMode) {
                console.log('🚀 Development mode: Mocking username availability check');
                setFieldSuccess(username, message, 'Username available (dev mode)');
                return true;
            }
            console.error('Server error:', response.status);
            return true; // Allow form submission if server check fails
        }

        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            if (window.isDevelopmentMode) {
                console.log('🚀 Development mode: Mocking username availability check');
                setFieldSuccess(username, message, 'Username available (dev mode)');
                return true;
            }
            console.error('⚠️ Server returned non-JSON response for username check. Make sure you are accessing through http://localhost/heritage-explorer/');
            return true; // Allow form submission if server check fails
        }

        const data = await response.json();

        if (!data.available) {
            setFieldError(username, message, 'Username is already taken');
            return false;
        }

        setFieldSuccess(username, message, 'Username is available');
        return true;
    } catch (error) {
        if (window.isDevelopmentMode) {
            console.log('🚀 Development mode: Username validation error, allowing submission');
            setFieldSuccess(username, message, 'Username available (dev mode)');
            return true;
        }
        console.error('Username validation error:', error);
        console.error('🔧 Make sure you are accessing the site through http://localhost/heritage-explorer/');
        return true; // Allow form submission if server check fails
    }
}

async function validateEmail() {
    const email = document.getElementById('email');
    const message = document.getElementById('emailMessage');

    if (!isValidEmail(email.value)) {
        setFieldError(email, message, 'Please enter a valid email address');
        return false;
    }

    // Check email availability
    try {
        const response = await fetch(`auth.php?action=check_email&email=${encodeURIComponent(email.value)}`);

        // Check if response is ok and content type is JSON
        if (!response.ok) {
            if (window.isDevelopmentMode) {
                console.log('🚀 Development mode: Mocking email availability check');
                setFieldSuccess(email, message, 'Email available (dev mode)');
                return true;
            }
            console.error('Server error:', response.status);
            return true; // Allow form submission if server check fails
        }

        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            if (window.isDevelopmentMode) {
                console.log('🚀 Development mode: Mocking email availability check');
                setFieldSuccess(email, message, 'Email available (dev mode)');
                return true;
            }
            console.error('⚠️ Server returned non-JSON response for email check. Make sure you are accessing through http://localhost/heritage-explorer/');
            return true; // Allow form submission if server check fails
        }

        const data = await response.json();

        if (!data.available) {
            setFieldError(email, message, 'Email is already registered');
            return false;
        }

        setFieldSuccess(email, message, 'Email is available');
        return true;
    } catch (error) {
        if (window.isDevelopmentMode) {
            console.log('🚀 Development mode: Email validation error, allowing submission');
            setFieldSuccess(email, message, 'Email available (dev mode)');
            return true;
        }
        console.error('Email validation error:', error);
        console.error('🔧 Make sure you are accessing the site through http://localhost/heritage-explorer/');
        return true; // Allow form submission if server check fails
    }
}

function validatePhone() {
    const phone = document.getElementById('phone');
    const message = document.getElementById('phoneMessage');

    if (phone.value && !isValidPhone(phone.value)) {
        setFieldError(phone, message, 'Please enter a valid phone number');
        return false;
    }

    setFieldSuccess(phone, message, '');
    return true;
}

function validatePasswordStrength() {
    const password = document.getElementById('password');
    const strengthFill = document.getElementById('strengthFill');
    const strengthText = document.getElementById('strengthText');
    const message = document.getElementById('passwordMessage');

    const strength = getPasswordStrength(password.value);

    // Update strength indicator
    strengthFill.className = `strength-fill ${strength.level}`;
    strengthText.textContent = strength.text;

    if (password.value.length < 8) {
        setFieldError(password, message, 'Password must be at least 8 characters long');
        return false;
    }

    if (strength.level === 'weak') {
        setFieldError(password, message, 'Password is too weak');
        return false;
    }

    setFieldSuccess(password, message, '');
    return true;
}

function validateConfirmPassword() {
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirmPassword');
    const message = document.getElementById('confirmPasswordMessage');

    if (password.value !== confirmPassword.value) {
        setFieldError(confirmPassword, message, 'Passwords do not match');
        return false;
    }

    setFieldSuccess(confirmPassword, message, 'Passwords match');
    return true;
}

function validateUsernameEmail() {
    const usernameEmail = document.getElementById('usernameEmail');

    if (usernameEmail.value.trim().length < 3) {
        usernameEmail.classList.add('error');
        return false;
    }

    usernameEmail.classList.remove('error');
    usernameEmail.classList.add('success');
    return true;
}

function validatePassword() {
    const password = document.getElementById('password');

    if (password.value.length < 8) {
        password.classList.add('error');
        return false;
    }

    password.classList.remove('error');
    password.classList.add('success');
    return true;
}

// Validate entire signup form
function validateSignupForm() {
    const fullNameValid = validateFullName();
    const passwordValid = validatePasswordStrength();
    const confirmPasswordValid = validateConfirmPassword();
    const phoneValid = validatePhone();

    const agreeTerms = document.getElementById('agreeTerms');
    if (!agreeTerms.checked) {
        showAlert(document.getElementById('alertMessage'), 'Please agree to the terms and conditions', 'error');
        return false;
    }

    return fullNameValid && passwordValid && confirmPasswordValid && phoneValid;
}

// Utility functions
function setFieldError(field, message, text) {
    field.classList.remove('success');
    field.classList.add('error');
    if (message) {
        message.textContent = text;
        message.classList.remove('success');
        message.classList.add('error');
    }
}

function setFieldSuccess(field, message, text) {
    field.classList.remove('error');
    field.classList.add('success');
    if (message) {
        message.textContent = text;
        message.classList.remove('error');
        message.classList.add('success');
    }
}

function showAlert(alertElement, message, type) {
    alertElement.textContent = message;
    alertElement.className = `alert ${type}`;
    alertElement.style.display = 'block';
}

function hideAlert(alertElement) {
    alertElement.style.display = 'none';
}

function setButtonLoading(button, loading) {
    if (loading) {
        button.classList.add('loading');
        button.disabled = true;
    } else {
        button.classList.remove('loading');
        button.disabled = false;
    }
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidPhone(phone) {
    const phoneRegex = /^[+]?[\d\s\-\(\)]{10,15}$/;
    return phoneRegex.test(phone);
}

function getPasswordStrength(password) {
    let score = 0;

    if (password.length >= 8) score++;
    if (password.length >= 12) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;

    if (score < 3) {
        return { level: 'weak', text: 'Weak password' };
    } else if (score < 4) {
        return { level: 'fair', text: 'Fair password' };
    } else if (score < 6) {
        return { level: 'good', text: 'Good password' };
    } else {
        return { level: 'strong', text: 'Strong password' };
    }
}

// Show server error message
function showServerError() {
    // Create error overlay
    const errorOverlay = document.createElement('div');
    errorOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: Arial, sans-serif;
    `;

    errorOverlay.innerHTML = `
        <div style="max-width: 600px; padding: 40px; text-align: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.3);">
            <h1 style="color: #ff6b6b; margin-bottom: 20px; font-size: 2.5em;">🚨 Server Configuration Error</h1>
            <p style="font-size: 1.2em; margin-bottom: 20px; line-height: 1.6;">
                You're accessing this site through a <strong>static file server</strong> (${window.location.origin})<br>
                The authentication system requires a <strong>PHP-enabled web server</strong> to function.
            </p>
            <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin: 20px 0; text-align: left;">
                <h3 style="color: #4ecdc4; margin-bottom: 15px;">🔧 How to Fix This:</h3>
                <ol style="line-height: 2; font-size: 1.1em;">
                    <li><strong>Install XAMPP:</strong> Download from <a href="https://www.apachefriends.org/" target="_blank" style="color: #4ecdc4;">apachefriends.org</a></li>
                    <li><strong>Start Services:</strong> Open XAMPP Control Panel, start Apache & MySQL</li>
                    <li><strong>Move Files:</strong> Copy all project files to <code style="background: rgba(0,0,0,0.3); padding: 2px 6px; border-radius: 3px;">C:\\xampp\\htdocs\\heritage-explorer\\</code></li>
                    <li><strong>Access Correctly:</strong> Go to <code style="background: rgba(0,0,0,0.3); padding: 2px 6px; border-radius: 3px;">http://localhost/heritage-explorer/</code></li>
                </ol>
            </div>
            <button onclick="window.open('check-setup.html', '_blank')" style="background: #4ecdc4; color: white; border: none; padding: 15px 30px; border-radius: 8px; font-size: 1.1em; cursor: pointer; margin: 10px;">
                📋 Open Setup Guide
            </button>
            <button onclick="this.parentElement.parentElement.remove()" style="background: #ff6b6b; color: white; border: none; padding: 15px 30px; border-radius: 8px; font-size: 1.1em; cursor: pointer; margin: 10px;">
                ❌ Close This Message
            </button>
        </div>
    `;

    document.body.appendChild(errorOverlay);
}

// Initialize form handlers
function initializeAuthForms() {
    // Initialize login form if present
    initializeLoginForm();

    // Initialize signup form if present
    initializeSignupForm();
}
