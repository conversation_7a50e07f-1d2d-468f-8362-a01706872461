@echo off
cls
color 0A
echo.
echo ==========================================
echo    Heritage Explorer - Terminal Runner
echo ==========================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] Running with administrator privileges
) else (
    echo [WARNING] Not running as administrator - some operations may fail
)

echo.
echo [STEP 1] Checking XAMPP installation...
if exist "C:\xampp\xampp-control.exe" (
    echo [OK] XAMPP found at C:\xampp\
) else (
    echo [ERROR] XAMPP not found!
    echo.
    echo Please install XAMPP first:
    echo 1. Download from: https://www.apachefriends.org/
    echo 2. Install to C:\xampp\
    echo 3. Run this script again
    echo.
    echo Opening download page...
    start https://www.apachefriends.org/download.html
    pause
    exit /b 1
)

echo.
echo [STEP 2] Creating project directory...
if not exist "C:\xampp\htdocs\heritage-explorer" (
    mkdir "C:\xampp\htdocs\heritage-explorer"
    echo [OK] Created C:\xampp\htdocs\heritage-explorer\
) else (
    echo [OK] Directory already exists
)

echo.
echo [STEP 3] Copying project files...
echo Current directory: %cd%
echo Target directory: C:\xampp\htdocs\heritage-explorer\

:: Copy all files from current directory to XAMPP
xcopy "%cd%\*" "C:\xampp\htdocs\heritage-explorer\" /E /H /Y /I 2>nul
if %errorlevel% == 0 (
    echo [OK] Files copied successfully
) else (
    echo [WARNING] Some files may not have been copied
    echo Please manually copy files if needed
)

echo.
echo [STEP 4] Starting XAMPP services...
echo.
echo Starting Apache...
"C:\xampp\apache\bin\httpd.exe" -k start 2>nul
if %errorlevel% == 0 (
    echo [OK] Apache started
) else (
    echo [INFO] Apache may already be running or needs manual start
)

echo.
echo Starting MySQL...
"C:\xampp\mysql\bin\mysqld.exe" --defaults-file="C:\xampp\mysql\bin\my.ini" --standalone --console 2>nul &
echo [INFO] MySQL start command executed

echo.
echo [STEP 5] Waiting for services to start...
timeout /t 5 /nobreak >nul

echo.
echo [STEP 6] Testing server connection...
curl -s http://localhost/heritage-explorer/website.html >nul 2>&1
if %errorlevel% == 0 (
    echo [OK] Server is responding
) else (
    echo [WARNING] Server may not be ready yet
)

echo.
echo [STEP 7] Setting up database...
echo Opening database setup in browser...
start http://localhost/heritage-explorer/setup-database.php

echo.
echo [STEP 8] Launching project...
timeout /t 3 /nobreak >nul
start http://localhost/heritage-explorer/website.html

echo.
echo ==========================================
echo    PROJECT LAUNCHED SUCCESSFULLY!
echo ==========================================
echo.
echo Your Heritage Explorer is now running at:
echo http://localhost/heritage-explorer/website.html
echo.
echo Available endpoints:
echo - Main Site: http://localhost/heritage-explorer/website.html
echo - Login:     http://localhost/heritage-explorer/login.html
echo - Signup:    http://localhost/heritage-explorer/signup.html
echo - DB Test:   http://localhost/heritage-explorer/test-connection.php
echo - Status:    http://localhost/heritage-explorer/project-status.html
echo.
echo Admin credentials:
echo Username: admin
echo Password: admin123
echo.
echo Press any key to exit...
pause >nul
