<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON><PERSON><PERSON> - Heritage Explorer</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
  <style>
    :root {
      --primary-color: #1a237e;
      --secondary-color: #457b9d;
      --accent-color: #e63946;
      --light-color: #f1faee;
      --dark-color: #1d3557;
      --success-color: #2e7d32;
      --warning-color: #ff9800;
      --danger-color: #c62828;
      --gray-100: #f8f9fa;
      --gray-200: #e9ecef;
      --gray-300: #dee2e6;
      --gray-400: #ced4da;
      --gray-500: #adb5bd;
      --gray-600: #6c757d;
      --gray-700: #495057;
      --gray-800: #343a40;
      --gray-900: #212529;
      --border-radius: 0.5rem;
      --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
      --transition: all 0.3s ease-in-out;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: var(--gray-800);
      background-color: var(--gray-100);
    }

    header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      text-align: center;
    }

    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      background-color: white;
      box-shadow: var(--box-shadow);
    }

    nav a {
      color: var(--gray-700);
      text-decoration: none;
      margin: 0 1rem;
      font-weight: 500;
      transition: var(--transition);
    }

    nav a:hover {
      color: var(--primary-color);
    }

    main {
      max-width: 1200px;
      margin: 2rem auto;
      padding: 0 1rem;
    }

    .site-details {
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    @media (min-width: 768px) {
      .site-details {
        grid-template-columns: 1fr 1fr;
      }
    }

    .site-image {
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
    }

    .site-image img {
      width: 100%;
      height: auto;
      display: block;
    }

    .site-info h1 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .site-info p {
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }

    .site-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .meta-item {
      display: flex;
      align-items: center;
      background-color: var(--gray-200);
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
    }

    .meta-item i {
      margin-right: 0.5rem;
      color: var(--secondary-color);
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      margin-top: 1.5rem;
    }

    .action-buttons button {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      background-color: var(--secondary-color);
      color: white;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
      display: flex;
      align-items: center;
    }

    .action-buttons button i {
      margin-right: 0.5rem;
    }

    .action-buttons button:hover {
      background-color: var(--primary-color);
    }

    .site-description {
      margin-bottom: 2rem;
    }

    .site-description h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .map-container {
      height: 400px;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      margin-bottom: 2rem;
    }

    .nearby-attractions {
      margin-bottom: 2rem;
    }

    .nearby-attractions h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .attractions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .attraction-card {
      background-color: white;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
    }

    .attraction-card:hover {
      transform: translateY(-5px);
    }

    .attraction-card img {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }

    .attraction-card-content {
      padding: 1rem;
    }

    .attraction-card-content h3 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    footer {
      background-color: var(--primary-color);
      color: white;
      text-align: center;
      padding: 1rem;
      margin-top: 2rem;
    }

    #backToTop {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      width: 3rem;
      height: 3rem;
      background-color: var(--primary-color);
      color: white;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
      display: none;
    }

    #backToTop:hover {
      background-color: var(--secondary-color);
    }

    /* Travel Information Section */
    .travel-info {
      margin-bottom: 2rem;
      background-color: white;
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      overflow: hidden;
    }

    .travel-info-header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .travel-info-header h2 {
      margin: 0;
      font-size: 1.5rem;
    }

    .travel-info-tabs {
      display: flex;
      background-color: var(--gray-200);
    }

    .travel-tab {
      padding: 1rem;
      cursor: pointer;
      flex: 1;
      text-align: center;
      font-weight: 500;
      transition: var(--transition);
      border-bottom: 3px solid transparent;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .travel-tab i {
      margin-right: 0.5rem;
    }

    .travel-tab.active {
      background-color: white;
      border-bottom-color: var(--accent-color);
    }

    .travel-tab:hover:not(.active) {
      background-color: var(--gray-300);
    }

    .travel-content {
      padding: 1.5rem;
    }

    .travel-panel {
      display: none;
    }

    .travel-panel.active {
      display: block;
    }

    /* Weather Panel */
    .weather-info {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      align-items: center;
    }

    .weather-current {
      display: flex;
      align-items: center;
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      flex: 1;
      min-width: 250px;
    }

    .weather-icon {
      font-size: 3rem;
      margin-right: 1rem;
      color: var(--secondary-color);
    }

    .weather-details h3 {
      margin: 0;
      color: var(--primary-color);
    }

    .weather-temp {
      font-size: 2rem;
      font-weight: bold;
    }

    .weather-desc {
      color: var(--gray-700);
    }

    .weather-forecast {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      flex: 2;
      min-width: 300px;
    }

    .forecast-day {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      text-align: center;
      flex: 1;
      min-width: 100px;
    }

    .forecast-day h4 {
      margin: 0 0 0.5rem 0;
      color: var(--primary-color);
    }

    .forecast-icon {
      font-size: 1.5rem;
      margin: 0.5rem 0;
      color: var(--secondary-color);
    }

    /* Transportation Panel */
    .transport-options {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .transport-option {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
      display: flex;
      flex-direction: column;
    }

    .transport-option h3 {
      display: flex;
      align-items: center;
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .transport-option h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .transport-option p {
      margin-bottom: 0.5rem;
    }

    .transport-option .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    /* Hours & Tickets Panel */
    .hours-container {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .opening-hours, .ticket-info {
      flex: 1;
      min-width: 250px;
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .opening-hours h3, .ticket-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .opening-hours h3 i, .ticket-info h3 i {
      margin-right: 0.5rem;
    }

    .hours-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .hours-list li {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .hours-list li:last-child {
      border-bottom: none;
    }

    .day {
      font-weight: 500;
    }

    .ticket-type {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .ticket-type:last-child {
      border-bottom: none;
    }

    .ticket-price {
      font-weight: bold;
    }

    /* Amenities Panel */
    .amenities-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .amenity-card {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .amenity-card h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .amenity-card h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .amenity-card p {
      margin-bottom: 0.5rem;
    }

    .amenity-card .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    .amenity-card .rating {
      color: var(--warning-color);
      margin-bottom: 0.5rem;
    }

    .weather-tips h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .local-transport h3, .best-time h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>Heritage Explorer</h1>
    <p>Discover Tamil Nadu's rich heritage and culture</p>
  </header>

  <nav>
    <div>
      <a href="website.html"><i class="fas fa-home"></i> Home</a>
      <a href="#"><i class="fas fa-map-marked-alt"></i> Explore</a>
      <a href="#"><i class="fas fa-info-circle"></i> About</a>
    </div>
    <div>
      <a href="#" id="toggleLang">தமிழ்</a>
    </div>
  </nav>

  <main>
    <div class="site-details">
      <div class="site-image">
        <img src="https://tse1.mm.bing.net/th?id=OIP.3B2tPbPkrxw-phM-4Q6AqgHaE8&pid=Api&P=0&h=220" alt="Meenakshi Amman Temple">
      </div>
      <div class="site-info">
        <h1>Meenakshi Amman Temple</h1>
        <div class="site-meta">
          <div class="meta-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>Madurai, Tamil Nadu</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-place-of-worship"></i>
            <span>Temple</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-gopuram"></i>
            <span>14 Gopurams</span>
          </div>
        </div>
        <p>A historic Hindu temple dedicated to Goddess Meenakshi, known for its magnificent architecture, colorful sculptures, and towering gopurams that dominate Madurai's skyline.</p>
        <div class="action-buttons">
          <button id="playAudioButton"><i class="fas fa-volume-up"></i> Listen</button>
          <button id="favoriteButton"><i class="far fa-heart"></i> Favorite</button>
          <button id="shareButton"><i class="fas fa-share-alt"></i> Share</button>
        </div>
      </div>
    </div>

    <div class="site-description">
      <h2>About Meenakshi Amman Temple</h2>
      <p>The Meenakshi Amman Temple is a historic Hindu temple located in the city of Madurai, Tamil Nadu. It is dedicated to Goddess Meenakshi, an avatar of the Hindu goddess Parvati, and her consort, Lord Sundareswarar (Shiva). The temple forms the heart and lifeline of the 2,500-year-old city of Madurai and is a significant symbol of the Tamil people's art and culture.</p>
      <p>The temple complex covers an area of 14 acres and features 14 magnificent gopurams (gateway towers), the tallest being the southern tower which rises to 170 feet. The temple is renowned for its intricate carvings, with an estimated 33,000 sculptures adorning the complex. The most striking feature is the Hall of Thousand Pillars, each carved with exquisite figures of dancers, musicians, and deities. The temple's pillared corridors, known as prakarams, stretch for nearly a mile in total length.</p>
      <p>The Meenakshi Temple is not just a religious site but also a masterpiece of Dravidian architecture. It hosts the famous Meenakshi Thirukalyanam (celestial wedding of the Goddess) festival during April-May, which attracts over a million visitors. The temple is also known for its sacred tank called Porthamarai Kulam (Pond with the Golden Lotus), where devotees take ritual baths. The temple remains an active place of worship and a major pilgrimage destination, drawing thousands of devotees and tourists daily.</p>
    </div>

    <div class="map-container">
      <!-- Map will be embedded here -->
      <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3930.1501612280716!2d78.11721541479044!3d9.919506992907993!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3b00c58461e46987%3A0xf134621ce5286703!2sMeenakshi%20Amman%20Temple!5e0!3m2!1sen!2sin!4v1652345678901!5m2!1sen!2sin" width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy" title="Map of Meenakshi Amman Temple"></iframe>
    </div>

    <!-- Travel Information Section -->
    <div class="travel-info">
      <div class="travel-info-header">
        <h2>Practical Travel Information</h2>
        <div class="last-updated">Last updated: <span id="lastUpdated">Today</span></div>
      </div>

      <div class="travel-info-tabs">
        <div class="travel-tab active" data-tab="weather">
          <i class="fas fa-cloud-sun"></i> Weather
        </div>
        <div class="travel-tab" data-tab="transport">
          <i class="fas fa-bus"></i> Transportation
        </div>
        <div class="travel-tab" data-tab="hours">
          <i class="far fa-clock"></i> Hours & Tickets
        </div>
        <div class="travel-tab" data-tab="amenities">
          <i class="fas fa-concierge-bell"></i> Nearby Amenities
        </div>
      </div>

      <div class="travel-content">
        <!-- Weather Panel -->
        <div class="travel-panel active" id="weatherPanel">
          <div class="weather-info">
            <div class="weather-current">
              <div class="weather-icon">
                <i class="fas fa-sun"></i>
              </div>
              <div class="weather-details">
                <h3>Current Weather</h3>
                <div class="weather-temp">34°C</div>
                <div class="weather-desc">Sunny</div>
                <div class="weather-extra">Humidity: 70% | Wind: 8 km/h</div>
              </div>
            </div>

            <div class="weather-forecast">
              <div class="forecast-day">
                <h4>Tomorrow</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun"></i></div>
                <div class="forecast-temp">33°C</div>
                <div class="forecast-desc">Partly Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Wed</h4>
                <div class="forecast-icon"><i class="fas fa-cloud"></i></div>
                <div class="forecast-temp">32°C</div>
                <div class="forecast-desc">Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Thu</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun-rain"></i></div>
                <div class="forecast-temp">31°C</div>
                <div class="forecast-desc">Light Rain</div>
              </div>
            </div>
          </div>

          <div class="weather-tips">
            <h3>Weather Tips</h3>
            <p>Madurai has a hot and humid climate throughout the year. The best time to visit is from October to March when the weather is relatively cooler. During summer (April-June), temperatures can soar above 40°C, making it uncomfortable for sightseeing. Carry light cotton clothes, a hat, sunglasses, and sunscreen. During the monsoon season (July-September), carry an umbrella or raincoat. The temple floors can get hot during midday, so consider wearing socks or footwear that can be easily removed and put back on.</p>
          </div>
        </div>

        <!-- Transportation Panel -->
        <div class="travel-panel" id="transportPanel">
          <div class="transport-options">
            <div class="transport-option">
              <h3><i class="fas fa-plane"></i> By Air</h3>
              <p>Madurai International Airport (IXM) is the nearest airport.</p>
              <p class="distance">12 km from the temple (approx. 30 minutes by car)</p>
              <p>Regular flights from Chennai, Bangalore, Mumbai, and international destinations like Singapore and Colombo.</p>
              <p>Prepaid taxis and app-based cabs are available from the airport to the temple.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-train"></i> By Train</h3>
              <p>Madurai Junction Railway Station is well-connected.</p>
              <p class="distance">3 km from the temple (10 minutes by auto-rickshaw)</p>
              <p>Regular trains from Chennai, Coimbatore, Trichy, and other major cities.</p>
              <p>Auto-rickshaws and taxis are readily available outside the station.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-bus"></i> By Bus</h3>
              <p>Madurai has excellent bus connectivity with TNSTC and private operators.</p>
              <p class="distance">4 km from the temple (15 minutes by auto)</p>
              <p>Regular state-run and private buses from Chennai, Coimbatore, Trichy, and other cities.</p>
              <p>Local city buses also connect the bus stand to the temple area.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-car"></i> By Car</h3>
              <p>Well-connected by highways from major cities.</p>
              <p>From Chennai: NH-45 (450 km, approx. 8 hours)</p>
              <p>From Coimbatore: NH-44 (220 km, approx. 4 hours)</p>
              <p>Parking available near the temple complex (fees apply).</p>
            </div>
          </div>

          <div class="local-transport">
            <h3>Local Transportation</h3>
            <p>Auto-rickshaws are the most convenient way to get around Madurai. Negotiate the fare before starting your journey or insist on using the meter. App-based taxi services like Ola and Uber are also available. The temple is located in the heart of the city, and many attractions are within walking distance. For budget travelers, local buses are available but can be crowded.</p>
          </div>
        </div>

        <!-- Hours & Tickets Panel -->
        <div class="travel-panel" id="hoursPanel">
          <div class="hours-container">
            <div class="opening-hours">
              <h3><i class="far fa-clock"></i> Opening Hours</h3>
              <ul class="hours-list">
                <li><span class="day">Morning:</span> <span class="time">5:00 AM - 12:30 PM</span></li>
                <li><span class="day">Evening:</span> <span class="time">4:00 PM - 9:30 PM</span></li>
                <li><span class="day">Special Darshan:</span> <span class="time">6:00 AM - 7:00 AM</span></li>
                <li><span class="day">Festival Days:</span> <span class="time">Extended hours (check locally)</span></li>
              </ul>
              <p class="hours-note">The temple is closed during the afternoon for rituals and cleaning. During major festivals like Chithirai Festival (April-May), timings may vary. Non-Hindus are not allowed in the main sanctum but can visit other parts of the temple.</p>
            </div>

            <div class="ticket-info">
              <h3><i class="fas fa-ticket-alt"></i> Ticket Information</h3>
              <div class="ticket-type">
                <span>General Entry:</span>
                <span class="ticket-price">Free</span>
              </div>
              <div class="ticket-type">
                <span>Special Darshan:</span>
                <span class="ticket-price">₹100</span>
              </div>
              <div class="ticket-type">
                <span>Still Camera Fee:</span>
                <span class="ticket-price">₹50</span>
              </div>
              <div class="ticket-type">
                <span>Video Camera Fee:</span>
                <span class="ticket-price">₹100</span>
              </div>
              <p class="ticket-note">Mobile phone photography is allowed in most areas without a fee. Footwear must be left at designated counters (₹10 fee). Lockers are available for storing valuables.</p>
            </div>
          </div>

          <div class="best-time">
            <h3>Best Time to Visit</h3>
            <p>The best time to visit is early morning (5:00 AM - 8:00 AM) or evening (6:00 PM - 8:00 PM) when the temple is less crowded and the weather is cooler. Avoid visiting during the afternoon due to the heat and closure for rituals. The temple is particularly crowded on Fridays and during festivals. The annual Chithirai Festival (April-May) is a spectacular time to visit but expect large crowds. October to March is the ideal season to visit Madurai as the weather is pleasant.</p>
          </div>
        </div>

        <!-- Amenities Panel -->
        <div class="travel-panel" id="amenitiesPanel">
          <div class="amenities-grid">
            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Heritage Madurai</strong></p>
              <p class="distance">3.5 km from temple</p>
              <p class="rating">★★★★☆ (4.5/5)</p>
              <p>Luxury heritage hotel with traditional Tamil architecture and modern amenities.</p>
              <p>Price range: ₹6,000 - ₹12,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>JC Residency</strong></p>
              <p class="distance">1.2 km from temple</p>
              <p class="rating">★★★★☆ (4.2/5)</p>
              <p>Mid-range hotel with comfortable rooms and good amenities.</p>
              <p>Price range: ₹2,500 - ₹5,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Murugan Idli Shop</strong></p>
              <p class="distance">0.5 km from temple</p>
              <p class="rating">★★★★★ (4.7/5)</p>
              <p>Famous for authentic South Indian vegetarian cuisine, especially soft idlis.</p>
              <p>Price range: ₹200 - ₹400 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Kumar Mess</strong></p>
              <p class="distance">1 km from temple</p>
              <p class="rating">★★★★☆ (4.3/5)</p>
              <p>Popular for non-vegetarian Chettinad cuisine and local specialties.</p>
              <p>Price range: ₹300 - ₹600 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-shopping-bag"></i> Shopping</h3>
              <p><strong>Puthu Mandapam</strong></p>
              <p class="distance">0.1 km from temple</p>
              <p>Historic shopping arcade with hundreds of shops selling textiles, handicrafts, and souvenirs.</p>
              <p>Famous for Sungudi sarees, cotton fabrics, and brass items.</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-first-aid"></i> Medical Facilities</h3>
              <p><strong>Apollo Speciality Hospital</strong></p>
              <p class="distance">5 km from temple</p>
              <p>Multi-specialty hospital with 24/7 emergency services and English-speaking staff.</p>
              <p>Emergency contact: +91 452 258 0892</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-coffee"></i> Cafes</h3>
              <p><strong>Cafe Utsav</strong></p>
              <p class="distance">1.5 km from temple</p>
              <p class="rating">★★★★☆ (4.1/5)</p>
              <p>Cozy cafe with a variety of coffee, tea, and light snacks.</p>
              <p>Price range: ₹300 - ₹500 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-info-circle"></i> Tourist Information</h3>
              <p><strong>Tamil Nadu Tourism Office</strong></p>
              <p class="distance">2 km from temple</p>
              <p>Official tourism information center with maps, guides, and tour booking services.</p>
              <p>Contact: +91 452 233 4757</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="nearby-attractions">
      <h2>Nearby Attractions</h2>
      <div class="attractions-grid">
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/thirumalai-nayakkar-palace_20190619141119.jpg" alt="Thirumalai Nayakkar Palace">
          <div class="attraction-card-content">
            <h3>Thirumalai Nayakkar Palace</h3>
            <p>A 17th-century palace showcasing Indo-Saracenic architectural style, built by King Thirumalai Nayak.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Gandhi_Museum_Madurai_20190212182004.jpg" alt="Gandhi Memorial Museum">
          <div class="attraction-card-content">
            <h3>Gandhi Memorial Museum</h3>
            <p>Housed in the historic Tamukkam Palace, displaying artifacts related to India's freedom struggle and Mahatma Gandhi.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Vandiyur_Mariamman_Teppakulam_20190212182005.jpg" alt="Vandiyur Mariamman Teppakulam">
          <div class="attraction-card-content">
            <h3>Vandiyur Mariamman Teppakulam</h3>
            <p>A massive temple tank with a mandapam (hall) in the center, famous for its float festival in January-February.</p>
          </div>
        </div>
      </div>
    </div>
  </main>

  <footer>
    <p>&copy; 2025 Heritage Explorer | Explore Tamil Nadu</p>
  </footer>

  <div id="backToTop">
    <i class="fas fa-arrow-up"></i>
  </div>

  <script>
    // Back to Top Button
    const backToTop = document.getElementById("backToTop");
    window.addEventListener("scroll", () => {
      if (window.scrollY > 300) {
        backToTop.style.display = "flex";
      } else {
        backToTop.style.display = "none";
      }
    });
    backToTop.addEventListener("click", () => {
      window.scrollTo({ top: 0, behavior: "smooth" });
    });

    // Audio Playback
    document.getElementById("playAudioButton").addEventListener("click", () => {
      const description = document.querySelector(".site-info p").textContent;
      const utterance = new SpeechSynthesisUtterance(description);
      utterance.lang = "en-US";
      speechSynthesis.speak(utterance);
    });

    // Favorite Button
    document.getElementById("favoriteButton").addEventListener("click", function() {
      const icon = this.querySelector("i");
      if (icon.classList.contains("far")) {
        icon.classList.remove("far");
        icon.classList.add("fas");
        alert("Added to favorites!");
      } else {
        icon.classList.remove("fas");
        icon.classList.add("far");
        alert("Removed from favorites!");
      }
    });

    // Share Button
    document.getElementById("shareButton").addEventListener("click", () => {
      const shareData = {
        title: "Meenakshi Amman Temple - Heritage Explorer",
        text: "Check out this beautiful temple in Tamil Nadu!",
        url: window.location.href
      };

      if (navigator.share) {
        navigator.share(shareData)
          .then(() => console.log("Shared successfully"))
          .catch((error) => console.error("Error sharing:", error));
      } else {
        navigator.clipboard.writeText(shareData.url)
          .then(() => alert("URL copied to clipboard"))
          .catch((err) => console.error("Could not copy text: ", err));
      }
    });

    // Initialize the travel info tabs
    document.addEventListener("DOMContentLoaded", function() {
      // Set the last updated date
      const today = new Date();
      document.getElementById("lastUpdated").textContent = today.toLocaleDateString();

      // Tab functionality
      const tabs = document.querySelectorAll(".travel-tab");
      tabs.forEach(tab => {
        tab.addEventListener("click", function() {
          // Remove active class from all tabs
          tabs.forEach(t => t.classList.remove("active"));

          // Add active class to clicked tab
          this.classList.add("active");

          // Hide all panels
          const panels = document.querySelectorAll(".travel-panel");
          panels.forEach(panel => panel.classList.remove("active"));

          // Show the corresponding panel
          const tabId = this.getAttribute("data-tab");
          document.getElementById(tabId + "Panel").classList.add("active");
        });
      });

      // Fetch weather data (mock)
      fetchWeatherData();
    });

    // Weather API Integration (Mock)
    function fetchWeatherData() {
      // Coordinates for Madurai
      const lat = 9.925;
      const lon = 78.119;

      // Mock weather data
      const mockWeatherData = {
        current: {
          temp: 34,
          humidity: 70,
          wind_speed: 8,
          weather: [{ main: "Clear", description: "Sunny", icon: "01d" }]
        },
        daily: [
          {
            dt: Date.now() + 86400000, // Tomorrow
            temp: { day: 33 },
            weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
          },
          {
            dt: Date.now() + *********, // Day after tomorrow
            temp: { day: 32 },
            weather: [{ main: "Clouds", description: "Cloudy", icon: "03d" }]
          },
          {
            dt: Date.now() + 259200000, // 3 days from now
            temp: { day: 31 },
            weather: [{ main: "Rain", description: "Light Rain", icon: "10d" }]
          }
        ]
      };

      updateWeatherUI(mockWeatherData);
    }

    function updateWeatherUI(data) {
      // Update current weather
      document.querySelector(".weather-temp").textContent = `${data.current.temp}°C`;
      document.querySelector(".weather-desc").textContent = data.current.weather[0].description;
      document.querySelector(".weather-extra").textContent = `Humidity: ${data.current.humidity}% | Wind: ${data.current.wind_speed} km/h`;

      // Update weather icon
      const weatherIconElement = document.querySelector(".weather-icon i");
      updateWeatherIcon(weatherIconElement, data.current.weather[0].main);

      // Update forecast
      const forecastDays = document.querySelectorAll(".forecast-day");
      data.daily.forEach((day, index) => {
        if (index < forecastDays.length) {
          const dayElement = forecastDays[index];
          const date = new Date(day.dt);
          const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });

          dayElement.querySelector("h4").textContent = index === 0 ? "Tomorrow" : dayName;
          dayElement.querySelector(".forecast-temp").textContent = `${day.temp.day}°C`;
          dayElement.querySelector(".forecast-desc").textContent = day.weather[0].description;

          // Update forecast icon
          const iconElement = dayElement.querySelector(".forecast-icon i");
          updateWeatherIcon(iconElement, day.weather[0].main);
        }
      });
    }

    function updateWeatherIcon(iconElement, weatherType) {
      // Remove all existing weather classes
      iconElement.className = "";

      // Add the appropriate weather icon class
      switch (weatherType) {
        case "Clear":
          iconElement.classList.add("fas", "fa-sun");
          break;
        case "Clouds":
          iconElement.classList.add("fas", "fa-cloud");
          break;
        case "Rain":
        case "Drizzle":
          iconElement.classList.add("fas", "fa-cloud-rain");
          break;
        case "Thunderstorm":
          iconElement.classList.add("fas", "fa-bolt");
          break;
        case "Snow":
          iconElement.classList.add("fas", "fa-snowflake");
          break;
        case "Mist":
        case "Fog":
        case "Haze":
          iconElement.classList.add("fas", "fa-smog");
          break;
        default:
          iconElement.classList.add("fas", "fa-cloud-sun");
      }
    }
  </script>

  <!-- Language Toggle Script -->
  <script src="language-toggle.js"></script>
</body>
</html>
