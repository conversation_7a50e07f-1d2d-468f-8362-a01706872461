#!/usr/bin/env node

/**
 * Heritage Explorer - Server Test Script
 * Tests server functionality and endpoints
 */

const http = require('http');
const fs = require('fs');

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m'
};

function log(type, message) {
    switch (type) {
        case 'success':
            console.log(`${colors.green}✅ ${message}${colors.reset}`);
            break;
        case 'error':
            console.log(`${colors.red}❌ ${message}${colors.reset}`);
            break;
        case 'warning':
            console.log(`${colors.yellow}⚠️  ${message}${colors.reset}`);
            break;
        case 'info':
            console.log(`${colors.blue}ℹ️  ${message}${colors.reset}`);
            break;
        default:
            console.log(message);
    }
}

// Test HTTP endpoint
function testEndpoint(host, port, path) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: host,
            port: port,
            path: path,
            method: 'GET',
            timeout: 5000
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    data: data
                });
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        req.on('timeout', () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });

        req.end();
    });
}

// Test file existence
function testFiles() {
    log('info', 'Testing project files...');
    
    const requiredFiles = [
        'website.html',
        'login.html',
        'signup.html',
        'auth.php',
        'config.php',
        'auth.js',
        'package.json'
    ];
    
    let allFilesExist = true;
    
    requiredFiles.forEach(file => {
        if (fs.existsSync(file)) {
            log('success', `Found: ${file}`);
        } else {
            log('error', `Missing: ${file}`);
            allFilesExist = false;
        }
    });
    
    return allFilesExist;
}

// Test development server
async function testDevServer() {
    log('info', 'Testing development server...');
    
    const endpoints = [
        '/',
        '/website.html',
        '/login.html',
        '/signup.html',
        '/auth.php?action=check_session'
    ];
    
    for (const endpoint of endpoints) {
        try {
            const result = await testEndpoint('localhost', 3000, endpoint);
            if (result.statusCode === 200) {
                log('success', `Dev server ${endpoint} - OK (${result.statusCode})`);
            } else {
                log('warning', `Dev server ${endpoint} - ${result.statusCode}`);
            }
        } catch (error) {
            log('error', `Dev server ${endpoint} - ${error.message}`);
        }
    }
}

// Test XAMPP server
async function testXamppServer() {
    log('info', 'Testing XAMPP server...');
    
    const endpoints = [
        '/heritage-explorer/',
        '/heritage-explorer/website.html',
        '/heritage-explorer/auth.php?action=check_session'
    ];
    
    for (const endpoint of endpoints) {
        try {
            const result = await testEndpoint('localhost', 80, endpoint);
            if (result.statusCode === 200) {
                log('success', `XAMPP server ${endpoint} - OK (${result.statusCode})`);
            } else {
                log('warning', `XAMPP server ${endpoint} - ${result.statusCode}`);
            }
        } catch (error) {
            log('error', `XAMPP server ${endpoint} - ${error.message}`);
        }
    }
}

// Main test function
async function runTests() {
    console.log('\n🧪 Heritage Explorer - Server Tests\n');
    console.log('='.repeat(40));
    
    // Test 1: File existence
    console.log('\n📁 File Tests:');
    const filesOk = testFiles();
    
    // Test 2: Development server
    console.log('\n🚀 Development Server Tests:');
    await testDevServer();
    
    // Test 3: XAMPP server
    console.log('\n🐘 XAMPP Server Tests:');
    await testXamppServer();
    
    // Summary
    console.log('\n' + '='.repeat(40));
    console.log('📊 Test Summary:');
    
    if (filesOk) {
        log('success', 'All required files are present');
    } else {
        log('error', 'Some required files are missing');
    }
    
    console.log('\n💡 Next Steps:');
    console.log('• If dev server tests pass: Use pnpm dev for development');
    console.log('• If XAMPP tests pass: Use http://localhost/heritage-explorer/');
    console.log('• If tests fail: Check server status and file placement');
    
    console.log('\n🔗 Useful Commands:');
    console.log('• pnpm dev     - Start development server');
    console.log('• pnpm xampp   - Setup XAMPP');
    console.log('• pnpm setup   - Run project setup');
    
    console.log('\n' + '='.repeat(40) + '\n');
}

// Run tests if called directly
if (require.main === module) {
    runTests().catch(error => {
        log('error', `Test failed: ${error.message}`);
        process.exit(1);
    });
}

module.exports = { runTests, testEndpoint, testFiles };
