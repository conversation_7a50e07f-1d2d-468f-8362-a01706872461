<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="description" content="Heritage Explorer helps you discover Tamil Nadu's rich heritage and culture." />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Heritage Explorer</title>
  <!-- Import Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    /* Professional CSS Styling */
    :root {
      --primary-color: #e63946;
      --secondary-color: #457b9d;
      --accent-color: #1d3557;
      --light-color: #f1faee;
      --dark-color: #1d3557;
      --success-color: #2a9d8f;
      --warning-color: #e9c46a;
      --danger-color: #e76f51;
      --gray-100: #f8f9fa;
      --gray-200: #e9ecef;
      --gray-300: #dee2e6;
      --gray-400: #ced4da;
      --gray-500: #adb5bd;
      --gray-600: #6c757d;
      --gray-700: #495057;
      --gray-800: #343a40;
      --gray-900: #212529;
      --border-radius: 8px;
      --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      --transition: all 0.3s ease;
    }

    /* Base Styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Poppins', sans-serif;
      line-height: 1.6;
      color: var(--gray-800);
      background-color: var(--gray-100);
      padding-bottom: 60px;
    }

    a {
      color: var(--secondary-color);
      text-decoration: none;
      transition: var(--transition);
    }

    a:hover {
      color: var(--primary-color);
    }

    button {
      cursor: pointer;
      font-family: 'Poppins', sans-serif;
      transition: var(--transition);
    }

    img {
      max-width: 100%;
      height: auto;
      border-radius: var(--border-radius);
    }

    /* Header Styles */
    header {
      background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
      color: white;
      padding: 2rem 1rem;
      text-align: center;
      position: relative;
      box-shadow: var(--box-shadow);
    }

    #mainHeading {
      font-size: 2.5rem;
      margin-bottom: 0.5rem;
      font-weight: 700;
    }

    #subHeading {
      font-size: 1.2rem;
      font-weight: 300;
      margin-bottom: 1.5rem;
    }

    .language-toggle {
      display: flex;
      justify-content: center;
      gap: 1rem;
    }

    .language-toggle button {
      background-color: rgba(255, 255, 255, 0.2);
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
      font-weight: 500;
    }

    .language-toggle button:hover {
      background-color: rgba(255, 255, 255, 0.3);
    }

    .user-info {
      display: flex;
      gap: 0.5rem;
      align-items: center;
    }

    .auth-link {
      background-color: rgba(255, 255, 255, 0.2);
      color: white;
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
      font-weight: 500;
      font-size: 0.9rem;
      transition: var(--transition);
      display: flex;
      align-items: center;
      gap: 0.3rem;
    }

    .auth-link:hover {
      background-color: rgba(255, 255, 255, 0.3);
      color: white;
    }

    .auth-link.login-link {
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .auth-link.signup-link {
      background-color: var(--warning-color);
    }

    .auth-link.signup-link:hover {
      background-color: #d4a853;
    }

    .user-welcome {
      color: white;
      font-weight: 500;
      margin-right: 1rem;
    }

    .logout-btn {
      background-color: rgba(255, 255, 255, 0.2);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
    }

    .logout-btn:hover {
      background-color: rgba(255, 255, 255, 0.3);
    }

    /* Main Content Styles */
    main {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem 1rem;
    }

    /* Search Container Styles */
    .search-container {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      margin-bottom: 2rem;
      align-items: center;
    }

    #searchBar {
      flex: 1;
      min-width: 200px;
      padding: 0.8rem 1rem;
      border: 1px solid var(--gray-300);
      border-radius: var(--border-radius);
      font-size: 1rem;
      outline: none;
      transition: var(--transition);
    }

    #searchBar:focus {
      border-color: var(--secondary-color);
      box-shadow: 0 0 0 3px rgba(69, 123, 157, 0.2);
    }

    #micButton {
      background-color: var(--light-color);
      border: 1px solid var(--gray-300);
      border-radius: var(--border-radius);
      padding: 0.8rem;
      font-size: 1rem;
    }

    #micButton:hover {
      background-color: var(--gray-200);
    }

    #currentLocationButton {
      background-color: var(--secondary-color);
      color: white;
      border: none;
      border-radius: var(--border-radius);
      padding: 0.8rem 1rem;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    #currentLocationButton:hover {
      background-color: var(--accent-color);
    }

    .spinner {
      width: 20px;
      height: 20px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s ease-in-out infinite;
      margin-left: 0.5rem;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    /* Filter Container Styles */
    .filter-container {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .location-filter, .category-filter {
      flex: 1;
      min-width: 200px;
    }

    .filter-container label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: var(--gray-700);
    }

    .filter-container select {
      width: 100%;
      padding: 0.8rem;
      border: 1px solid var(--gray-300);
      border-radius: var(--border-radius);
      background-color: white;
      font-size: 1rem;
      outline: none;
      transition: var(--transition);
    }

    .filter-container select:focus {
      border-color: var(--secondary-color);
      box-shadow: 0 0 0 3px rgba(69, 123, 157, 0.2);
    }

    /* Category Buttons Styles */
    .category-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 0.8rem;
      margin-bottom: 2rem;
      justify-content: center;
    }

    .category-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: white;
      border: 1px solid var(--gray-300);
      border-radius: var(--border-radius);
      padding: 1rem;
      min-width: 100px;
      text-align: center;
      transition: var(--transition);
      cursor: pointer;
    }

    .category-btn i {
      font-size: 1.5rem;
      margin-bottom: 0.5rem;
      color: var(--secondary-color);
    }

    .category-btn span {
      font-size: 0.9rem;
      font-weight: 500;
      color: var(--gray-700);
    }

    .category-btn:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      border-color: var(--secondary-color);
    }

    .category-btn.active {
      background-color: var(--secondary-color);
      border-color: var(--secondary-color);
    }

    .category-btn.active i,
    .category-btn.active span {
      color: white;
    }

    /* Category Filter Section */
    .categories-section {
      margin-bottom: 2rem;
    }

    .categories-section h2 {
      text-align: center;
      margin-bottom: 1.5rem;
      color: var(--accent-color);
      font-size: 1.5rem;
    }

    .categories-container {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      justify-content: center;
    }

    .category-filter-btn {
      background-color: white;
      border: 1px solid var(--gray-300);
      border-radius: var(--border-radius);
      padding: 0.8rem 1.5rem;
      font-size: 0.9rem;
      font-weight: 500;
      color: var(--gray-700);
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: var(--transition);
    }

    .category-filter-btn i {
      color: var(--secondary-color);
    }

    .category-filter-btn:hover {
      background-color: var(--light-color);
      border-color: var(--secondary-color);
    }

    .category-filter-btn.active {
      background-color: var(--secondary-color);
      color: white;
      border-color: var(--secondary-color);
    }

    .category-filter-btn.active i {
      color: white;
    }

    /* Chatbot Typing Indicator */
    .typing-indicator {
      display: flex;
      align-items: center;
    }

    .typing-indicator span {
      height: 8px;
      width: 8px;
      float: left;
      margin: 0 1px;
      background-color: var(--gray-500);
      display: block;
      border-radius: 50%;
      opacity: 0.4;
    }

    .typing-indicator span:nth-of-type(1) {
      animation: 1s blink infinite 0.3333s;
    }

    .typing-indicator span:nth-of-type(2) {
      animation: 1s blink infinite 0.6666s;
    }

    .typing-indicator span:nth-of-type(3) {
      animation: 1s blink infinite 0.9999s;
    }

    @keyframes blink {
      50% {
        opacity: 1;
      }
    }

    /* Chatbot Voice Controls */
    #chatbotMicButton, #chatbotVoiceToggle {
      background-color: transparent;
      border: none;
      color: var(--gray-600);
      font-size: 1.2rem;
      cursor: pointer;
      transition: var(--transition);
      padding: 0.5rem;
    }

    #chatbotMicButton:hover, #chatbotVoiceToggle:hover {
      color: var(--secondary-color);
    }

    #chatbotMicButton.listening {
      color: var(--danger-color);
      animation: pulse 1.5s infinite;
    }

    #chatbotVoiceToggle.active {
      color: var(--success-color);
    }

    @keyframes pulse {
      0% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.1);
      }
      100% {
        transform: scale(1);
      }
    }

    .system-message {
      background-color: var(--gray-200) !important;
      color: var(--gray-700) !important;
      font-style: italic;
      font-size: 0.9rem;
    }

    /* Site List Styles */
    .site-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 2rem;
    }

    .site-card {
      background-color: white;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
    }

    .site-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .site-card img {
      width: 100%;
      height: 200px;
      object-fit: cover;
      border-radius: var(--border-radius) var(--border-radius) 0 0;
    }

    .site-card-content {
      padding: 1.5rem;
    }

    .site-card h3 {
      font-size: 1.25rem;
      margin-bottom: 0.5rem;
      color: var(--accent-color);
    }

    .site-category {
      display: inline-block;
      background-color: var(--light-color);
      color: var(--accent-color);
      padding: 0.3rem 0.8rem;
      border-radius: 50px;
      font-size: 0.8rem;
      font-weight: 500;
      margin-bottom: 0.8rem;
    }

    .site-card p {
      margin-bottom: 1rem;
      color: var(--gray-700);
    }

    .site-card button {
      background-color: transparent;
      border: 1px solid var(--gray-300);
      border-radius: var(--border-radius);
      padding: 0.5rem 0.8rem;
      margin-right: 0.5rem;
      margin-bottom: 0.5rem;
      font-size: 0.9rem;
      color: var(--gray-700);
    }

    .site-card button:hover {
      background-color: var(--gray-200);
    }

    .site-card .viewButton {
      background-color: var(--primary-color);
      color: white;
      border: none;
    }

    .site-card .viewButton:hover {
      background-color: #d62b39;
    }

    .site-card .nearbyButton {
      background-color: var(--secondary-color);
      color: white;
      border: none;
    }

    .site-card .nearbyButton:hover {
      background-color: #3a6a89;
    }

    .site-card .favoriteButton {
      background-color: var(--warning-color);
      color: var(--gray-800);
      border: none;
    }

    .site-card .favoriteButton:hover {
      background-color: #e0bb5c;
    }

    /* Modal Styles */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.7);
      z-index: 1000;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .modal {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: white;
      border-radius: var(--border-radius);
      padding: 2rem;
      max-width: 600px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;
      z-index: 1001;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    /* Map Styles */
    #mapContainer {
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .modal-buttons {
      display: flex;
      justify-content: space-between;
      margin-top: 1rem;
    }

    #toggleViewButton {
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: var(--border-radius);
      padding: 0.8rem 1.5rem;
      font-weight: 500;
    }

    #toggleViewButton:hover {
      background-color: #d62b39;
    }

    .modal h2 {
      color: var(--accent-color);
      margin-bottom: 1rem;
      font-size: 1.5rem;
    }

    .modal p {
      margin-bottom: 1.5rem;
      line-height: 1.6;
    }

    .modal button {
      background-color: var(--secondary-color);
      color: white;
      border: none;
      border-radius: var(--border-radius);
      padding: 0.8rem 1.5rem;
      margin-right: 1rem;
      font-weight: 500;
    }

    .modal button:hover {
      background-color: var(--accent-color);
    }

    #closeModalButton, #closeNearbyModalButton, #closeFavoritesModalButton, #closeReviewsModalButton {
      background-color: var(--gray-500);
    }

    #closeModalButton:hover, #closeNearbyModalButton:hover, #closeFavoritesModalButton:hover, #closeReviewsModalButton:hover {
      background-color: var(--gray-600);
    }

    #playAudio {
      background-color: var(--success-color);
    }

    #playAudio:hover {
      background-color: #238b7e;
    }

    /* Reviews Modal Styles */
    #reviewsList {
      margin-bottom: 1.5rem;
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid var(--gray-300);
      border-radius: var(--border-radius);
      padding: 1rem;
    }

    .reviewsItem {
      padding: 0.8rem;
      border-bottom: 1px solid var(--gray-300);
    }

    .reviewsItem:last-child {
      border-bottom: none;
    }

    #newReview {
      width: 100%;
      padding: 0.8rem;
      border: 1px solid var(--gray-300);
      border-radius: var(--border-radius);
      margin-bottom: 1rem;
      min-height: 100px;
      resize: vertical;
      font-family: 'Poppins', sans-serif;
    }

    #newReview:focus {
      border-color: var(--secondary-color);
      outline: none;
    }

    #submitReviewButton {
      background-color: var(--success-color);
    }

    #submitReviewButton:hover {
      background-color: #238b7e;
    }

    /* Favorites Modal Styles */
    #favoritesList {
      margin-bottom: 1.5rem;
    }

    .favoritesItem {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.8rem;
      border-bottom: 1px solid var(--gray-300);
    }

    .favoritesItem:last-child {
      border-bottom: none;
    }

    /* Chatbot Styles */
    #chatbotContainer {
      position: fixed;
      bottom: 80px;
      right: 20px;
      width: 350px;
      height: 450px;
      background-color: white;
      border-radius: var(--border-radius);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      display: none;
      flex-direction: column;
      overflow: hidden;
      z-index: 999;
    }

    #chatbotHeader {
      background-color: var(--accent-color);
      color: white;
      padding: 1rem;
      font-weight: 500;
    }

    #chatbotMessages {
      flex: 1;
      overflow-y: auto;
      padding: 1rem;
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    #chatbotMessages .user,
    #chatbotMessages .bot {
      max-width: 80%;
      padding: 0.8rem 1rem;
      border-radius: 1rem;
      line-height: 1.4;
    }

    #chatbotMessages .user {
      align-self: flex-end;
      background-color: var(--secondary-color);
      color: white;
      border-bottom-right-radius: 0;
    }

    #chatbotMessages .bot {
      align-self: flex-start;
      background-color: var(--gray-200);
      color: var(--gray-800);
      border-bottom-left-radius: 0;
    }

    #chatbotInputContainer {
      display: flex;
      padding: 1rem;
      border-top: 1px solid var(--gray-300);
    }

    #chatbotInput {
      flex: 1;
      padding: 0.8rem;
      border: 1px solid var(--gray-300);
      border-radius: var(--border-radius) 0 0 var(--border-radius);
      outline: none;
    }

    #chatbotInput:focus {
      border-color: var(--secondary-color);
    }

    #chatbotSendButton {
      background-color: var(--secondary-color);
      color: white;
      border: none;
      border-radius: 0 var(--border-radius) var(--border-radius) 0;
      padding: 0 1rem;
    }

    #chatbotSendButton:hover {
      background-color: var(--accent-color);
    }

    #chatbotToggle {
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 50px;
      height: 50px;
      background-color: var(--secondary-color);
      color: white;
      border: none;
      border-radius: 50%;
      font-size: 1.5rem;
      display: flex;
      justify-content: center;
      align-items: center;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
      z-index: 998;
    }

    #chatbotToggle:hover {
      background-color: var(--accent-color);
    }

    /* Back to Top Button */
    #backToTop {
      position: fixed;
      bottom: 20px;
      right: 80px;
      width: 50px;
      height: 50px;
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: 50%;
      font-size: 1.5rem;
      display: flex;
      justify-content: center;
      align-items: center;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
      z-index: 998;
    }

    #backToTop:hover {
      background-color: #d62b39;
    }

    /* Footer Styles */
    footer {
      background-color: var(--accent-color);
      color: white;
      text-align: center;
      padding: 1.5rem;
      position: fixed;
      bottom: 0;
      width: 100%;
    }

    /* Utility Classes */
    .hidden {
      display: none !important;
    }

    /* Responsive Styles */
    @media (max-width: 768px) {
      #mainHeading {
        font-size: 2rem;
      }

      .site-list {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1.5rem;
      }

      .modal {
        width: 95%;
        padding: 1.5rem;
      }

      #chatbotContainer {
        width: 300px;
        height: 400px;
        bottom: 70px;
      }

      #backToTop, #chatbotToggle {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
      }
    }

    @media (max-width: 480px) {
      header {
        padding: 1.5rem 1rem;
      }

      #mainHeading {
        font-size: 1.8rem;
      }

      #subHeading {
        font-size: 1rem;
      }

      .search-container {
        flex-direction: column;
        align-items: stretch;
      }

      #searchBar, #micButton, #currentLocationButton {
        width: 100%;
      }

      .site-list {
        grid-template-columns: 1fr;
      }

      .modal {
        padding: 1rem;
      }

      #chatbotContainer {
        width: 90%;
        right: 5%;
        left: 5%;
      }

      #backToTop, #chatbotToggle {
        bottom: 70px;
      }

      footer {
        padding: 1rem;
      }
    }
  </style>
</head>
<body>
  <header>
    <h1 id="mainHeading">Heritage Explorer</h1>
    <p id="subHeading">Discover Tamil Nadu's rich heritage and culture</p>
    <!-- Language Toggle Navigation and User Actions -->
    <nav class="language-toggle" aria-label="Language Toggle">
      <button type="button" id="toggleLang" aria-label="Toggle Language">தமிழ்</button>
      <button type="button" id="favoritesButton" aria-label="View Favorites">Favorites</button>
      <div id="userInfo" class="user-info">
        <a href="login.html" class="auth-link login-link">
          <i class="fas fa-sign-in-alt"></i> Login
        </a>
        <a href="signup.html" class="auth-link signup-link">
          <i class="fas fa-user-plus"></i> Sign Up
        </a>
      </div>
    </nav>
  </header>

  <main>
    <!-- Search Bar -->
    <div class="search-container">
      <input
        type="search"
        id="searchBar"
        placeholder="Search Heritage Sites..."
        aria-label="Search Heritage Sites"
      />
      <button type="button" id="micButton" aria-label="Voice Search"><i class="fas fa-microphone"></i></button>
      <!-- Current Location Button -->
      <button type="button" id="currentLocationButton" aria-label="Find Nearby Sites">
        <i class="fas fa-map-marker-alt"></i> Find Nearby Sites
      </button>
      <div id="locationSpinner" class="spinner hidden" aria-hidden="true"></div>
    </div>

    <!-- Filter Dropdowns -->
    <div class="filter-container">
      <div class="location-filter">
        <label for="locationFilter">Filter by Location:</label>
        <select id="locationFilter">
          <option value="">All Locations</option>
        </select>
      </div>
      <div class="category-filter">
        <label for="categoryFilter">Filter by Category:</label>
        <select id="categoryFilter">
          <option value="">All Categories</option>
        </select>
      </div>
    </div>

    <!-- Categories Section -->
    <section class="categories-section">
      <h2>Explore by Category</h2>
      <div class="categories-container">
        <button class="category-filter-btn active" data-category="">
          <i class="fas fa-globe"></i> All Places
        </button>
        <button class="category-filter-btn" data-category="Temple">
          <i class="fas fa-gopuram"></i> Temples
        </button>
        <button class="category-filter-btn" data-category="Beach">
          <i class="fas fa-umbrella-beach"></i> Beaches
        </button>
        <button class="category-filter-btn" data-category="Hill Station">
          <i class="fas fa-mountain"></i> Hill Stations
        </button>
        <button class="category-filter-btn" data-category="Monument">
          <i class="fas fa-landmark"></i> Monuments
        </button>
        <button class="category-filter-btn" data-category="Museum">
          <i class="fas fa-museum"></i> Museums
        </button>
        <button class="category-filter-btn" data-category="Palace">
          <i class="fas fa-place-of-worship"></i> Palaces
        </button>
      </div>
    </section>

    <!-- Heritage Sites List -->
    <section class="site-list" id="siteList" aria-label="Heritage Sites">
      <!-- Dynamic site cards will be injected here -->
    </section>
  </main>

  <!-- Site Modal -->
  <div id="siteModal" class="modal hidden" role="dialog" aria-modal="true" aria-labelledby="modalTitle">
    <h2 id="modalTitle"></h2>
    <p id="modalDescription"></p>
    <button id="playAudio" type="button"><i class="fas fa-play"></i> Play Audio</button>
    <button id="closeModalButton" type="button" aria-label="Close Modal"><i class="fas fa-times"></i> Close</button>
  </div>
  <div class="modal-overlay hidden" id="modalOverlay"></div>

  <!-- Nearby Places Modal -->
  <div class="modal-overlay hidden" id="nearbyOverlay"></div>
  <div class="modal hidden" id="nearbyModal" role="dialog" aria-modal="true" aria-labelledby="nearbyModalTitle">
    <h2 id="nearbyModalTitle">Nearby Places</h2>
    <div id="mapContainer" style="width: 100%; height: 300px; margin-bottom: 15px;"></div>
    <div id="nearbyList"></div>
    <div class="modal-buttons">
      <button type="button" id="toggleViewButton">Show Map</button>
      <button type="button" id="findNearbyButton" aria-label="Find Nearby Sites Using GPS">
        <i class="fas fa-map-marker-alt"></i> Find Nearby Sites
      </button>
      <button type="button" id="closeNearbyModalButton"><i class="fas fa-times"></i> Close</button>
    </div>

    <script>
      // Update button text based on current language when the page loads
      document.addEventListener("DOMContentLoaded", function() {
        const updateNearbyModalButtons = function() {
          const toggleButton = document.getElementById("toggleViewButton");
          const findNearbyButton = document.getElementById("findNearbyButton");
          const closeButton = document.getElementById("closeNearbyModalButton");
          const modalTitle = document.getElementById("nearbyModalTitle");

          if (window.currentLanguage === "ta") {
            modalTitle.textContent = "அருகிலுள்ள இடங்கள்"; // Nearby Places
            toggleButton.textContent = "வரைபடத்தைக் காட்டு"; // Show Map
            findNearbyButton.innerHTML = '<i class="fas fa-map-marker-alt"></i> அருகிலுள்ள தளங்களைக் கண்டறிக'; // Find Nearby Sites
            closeButton.innerHTML = '<i class="fas fa-times"></i> மூடு'; // Close
          } else {
            modalTitle.textContent = "Nearby Places";
            toggleButton.textContent = "Show Map";
            findNearbyButton.innerHTML = '<i class="fas fa-map-marker-alt"></i> Find Nearby Sites';
            closeButton.innerHTML = '<i class="fas fa-times"></i> Close';
          }
        };

        // Update initially and when language changes
        updateNearbyModalButtons();

        // Listen for language changes
        document.getElementById("toggleLang").addEventListener("click", function() {
          setTimeout(updateNearbyModalButtons, 100); // Small delay to ensure currentLanguage is updated
        });
      });
    </script>
  </div>

  <!-- Favorites Modal -->
  <div class="modal-overlay hidden" id="favoritesOverlay"></div>
  <div class="modal hidden" id="favoritesModal" role="dialog" aria-modal="true" aria-labelledby="favoritesModalTitle">
    <h2 id="favoritesModalTitle">Favorite Sites</h2>
    <div id="favoritesList"></div>
    <button type="button" id="closeFavoritesModalButton"><i class="fas fa-times"></i> Close</button>
  </div>

  <!-- Reviews Modal -->
  <div class="modal-overlay hidden" id="reviewsOverlay"></div>
  <div class="modal hidden" id="reviewsModal" role="dialog" aria-modal="true" aria-labelledby="reviewsModalTitle">
    <h2 id="reviewsModalTitle">
      Reviews for <span id="reviewsSiteName"></span>
    </h2>
    <div id="reviewsList"></div>
    <textarea id="newReview" placeholder="Write your review here..."></textarea>
    <button type="button" id="submitReviewButton">Submit Review</button>
    <button type="button" id="closeReviewsModalButton">Close</button>
  </div>



  <!-- Chatbot -->
  <div id="chatbotContainer" role="complementary" aria-label="Heritage Chatbot">
    <div id="chatbotHeader">Heritage Chatbot</div>
    <div id="chatbotMessages" aria-live="polite"></div>
    <div id="chatbotInputContainer">
      <input
        id="chatbotInput"
        type="text"
        placeholder="Ask me anything..."
        aria-label="Chatbot Input"
      />
      <button type="button" id="chatbotMicButton" aria-label="Voice Input"><i class="fas fa-microphone"></i></button>
      <button type="button" id="chatbotVoiceToggle" aria-label="Toggle Voice Output"><i class="fas fa-volume-mute"></i></button>
      <button type="button" id="chatbotSendButton" aria-label="Send Message">Send</button>
    </div>
  </div>
  <button type="button" id="chatbotToggle" aria-label="Toggle Chatbot"><i class="fas fa-comments"></i></button>

  <!-- Back-to-Top Button -->
  <button type="button" id="backToTop" class="hidden" aria-label="Back to Top"><i class="fas fa-arrow-up"></i></button>

  <footer>
    <p id="footerText">&copy; 2025 Heritage Explorer | Explore Tamil Nadu</p>
  </footer>

  <!-- Include the heritage chatbot script -->
  <script src="heritage-chatbot.js"></script>
  <!-- Google Maps API -->
  <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyA5a7QFuqrEGFEJnJQvdgYUb-C3RRXmdlE&callback=initMap" async defer></script>

  <script>
    /***** Speech Recognition Setup *****/
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const SpeechGrammarList = window.SpeechGrammarList || window.webkitSpeechGrammarList;
    const SpeechSynthesisUtterance = window.SpeechSynthesisUtterance;

    /***** Analytics and Event Tracking *****/
    function trackEvent(eventName, eventData) {
      console.log("Tracking Event:", eventName, eventData);
      // For production, send this data to an analytics service.
    }

    /***** Mapping and Data *****/
    const detailsPageMapping = {
      // Temples
      "Brihadeeswarar Temple": "brihadeeswarar-temple.html",
      "Meenakshi Amman Temple": "meenakshi-amman-temple.html",
      "Ramanathaswamy Temple": "ramanathaswamy-temple.html",
      "Gangaikonda Cholapuram": "gangaikonda-cholapuram.html",

      // Monuments
      "Mahabalipuram": "mahabalipuram.html",
      "Shore Temple": "shore-temple.html",
      "Vivekananda Rock Memorial": "vivekananda-rock-memorial.html",
      "Gingee Fort": "gingee-fort.html",

      // Palaces
      "Thanjavur Palace": "thanjavur-palace.html",
      "Madurai Palace": "madurai-palace.html",
      "Chettinad Palace": "chettinad-palace.html",

      // Hill Stations
      "Ooty Hill Station": "ooty.html",
      "Kodaikanal": "kodaikanal.html",
      "Yelagiri Hills": "yelagiri-hills.html",
      "Kolli Hills": "kolli-hills.html",

      // Museums
      "Government Museum Chennai": "government-museum-chennai.html",
      "Thanjavur Art Gallery": "thanjavur-art-gallery.html",
      "DakshinaChitra Museum": "dakshinachitra-museum.html",

      // Beaches
      "Marina Beach": "marina-beach.html",
      "Covelong Beach": "covelong-beach.html",
      "Elliot's Beach": "elliots-beach.html",
      "Silver Beach": "silver-beach.html"
    };

    // Create a mapping between Tamil site names and English site names
    const tamilToEnglishNameMapping = {};

    // Initialize the mapping when the page loads
    function initializeTamilNameMapping() {
      // Loop through all Tamil sites and create a mapping to English names
      heritageSites.ta.forEach((tamilSite) => {
        // Find the corresponding English site with the same coordinates
        const englishSite = heritageSites.en.find(
          (site) =>
            site.coords.lat === tamilSite.coords.lat &&
            site.coords.lon === tamilSite.coords.lon
        );

        if (englishSite) {
          tamilToEnglishNameMapping[tamilSite.name] = englishSite.name;
        }
      });
    }

    // Call this after the heritageSites object is fully defined
    setTimeout(initializeTamilNameMapping, 0);

    function navigateToDetails(siteName) {
      let pageToNavigate;

      // If we're in Tamil mode, first convert the Tamil site name to English
      if (currentLanguage === "ta" && tamilToEnglishNameMapping[siteName]) {
        const englishName = tamilToEnglishNameMapping[siteName];
        pageToNavigate = detailsPageMapping[englishName];
      } else {
        // Otherwise use the name directly
        pageToNavigate = detailsPageMapping[siteName];
      }

      if (pageToNavigate) {
        window.location.href = pageToNavigate;
      } else {
        alert("Details page not found for " + siteName);
      }
    }

    const heritageSites = {
      en: [
        {
          name: "Brihadeeswarar Temple",
          location: "Thanjavur",
          category: "Temple",
          image:
            "https://dynamic-media-cdn.tripadvisor.com/media/photo-o/17/6a/e5/a7/thanjavur-brihadeeshwara.jpg?w=1200&h=1200&s=1",
          description:
            "A UNESCO World Heritage Site and a masterpiece of Chola architecture.",
          coords: { lat: 10.787, lon: 79.137 }
        },
        {
          name: "Meenakshi Amman Temple",
          location: "Madurai",
          category: "Temple",
          image:
            "https://tse1.mm.bing.net/th?id=OIP.3B2tPbPkrxw-phM-4Q6AqgHaE8&pid=Api&P=0&h=220",
          description:
            "A historic Hindu temple dedicated to Goddess Meenakshi.",
          coords: { lat: 9.925, lon: 78.1198 }
        },
        {
          name: "Mahabalipuram",
          location: "Chennai",
          category: "Monument",
          image:
            "https://tse3.mm.bing.net/th?id=OIP.fOvloesqytzzB8BpKC0eAAHaE5&pid=Api&P=0&h=220",
          description: "Famous for its rock-cut temples and sculptures.",
          coords: { lat: 12.620, lon: 80.192 }
        },
        {
          name: "Thanjavur Palace",
          location: "Thanjavur",
          category: "Palace",
          image:
            "https://tse4.mm.bing.net/th?id=OIP.SfjBkUFRZHrPwcb0pciZawHaE8&pid=Api&P=0&h=220",
          description:
            "A magnificent palace built during the Nayak period, known for its architecture and art gallery.",
          coords: { lat: 10.787, lon: 79.137 }
        },
        {
          name: "Ramanathaswamy Temple",
          location: "Rameswaram",
          category: "Temple",
          image:
            "https://tse4.mm.bing.net/th?id=OIP.GdA3HPrA9ByDA7kogoKstAHaE8&pid=Api&P=0&h=220",
          description:
            "Famous for its long corridors and Dravidian architectural style, dedicated to Lord Shiva.",
          coords: { lat: 9.288, lon: 79.312 }
        },
        {
          name: "Gangaikonda Cholapuram",
          location: "Jayankondam",
          category: "Temple",
          image:
            "https://tse4.mm.bing.net/th?id=OIP.JmWCuZVHlJQjjR574QohzwHaEK&pid=Api&P=0&h=220",
          description:
            "An architectural marvel built by the Cholas, featuring intricate carvings and sculptures.",
          coords: { lat: 10.712, lon: 79.360 }
        },
        {
          name: "Ooty Hill Station",
          location: "Nilgiris",
          category: "Hill Station",
          image:
            "https://tse4.mm.bing.net/th?id=OIP.WK3IXY-POi8acLl6iYaAywHaE9&pid=Api&P=0&h=220",
          description:
            "A picturesque hill station known for its tea gardens, botanical gardens, and pleasant climate.",
          coords: { lat: 11.413, lon: 76.695 }
        },
        {
          name: "Kodaikanal",
          location: "Dindigul",
          category: "Hill Station",
          image:
            "https://tse4.mm.bing.net/th?id=OIP.dJ6h2o6dAWoAN89d2Sjq6QHaDm&pid=Api&P=0&h=220",
          description:
            "A beautiful hill station with a star-shaped lake, known as the 'Princess of Hill Stations'.",
          coords: { lat: 10.238, lon: 77.488 }
        },
        {
          name: "Yelagiri Hills",
          location: "Vellore",
          category: "Hill Station",
          image:
            "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSRZ5_RlR2WhCc02rurg1mxrWAVBVSwQOS6lA&s",
          description:
            "A serene hill station with orchards, rose gardens, and artificial lakes, perfect for trekking and paragliding.",
          coords: { lat: 12.583, lon: 78.647 }
        },
        {
          name: "Kolli Hills",
          location: "Namakkal",
          category: "Hill Station",
          image:
            "https://t4.ftcdn.net/jpg/03/13/43/13/360_F_313431363_UClpHmoAEm0R2hmFU93piya9ScEFSiYm.jpg",
          description:
            "Known as 'Mountain of Death' due to its 70 hairpin bends, famous for its Agaya Gangai waterfalls and medicinal herbs.",
          coords: { lat: 11.248, lon: 78.330 }
        },
        {
          name: "Government Museum Chennai",
          location: "Chennai",
          category: "Museum",
          image:
            "https://chennaitourism.travel/images/places-to-visit/headers/chennai-government-museum-tourism-entry-fee-timings-holidays-reviews-header.jpg",
          description:
            "One of the oldest museums in India with a rich collection of archaeological and numismatic artifacts.",
          coords: { lat: 13.072, lon: 80.258 }
        },
        {
          name: "Thanjavur Art Gallery",
          location: "Thanjavur",
          category: "Museum",
          image:
            "https://c8.alamy.com/comp/C6CAD7/the-ornate-durbar-hall-of-tanjore-palace-in-thanjavur-tamil-nadu-india-C6CAD7.jpg",
          description:
            "Houses an exquisite collection of bronze and stone sculptures from the Chola period, along with paintings and artifacts.",
          coords: { lat: 10.785, lon: 79.135 }
        },
        {
          name: "DakshinaChitra Museum",
          location: "Chennai",
          category: "Museum",
          image:
            "https://lh3.googleusercontent.com/proxy/CTo84TvIsTmfzTStThmionWSzH2v4K3B5S1HwWwizeNaBgCi6SL0IBd0b4zaEGPdy46EUn3qLjLqw5gan8t2EAteDyO_rbXIi6NQtWUQ5_ia2ckJbFF1cEaFcBwXRnqsBnbcw8uY4YFNRuaSVBYzmCQ",
          description:
            "A living museum showcasing the art, architecture, lifestyles, and crafts of South India through reconstructed traditional houses.",
          coords: { lat: 12.825, lon: 80.230 }
        },
        {
          name: "Marina Beach",
          location: "Chennai",
          category: "Beach",
          image:
            "https://t4.ftcdn.net/jpg/04/84/47/27/360_F_484472702_acpl3SZTBwb2Al4ZiW8VusICp7Utl8ed.jpg",
          description:
            "The second-longest urban beach in the world, stretching 13 km along the Bay of Bengal.",
          coords: { lat: 13.050, lon: 80.282 }
        },
        {
          name: "Covelong Beach",
          location: "Kanchipuram",
          category: "Beach",
          image:
            "https://media.istockphoto.com/id/1150059940/photo/kovalam-beach-sunset.jpg?s=612x612&w=0&k=20&c=H0yfOqRkOXYaFxajF8pcHVJ3ONckf7L5cTLTvabZGRU=",
          description:
            "A serene beach known for water sports, fishing, and the remains of a Dutch fort.",
          coords: { lat: 12.787, lon: 80.248 }
        },
        {
          name: "Elliot's Beach",
          location: "Chennai",
          category: "Beach",
          image:
            "data:image/jpeg;base64,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",
          description:
            "Also known as Besant Nagar Beach, a clean and less crowded alternative to Marina Beach, popular for evening walks and street food.",
          coords: { lat: 13.002, lon: 80.271 }
        },
        {
          name: "Silver Beach",
          location: "Cuddalore",
          category: "Beach",
          image:
            "https://media-cdn.tripadvisor.com/media/photo-s/0a/6c/55/6f/sunrise-at-silver-beach.jpg",
          description:
            "One of the cleanest beaches in India with silver-like sand, offering beautiful views of the Bay of Bengal and the old lighthouse.",
          coords: { lat: 11.711, lon: 79.793 }
        },
        {
          name: "Vivekananda Rock Memorial",
          location: "Kanyakumari",
          category: "Monument",
          image:
            "https://media.istockphoto.com/id/1185863094/photo/vivekananda-rock-memorial-kanyakumari-tamil-nadu-india.jpg?s=612x612&w=0&k=20&c=_rSlagfs-A6o9skRMRAJmx9nzd3Eddmu9avKMm9Ftlo=",
          description:
            "A monument built on a rock island where Swami Vivekananda meditated, offering panoramic views of the ocean.",
          coords: { lat: 8.078, lon: 77.551 }
        },
        {
          name: "Shore Temple",
          location: "Mahabalipuram",
          category: "Monument",
          image:
            "https://media.istockphoto.com/id/471507884/photo/shore-temple-mamallapuram.jpg?s=612x612&w=0&k=20&c=QvX3e5SD4jPlznA6FBPSLm-_9fYbsf6KO94Bai-hKSU=",
          description:
            "A UNESCO World Heritage Site, this 8th-century temple is one of the oldest stone temples in South India, showcasing Pallava architecture.",
          coords: { lat: 12.616, lon: 80.199 }
        },
        {
          name: "Gingee Fort",
          location: "Villupuram",
          category: "Monument",
          image:
            "https://thumbs.dreamstime.com/b/gingee-fort-11006155.jpg",
          description:
            "Called the 'Troy of the East', this 16th-century fort complex spans three hills and features impressive defensive structures and temples.",
          coords: { lat: 12.253, lon: 79.409 }
        },
        {
          name: "Madurai Palace",
          location: "Madurai",
          category: "Palace",
          image:
            "https://thumbs.dreamstime.com/b/wide-view-ancient-thirumalai-nayak-palace-people-sculptures-pillars-madurai-tamil-nadu-india-may-93115432.jpg",
          description:
            "Also known as Thirumalai Nayakkar Palace, this 17th-century palace showcases Dravidian and Islamic architectural styles.",
          coords: { lat: 9.925, lon: 78.119 }
        },
        {
          name: "Chettinad Palace",
          location: "Karaikudi",
          category: "Palace",
          image:
            "https://www.shutterstock.com/image-photo/karaikkudi-india-103106-kanadukathan-palace-600nw-1978887176.jpg",
          description:
            "A magnificent mansion showcasing the opulent lifestyle of the Chettiars, featuring imported materials, intricate woodwork, and colorful tiles.",
          coords: { lat: 10.074, lon: 78.780 }
        }
      ],
      ta: [
        {
          name: "பிரகதீஸ்வரர் கோவில்",
          location: "தஞ்சாவூர்",
          category: "Temple",
          image:
            "https://tse2.mm.bing.net/th?id=OIP.U1BUbQpP1XjOdYLy1rrkmwHaFj&pid=Api&P=0&h=220",
          description:
            "யுனெஸ்கோ உலக பாரம்பரியக் களம் மற்றும் சோழ معمாரியத்தின் சிகரம்.",
          coords: { lat: 10.787, lon: 79.137 }
        },
        {
          name: "மீனாட்சி அம்மன் கோவில்",
          location: "மதுரை",
          category: "Temple",
          image:
            "https://tse1.mm.bing.net/th?id=OIP.3B2tPbPkrxw-phM-4Q6AqgHaE8&pid=Api&P=0&h=220",
          description:
            "மீனாட்சி தேவிக்கு அர்ப்பணிக்கப்பட்ட புகழ்பெற்ற இந்து கோவில்.",
          coords: { lat: 9.925, lon: 78.1198 }
        },
        {
          name: "மாமல்லபுரம்",
          location: "சென்னை",
          category: "Monument",
          image:
            "https://tse3.mm.bing.net/th?id=OIP.fOvloesqytzzB8BpKC0eAAHaE5&pid=Api&P=0&h=220",
          description:
            "பாறை செதுக்கப்பட்ட கோவில்கள் மற்றும் சிற்பங்களுக்குப் புகழ் பெற்றது.",
          coords: { lat: 12.620, lon: 80.192 }
        },
        {
          name: "தஞ்சாவூர் அரண்மனை",
          location: "தஞ்சாவூர்",
          category: "Palace",
          image:
            "https://tse4.mm.bing.net/th?id=OIP.SfjBkUFRZHrPwcb0pciZawHaE8&pid=Api&P=0&h=220",
          description:
            "நாயக்கர் ஆட்சி காலத்தில் கட்டப்பட்ட, சிற்பக்கலை மற்றும் கலைக்களரியால் புகழ்பெற்ற அரண்மனை.",
          coords: { lat: 10.787, lon: 79.137 }
        },
        {
          name: "ராமநாதசுவாமி கோவில்",
          location: "ராமேஸ்வரம்",
          category: "Temple",
          image:
            "https://tse4.mm.bing.net/th?id=OIP.GdA3HPrA9ByDA7kogoKstAHaE8&pid=Api&P=0&h=220",
          description:
            "பெரிய பிரகாரங்கள் மற்றும் திராவிடக் கட்டிடக்கலையை கொண்ட, சிவனுக்கு அர்ப்பணிக்கப்பட்ட கோவில்.",
          coords: { lat: 9.288, lon: 79.312 }
        },
        {
          name: "கங்கைகொண்ட சோழபுரம்",
          location: "ஜெயங்கொண்டம்",
          category: "Temple",
          image:
            "https://tse4.mm.bing.net/th?id=OIP.JmWCuZVHlJQjjR574QohzwHaEK&pid=Api&P=0&h=220",
          description:
            "சோழர்களால் கட்டப்பட்ட சிற்பக் கலை மற்றும் சிற்பங்களை கொண்ட சிறப்பு மிக்க கட்டிடம்.",
          coords: { lat: 10.712, lon: 79.360 }
        },
        {
          name: "ஊட்டி மலை நிலையம்",
          location: "நீலகிரி",
          category: "Hill Station",
          image:
            "https://www.holidify.com/images/cmsuploads/compressed/attr_1856_20190212181955.jpg",
          description:
            "தேயிலைத் தோட்டங்கள், தாவரவியல் பூங்காக்கள் மற்றும் இனிமையான காலநிலைக்கு பெயர் பெற்ற அழகிய மலை நிலையம்.",
          coords: { lat: 11.413, lon: 76.695 }
        },
        {
          name: "கொடைக்கானல்",
          location: "திண்டுக்கல்",
          category: "Hill Station",
          image:
            "https://www.holidify.com/images/cmsuploads/compressed/kodaikanal_20190307132054.jpg",
          description:
            "நட்சத்திர வடிவ ஏரியுடன் கூடிய அழகிய மலை நிலையம், 'மலை நிலையங்களின் இளவரசி' என்று அழைக்கப்படுகிறது.",
          coords: { lat: 10.238, lon: 77.488 }
        },
        {
          name: "அரசு அருங்காட்சியகம் சென்னை",
          location: "சென்னை",
          category: "Museum",
          image:
            "https://www.holidify.com/images/cmsuploads/compressed/Government_Museum_Chennai_20190613100641.jpg",
          description:
            "இந்தியாவின் மிகப் பழமையான அருங்காட்சியகங்களில் ஒன்று, தொல்பொருள் மற்றும் நாணயவியல் பொருட்களின் செழுமையான சேகரிப்புடன்.",
          coords: { lat: 13.072, lon: 80.258 }
        },
        {
          name: "மெரினா கடற்கரை",
          location: "சென்னை",
          category: "Beach",
          image:
            "https://www.holidify.com/images/cmsuploads/compressed/Marina_Beach_20190619141119.jpg",
          description:
            "உலகின் இரண்டாவது நீளமான நகர்ப்புற கடற்கரை, வங்காள விரிகுடா வழியாக 13 கிமீ நீளம் கொண்டது.",
          coords: { lat: 13.050, lon: 80.282 }
        },
        {
          name: "கோவளம் கடற்கரை",
          location: "காஞ்சிபுரம்",
          category: "Beach",
          image:
            "https://www.holidify.com/images/cmsuploads/compressed/Covelong_Beach_20190619141119.jpg",
          description:
            "நீர் விளையாட்டுகள், மீன்பிடித்தல் மற்றும் டச்சு கோட்டையின் எச்சங்களுக்கு பெயர் பெற்ற அமைதியான கடற்கரை.",
          coords: { lat: 12.787, lon: 80.248 }
        },
        {
          name: "விவேகானந்தர் பாறை நினைவுச்சின்னம்",
          location: "கன்னியாகுமரி",
          category: "Monument",
          image:
            "https://www.holidify.com/images/cmsuploads/compressed/Vivekananda_Rock_Memorial_20190619141119.jpg",
          description:
            "சுவாமி விவேகானந்தர் தியானம் செய்த பாறைத் தீவில் கட்டப்பட்ட நினைவுச்சின்னம், கடலின் பனோரமிக் காட்சிகளை வழங்குகிறது.",
          coords: { lat: 8.078, lon: 77.551 }
        },
        {
          name: "ஏலகிரி மலைகள்",
          location: "வேலூர்",
          category: "Hill Station",
          image:
            "https://www.holidify.com/images/cmsuploads/compressed/attr_1856_20190212181955.jpg",
          description:
            "தோட்டங்கள், ரோஜா பூங்காக்கள் மற்றும் செயற்கை ஏரிகளுடன் கூடிய அமைதியான மலை நிலையம், நடைபயணம் மற்றும் பாராகிளைடிங்கிற்கு சிறந்தது.",
          coords: { lat: 12.583, lon: 78.647 }
        },
        {
          name: "கொல்லி மலைகள்",
          location: "நாமக்கல்",
          category: "Hill Station",
          image:
            "https://www.holidify.com/images/cmsuploads/compressed/shutterstock_1084241210_20190430141712.jpg",
          description:
            "70 முடிச்சு வளைவுகள் காரணமாக 'மரண மலை' என்று அழைக்கப்படுகிறது, அகய கங்கை நீர்வீழ்ச்சி மற்றும் மூலிகைகளுக்கு பிரசித்தி பெற்றது.",
          coords: { lat: 11.248, lon: 78.330 }
        },
        {
          name: "தஞ்சாவூர் கலை அருங்காட்சியகம்",
          location: "தஞ்சாவூர்",
          category: "Museum",
          image:
            "https://www.holidify.com/images/cmsuploads/compressed/shutterstock_1084241210_20190430141712.jpg",
          description:
            "சோழர் காலத்தின் வெண்கல மற்றும் கல் சிற்பங்களின் அற்புதமான சேகரிப்பு, ஓவியங்கள் மற்றும் கலைப்பொருட்களுடன்.",
          coords: { lat: 10.785, lon: 79.135 }
        },
        {
          name: "தக்ஷிணசித்ரா அருங்காட்சியகம்",
          location: "சென்னை",
          category: "Museum",
          image:
            "https://www.holidify.com/images/cmsuploads/compressed/dakshina-chitra-museum_20190613100641.jpg",
          description:
            "மறுசீரமைக்கப்பட்ட பாரம்பரிய வீடுகள் மூலம் தென்னிந்தியாவின் கலை, கட்டிடக்கலை, வாழ்க்கை முறைகள் மற்றும் கைவினைப் பொருட்களைக் காட்சிப்படுத்தும் உயிருள்ள அருங்காட்சியகம்.",
          coords: { lat: 12.825, lon: 80.230 }
        },
        {
          name: "எலியட் கடற்கரை",
          location: "சென்னை",
          category: "Beach",
          image:
            "https://www.holidify.com/images/cmsuploads/compressed/elliot-beach_20190619141119.jpg",
          description:
            "பெசன்ட் நகர் கடற்கரை என்றும் அழைக்கப்படுகிறது, மெரினா கடற்கரைக்கு சுத்தமான மற்றும் குறைவான கூட்டமான மாற்று, மாலை நடைப்பயணம் மற்றும் தெரு உணவுகளுக்கு பிரபலமானது.",
          coords: { lat: 13.002, lon: 80.271 }
        },
        {
          name: "வெள்ளி கடற்கரை",
          location: "கடலூர்",
          category: "Beach",
          image:
            "https://www.holidify.com/images/cmsuploads/compressed/silver-beach_20190619141119.jpg",
          description:
            "வெள்ளி போன்ற மணலுடன் இந்தியாவின் மிகவும் சுத்தமான கடற்கரைகளில் ஒன்று, வங்காள விரிகுடா மற்றும் பழைய கலங்கரை விளக்கத்தின் அழகிய காட்சிகளை வழங்குகிறது.",
          coords: { lat: 11.711, lon: 79.793 }
        },
        {
          name: "கடற்கரை கோயில்",
          location: "மாமல்லபுரம்",
          category: "Monument",
          image:
            "https://www.holidify.com/images/cmsuploads/compressed/shore-temple_20190619141119.jpg",
          description:
            "யுனெஸ்கோ உலக பாரம்பரிய தளம், இந்த 8 ஆம் நூற்றாண்டு கோயில் தென்னிந்தியாவின் மிகப் பழமையான கல் கோயில்களில் ஒன்றாகும், பல்லவ கட்டிடக்கலையைக் காட்சிப்படுத்துகிறது.",
          coords: { lat: 12.616, lon: 80.199 }
        },
        {
          name: "செஞ்சி கோட்டை",
          location: "விழுப்புரம்",
          category: "Monument",
          image:
            "https://www.holidify.com/images/cmsuploads/compressed/gingee-fort_20190619141119.jpg",
          description:
            "'கிழக்கின் ட்ராய்' என்று அழைக்கப்படும் இந்த 16 ஆம் நூற்றாண்டு கோட்டை வளாகம் மூன்று மலைகளை உள்ளடக்கியது மற்றும் பாதுகாப்பு கட்டமைப்புகள் மற்றும் கோயில்களைக் கொண்டுள்ளது.",
          coords: { lat: 12.253, lon: 79.409 }
        },
        {
          name: "மதுரை அரண்மனை",
          location: "மதுரை",
          category: "Palace",
          image:
            "https://www.holidify.com/images/cmsuploads/compressed/thirumalai-nayakkar-palace_20190619141119.jpg",
          description:
            "திருமலை நாயக்கர் அரண்மனை என்றும் அழைக்கப்படும் இந்த 17 ஆம் நூற்றாண்டு அரண்மனை திராவிட மற்றும் இஸ்லாமிய கட்டிடக்கலை பாணிகளைக் காட்டுகிறது.",
          coords: { lat: 9.925, lon: 78.119 }
        },
        {
          name: "செட்டிநாடு அரண்மனை",
          location: "காரைக்குடி",
          category: "Palace",
          image:
            "https://www.holidify.com/images/cmsuploads/compressed/chettinad-palace_20190619141119.jpg",
          description:
            "செட்டியார்களின் ஆடம்பரமான வாழ்க்கை முறையைக் காட்டும் அற்புதமான மாளிகை, இறக்குமதி செய்யப்பட்ட பொருட்கள், சிக்கலான மரவேலைப்பாடுகள் மற்றும் வண்ணமயமான ஓடுகளைக் கொண்டது.",
          coords: { lat: 10.074, lon: 78.780 }
        }
      ]
    };

    let currentLanguage = "en";
    const siteList = document.getElementById("siteList");
    const modal = document.getElementById("siteModal");
    const modalOverlay = document.getElementById("modalOverlay");
    const modalTitle = document.getElementById("modalTitle");
    const modalDescription = document.getElementById("modalDescription");
    const playAudioButton = document.getElementById("playAudio");
    const closeModalButton = document.getElementById("closeModalButton");
    const nearbyOverlay = document.getElementById("nearbyOverlay");
    const closeNearbyModalButton = document.getElementById("closeNearbyModalButton");
    const favoritesOverlay = document.getElementById("favoritesOverlay");
    const closeFavoritesModalButton = document.getElementById("closeFavoritesModalButton");
    const reviewsOverlay = document.getElementById("reviewsOverlay");
    const closeReviewsModalButton = document.getElementById("closeReviewsModalButton");

    /***** Enhanced Chatbot Functionality: Conversation History *****/
    let conversationHistory = [];

    /***** Render Sites and Populate Location Filter *****/
    function renderSites(language, sites = null) {
      siteList.innerHTML = "";
      const sitesToRender = sites || heritageSites[language];
      sitesToRender.forEach((site) => {
        const distanceInfo = site.distance
          ? `<p>Distance: ${site.distance.toFixed(2)} km</p>`
          : "";
        const card = document.createElement("article");
        card.classList.add("site-card");
        card.innerHTML = `
          <img src="${site.image}" alt="${site.name} image" loading="lazy">
          <div class="site-card-content">
            <h3>${site.name}</h3>
            <p class="site-category">${site.category || ""}</p>
            <p>${site.description}</p>
            ${distanceInfo}
            <button class="viewButton" data-site="${site.name}">View</button>
            <button class="nearbyButton" data-location="${site.location}" data-site="${site.name}">Nearby Places</button>
            <button class="favoriteButton" data-site="${site.name}">Favorite</button>
            <button class="shareButton" data-site="${site.name}">Share</button>
            <button class="reviewButton" data-site="${site.name}">Review</button>
          </div>
        `;
        siteList.appendChild(card);
      });
      populateLocationFilter();
    }

    /***** Search Sites *****/
    document.getElementById("searchBar").addEventListener("input", searchSites);
    function searchSites() {
      const searchInput = document.getElementById("searchBar").value.toLowerCase();
      const filteredSites = heritageSites[currentLanguage].filter(
        (site) =>
          site.name.toLowerCase().includes(searchInput) ||
          site.location.toLowerCase().includes(searchInput) ||
          site.description.toLowerCase().includes(searchInput) ||
          (site.category && site.category.toLowerCase().includes(searchInput))
      );
      renderSites(currentLanguage, filteredSites);
    }

    /***** Haversine Formula for Distance Calculation *****/
    function getDistance(lat1, lon1, lat2, lon2) {
      const R = 6371; // Earth's radius in km
      const dLat = ((lat2 - lat1) * Math.PI) / 180;
      const dLon = ((lon2 - lon1) * Math.PI) / 180;
      const a =
        Math.sin(dLat / 2) ** 2 +
        Math.cos(lat1 * Math.PI / 180) *
          Math.cos(lat2 * Math.PI / 180) *
          Math.sin(dLon / 2) ** 2;
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      return R * c;
    }

    /***** Current Location to Find Nearby Sites Feature *****/
    function searchNearbySites(userCoords, threshold = 50) {
      const filteredSites = heritageSites[currentLanguage].filter((site) => {
        if (site.coords) {
          const distance = getDistance(
            userCoords.lat,
            userCoords.lon,
            site.coords.lat,
            site.coords.lon
          );
          site.distance = distance;
          return distance <= threshold;
        }
        return false;
      });
      if (filteredSites.length === 0) {
        siteList.innerHTML = `<p style="text-align: center; padding: 1rem;">No nearby heritage sites found.</p>`;
      } else {
        filteredSites.sort((a, b) => a.distance - b.distance);
        renderSites(currentLanguage, filteredSites);
      }
    }

    /***** Speech Recognition for Search *****/
    const micButton = document.getElementById("micButton");
    const searchBar = document.getElementById("searchBar");
    window.SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognition();
    recognition.lang = currentLanguage === "en" ? "en-US" : "ta-IN";
    recognition.interimResults = false;
    recognition.continuous = false;

    micButton.addEventListener("click", () => {
      recognition.start();
    });
    recognition.addEventListener("result", (event) => {
      const speechToText = event.results[0][0].transcript;
      searchBar.value = speechToText;
      searchSites();
    });
    recognition.addEventListener("error", (event) => {
      console.error("Speech recognition error:", event.error);
    });

    /***** Event listener for the "Find Nearby Sites" button *****/
    document.getElementById("currentLocationButton").addEventListener("click", () => {
      const spinner = document.getElementById("locationSpinner");
      spinner.style.display = "inline-block";
      trackEvent("use_current_location", {});
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            spinner.style.display = "none";
            const userCoords = {
              lat: position.coords.latitude,
              lon: position.coords.longitude
            };
            searchNearbySites(userCoords);
          },
          (error) => {
            spinner.style.display = "none";
            console.error("Geolocation error:", error);
            alert("Unable to retrieve your location. Please ensure location services are enabled.");
          }
        );
      } else {
        spinner.style.display = "none";
        alert("Geolocation is not supported by your browser.");
      }
    });

    /***** Populate Filter Dropdowns *****/
    function populateLocationFilter() {
      // Location filter
      const locationFilter = document.getElementById("locationFilter");
      locationFilter.innerHTML = '<option value="">All Locations</option>';
      const locations = new Set();
      heritageSites[currentLanguage].forEach((site) => {
        if (site.location) {
          locations.add(site.location);
        }
      });
      locations.forEach((loc) => {
        const option = document.createElement("option");
        option.value = loc;
        option.textContent = loc;
        locationFilter.appendChild(option);
      });
    }

    function populateCategoryFilter() {
      // Category filter
      const categoryFilter = document.getElementById("categoryFilter");
      categoryFilter.innerHTML = '<option value="">All Categories</option>';
      const categories = new Set();
      heritageSites[currentLanguage].forEach((site) => {
        if (site.category) {
          categories.add(site.category);
        }
      });
      categories.forEach((cat) => {
        const option = document.createElement("option");
        option.value = cat;
        option.textContent = cat;
        categoryFilter.appendChild(option);
      });
    }

    // Filter by dropdown selections
    document.getElementById("locationFilter").addEventListener("change", function () {
      const selectedLocation = this.value;
      const selectedCategory = document.getElementById("categoryFilter").value;

      let filteredSites = heritageSites[currentLanguage];

      // Filter by location if selected
      if (selectedLocation !== "") {
        filteredSites = filteredSites.filter(site => site.location === selectedLocation);
      }

      // Filter by category if selected
      if (selectedCategory !== "") {
        filteredSites = filteredSites.filter(site => site.category === selectedCategory);
      }

      renderSites(currentLanguage, filteredSites);

      // Update active category button
      updateActiveCategoryButton(selectedCategory);
    });

    document.getElementById("categoryFilter").addEventListener("change", function () {
      const selectedCategory = this.value;
      const selectedLocation = document.getElementById("locationFilter").value;

      let filteredSites = heritageSites[currentLanguage];

      // Filter by category if selected
      if (selectedCategory !== "") {
        filteredSites = filteredSites.filter(site => site.category === selectedCategory);
      }

      // Filter by location if selected
      if (selectedLocation !== "") {
        filteredSites = filteredSites.filter(site => site.location === selectedLocation);
      }

      renderSites(currentLanguage, filteredSites);

      // Update active category button
      updateActiveCategoryButton(selectedCategory);
    });

    // Category filter buttons
    const categoryFilterButtons = document.querySelectorAll('.category-filter-btn');
    categoryFilterButtons.forEach(button => {
      button.addEventListener('click', function() {
        const category = this.getAttribute('data-category');

        // Update active button
        categoryFilterButtons.forEach(btn => btn.classList.remove('active'));
        this.classList.add('active');

        // Update dropdown to match
        const categoryDropdown = document.getElementById('categoryFilter');
        categoryDropdown.value = category;

        // Filter sites
        let filteredSites = heritageSites[currentLanguage];
        const selectedLocation = document.getElementById("locationFilter").value;

        // Filter by category if selected
        if (category !== "") {
          filteredSites = filteredSites.filter(site => site.category === category);
        }

        // Filter by location if selected
        if (selectedLocation !== "") {
          filteredSites = filteredSites.filter(site => site.location === selectedLocation);
        }

        renderSites(currentLanguage, filteredSites);
      });
    });

    // Function to update active category button based on dropdown selection
    function updateActiveCategoryButton(category) {
      const categoryButtons = document.querySelectorAll('.category-filter-btn');
      categoryButtons.forEach(button => {
        const buttonCategory = button.getAttribute('data-category');
        if (buttonCategory === category) {
          button.classList.add('active');
        } else {
          button.classList.remove('active');
        }
      });
    }

    /***** Modal Toggle and Close Functions *****/
    function modalToggle(modalId, state) {
      const modals = {
        siteModal: [document.getElementById("siteModal"), document.getElementById("modalOverlay")],
        nearbyModal: [document.getElementById("nearbyModal"), document.getElementById("nearbyOverlay")],
        favoritesModal: [document.getElementById("favoritesModal"), document.getElementById("favoritesOverlay")],
        reviewsModal: [document.getElementById("reviewsModal"), document.getElementById("reviewsOverlay")]
      };
      const [modalElement, overlayElement] = modals[modalId];
      modalElement.style.display = state ? "block" : "none";
      overlayElement.style.display = state ? "block" : "none";
    }
    document.getElementById("modalOverlay").addEventListener("click", () => modalToggle("siteModal", false));
    closeModalButton.addEventListener("click", () => modalToggle("siteModal", false));
    document.getElementById("nearbyOverlay").addEventListener("click", () => modalToggle("nearbyModal", false));
    document.getElementById("closeNearbyModalButton").addEventListener("click", () => modalToggle("nearbyModal", false));
    document.getElementById("favoritesOverlay").addEventListener("click", () => modalToggle("favoritesModal", false));
    closeFavoritesModalButton.addEventListener("click", () => modalToggle("favoritesModal", false));
    document.getElementById("reviewsOverlay").addEventListener("click", () => modalToggle("reviewsModal", false));
    closeReviewsModalButton.addEventListener("click", () => modalToggle("reviewsModal", false));

    /***** Audio Playback for Site Description *****/
    playAudioButton.addEventListener("click", () => {
      const utterance = new SpeechSynthesisUtterance(modalDescription.textContent);
      utterance.lang = currentLanguage === "en" ? "en-US" : "ta-IN";
      speechSynthesis.speak(utterance);
    });

    /***** Language Toggle *****/
    document.getElementById("toggleLang").addEventListener("click", () => {
      currentLanguage = currentLanguage === "en" ? "ta" : "en";
      trackEvent("toggle_language", { language: currentLanguage });
      document.getElementById("mainHeading").textContent =
        currentLanguage === "en" ? "Heritage Explorer" : "பாரம்பரிய கையேடு";
      document.getElementById("subHeading").textContent =
        currentLanguage === "en"
          ? "Discover Tamil Nadu's rich heritage and culture"
          : "தமிழகத்தின் பாரம்பரியத்தை மற்றும் பண்பாட்டை ஆராயுங்கள்";
      document.getElementById("footerText").textContent =
        currentLanguage === "en"
          ? "© 2025 Heritage Explorer | Explore Tamil Nadu"
          : "© 2025 பாரம்பரிய கையேடு | தமிழகத்தை ஆராயுங்கள்";
      document.getElementById("toggleLang").textContent =
        currentLanguage === "en" ? "தமிழ்" : "English";
      renderSites(currentLanguage);
      recognition.lang = currentLanguage === "en" ? "en-US" : "ta-IN";
    });

    /***** Favorites Feature *****/
    function toggleFavorite(siteName) {
      let favorites = JSON.parse(localStorage.getItem("favoriteSites")) || [];
      if (favorites.includes(siteName)) {
        favorites = favorites.filter((name) => name !== siteName);
        alert(`${siteName} removed from favorites`);
      } else {
        favorites.push(siteName);
        alert(`${siteName} added to favorites`);
      }
      localStorage.setItem("favoriteSites", JSON.stringify(favorites));
    }
    function showFavoritesModal() {
      const favoritesListDiv = document.getElementById("favoritesList");
      favoritesListDiv.innerHTML = "";
      let favorites = JSON.parse(localStorage.getItem("favoriteSites")) || [];
      if (favorites.length === 0) {
        favoritesListDiv.innerHTML = "<p>No favorites added yet.</p>";
      } else {
        favorites.forEach((siteName) => {
          const p = document.createElement("p");
          p.classList.add("favoritesItem");
          p.textContent = siteName;
          const viewBtn = document.createElement("button");
          viewBtn.textContent = "View";
          viewBtn.addEventListener("click", () => {
            navigateToDetails(siteName);
            modalToggle("favoritesModal", false);
          });
          p.appendChild(viewBtn);
          favoritesListDiv.appendChild(p);
        });
      }
      modalToggle("favoritesModal", true);
    }
    document.getElementById("favoritesButton").addEventListener("click", showFavoritesModal);

    /***** Reviews Feature *****/
    function openReviewsModal(siteName) {
      document.getElementById("reviewsSiteName").textContent = siteName;
      loadReviews(siteName);
      modalToggle("reviewsModal", true);
      window.currentReviewSite = siteName;
    }
    function loadReviews(siteName) {
      const reviewsListDiv = document.getElementById("reviewsList");
      reviewsListDiv.innerHTML = "";
      let reviews = JSON.parse(localStorage.getItem("reviews")) || {};
      let siteReviews = reviews[siteName] || [];
      if (siteReviews.length === 0) {
        reviewsListDiv.innerHTML =
          "<p>No reviews yet. Be the first to review this site!</p>";
      } else {
        siteReviews.forEach((review) => {
          const p = document.createElement("p");
          p.classList.add("reviewsItem");
          p.textContent = review;
          reviewsListDiv.appendChild(p);
        });
      }
    }
    function submitReview() {
      const reviewText = document.getElementById("newReview").value.trim();
      if (!reviewText) {
        alert("Please write a review before submitting.");
        return;
      }
      let reviews = JSON.parse(localStorage.getItem("reviews")) || {};
      let site = window.currentReviewSite;
      if (!reviews[site]) {
        reviews[site] = [];
      }
      reviews[site].push(reviewText);
      localStorage.setItem("reviews", JSON.stringify(reviews));
      document.getElementById("newReview").value = "";
      loadReviews(site);
      alert("Review submitted!");
    }
    document.getElementById("submitReviewButton").addEventListener("click", submitReview);

    /***** Chatbot Setup *****/
    const chatbotToggle = document.getElementById("chatbotToggle");
    const chatbotContainer = document.getElementById("chatbotContainer");
    const chatbotMessages = document.getElementById("chatbotMessages");
    const chatbotInput = document.getElementById("chatbotInput");
    const chatbotSendButton = document.getElementById("chatbotSendButton");
    const chatbotMicButton = document.getElementById("chatbotMicButton");
    const chatbotVoiceToggle = document.getElementById("chatbotVoiceToggle");
    let isChatbotVoiceEnabled = false;

    chatbotToggle.addEventListener("click", () => {
      chatbotContainer.style.display = chatbotContainer.style.display === "flex" ? "none" : "flex";
      trackEvent("toggle_chatbot", { state: chatbotContainer.style.display });
    });

    function sendMessage() {
      const userMessage = chatbotInput.value.trim();
      if (!userMessage) return;
      trackEvent("chatbot_user_message", { message: userMessage });
      const userMessageElement = document.createElement("div");
      userMessageElement.classList.add("user");
      userMessageElement.textContent = userMessage;
      chatbotMessages.appendChild(userMessageElement);
      chatbotInput.value = "";
      chatbotMessages.scrollTop = chatbotMessages.scrollHeight;

      // Add typing indicator
      const typingIndicator = document.createElement("div");
      typingIndicator.classList.add("bot", "typing-indicator");
      typingIndicator.innerHTML = "<span></span><span></span><span></span>";
      chatbotMessages.appendChild(typingIndicator);
      chatbotMessages.scrollTop = chatbotMessages.scrollHeight;

      setTimeout(() => {
        // Remove typing indicator
        chatbotMessages.removeChild(typingIndicator);

        const botResponse = getChatbotResponse(userMessage);
        const botMessageElement = document.createElement("div");
        botMessageElement.classList.add("bot");
        botMessageElement.textContent = botResponse;
        chatbotMessages.appendChild(botMessageElement);
        chatbotMessages.scrollTop = chatbotMessages.scrollHeight;
        trackEvent("chatbot_bot_response", { message: botResponse });

        // Speak the response if voice is enabled
        if (isChatbotVoiceEnabled) {
          speakText(botResponse);
        }
      }, 1000);
    }

    // Speech recognition for chatbot
    const chatbotRecognition = new SpeechRecognition();
    chatbotRecognition.lang = currentLanguage === "en" ? "en-US" : "ta-IN";
    chatbotRecognition.interimResults = false;
    chatbotRecognition.continuous = false;

    // Add event listener for the chatbot mic button
    chatbotMicButton.addEventListener("click", () => {
      // Add visual feedback that the mic is active
      chatbotMicButton.classList.add("listening");
      chatbotMicButton.innerHTML = '<i class="fas fa-microphone-alt"></i>';

      // Start recognition
      chatbotRecognition.start();
      trackEvent("chatbot_voice_input_start", {});
    });

    chatbotRecognition.addEventListener("result", (event) => {
      const speechToText = event.results[0][0].transcript;
      chatbotInput.value = speechToText;

      // Remove visual feedback
      chatbotMicButton.classList.remove("listening");
      chatbotMicButton.innerHTML = '<i class="fas fa-microphone"></i>';

      // Send the message
      sendMessage();
      trackEvent("chatbot_voice_input_success", { text: speechToText });
    });

    chatbotRecognition.addEventListener("error", (event) => {
      console.error("Speech recognition error:", event.error);

      // Remove visual feedback
      chatbotMicButton.classList.remove("listening");
      chatbotMicButton.innerHTML = '<i class="fas fa-microphone"></i>';

      trackEvent("chatbot_voice_input_error", { error: event.error });
    });

    chatbotRecognition.addEventListener("end", () => {
      // Remove visual feedback if it's still there
      chatbotMicButton.classList.remove("listening");
      chatbotMicButton.innerHTML = '<i class="fas fa-microphone"></i>';
    });

    // Toggle voice output
    chatbotVoiceToggle.addEventListener("click", () => {
      isChatbotVoiceEnabled = !isChatbotVoiceEnabled;
      chatbotVoiceToggle.classList.toggle("active", isChatbotVoiceEnabled);
      chatbotVoiceToggle.innerHTML = isChatbotVoiceEnabled ?
        '<i class="fas fa-volume-up"></i>' :
        '<i class="fas fa-volume-mute"></i>';

      trackEvent("chatbot_voice_output_toggle", { enabled: isChatbotVoiceEnabled });

      // Provide feedback
      const feedbackMessage = document.createElement("div");
      feedbackMessage.classList.add("bot", "system-message");
      feedbackMessage.textContent = isChatbotVoiceEnabled ?
        "Voice output enabled. I will now speak my responses." :
        "Voice output disabled. I will no longer speak my responses.";
      chatbotMessages.appendChild(feedbackMessage);
      chatbotMessages.scrollTop = chatbotMessages.scrollHeight;
    });

    // Function to speak text using speech synthesis
    function speakText(text) {
      if (!text) return;

      // Cancel any ongoing speech
      window.speechSynthesis.cancel();

      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = currentLanguage === "en" ? "en-US" : "ta-IN";
      utterance.rate = 1.0;
      utterance.pitch = 1.0;

      window.speechSynthesis.speak(utterance);
    }

    function getChatbotResponse(userMessage) {
      conversationHistory.push({ role: "user", message: userMessage });

      // Use the new implementation from heritage-chatbot.js with fallback
      let botResponse;

      try {
        if (typeof window.getHeritageChatbotResponse === 'function') {
          console.log("Using enhanced chatbot response function");
          botResponse = window.getHeritageChatbotResponse(userMessage, currentLanguage);
        } else {
          console.log("Enhanced chatbot function not found, using fallback");
          botResponse = "I'm sorry, the enhanced chatbot is not available right now. Please try again later.";
        }
      } catch (error) {
        console.error("Error in chatbot response:", error);
        botResponse = "I'm sorry, there was an error processing your request. Please try again.";
      }

      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }
    chatbotSendButton.addEventListener("click", sendMessage);
    chatbotInput.addEventListener("keydown", (e) => {
      if (e.key === "Enter") sendMessage();
    });

    /***** Delegate Click Events for Dynamic Site Card Buttons *****/
    siteList.addEventListener("click", (e) => {
      if (e.target.classList.contains("viewButton")) {
        const siteName = e.target.getAttribute("data-site");
        trackEvent("click_view_button", { site: siteName });
        navigateToDetails(siteName);
      } else if (e.target.classList.contains("nearbyButton")) {
        const location = e.target.getAttribute("data-location");
        const siteName = e.target.getAttribute("data-site");
        trackEvent("click_nearby_button", { site: siteName, location: location });
        showNearbyPlaces(location, siteName);
      } else if (e.target.classList.contains("favoriteButton")) {
        const siteName = e.target.getAttribute("data-site");
        trackEvent("click_favorite_button", { site: siteName });
        toggleFavorite(siteName);
      } else if (e.target.classList.contains("shareButton")) {
        const siteName = e.target.getAttribute("data-site");
        trackEvent("click_share_button", { site: siteName });
        shareSite(siteName);
      } else if (e.target.classList.contains("reviewButton")) {
        const siteName = e.target.getAttribute("data-site");
        trackEvent("click_review_button", { site: siteName });
        openReviewsModal(siteName);
      }
    });

    /***** Show Nearby Places Function *****/
    function showNearbyPlaces(location, siteName) {
      const nearbySites = heritageSites[currentLanguage].filter(
        (site) => site.location === location && site.name !== siteName
      );
      const nearbyListDiv = document.getElementById("nearbyList");
      nearbyListDiv.innerHTML = "";

      if (nearbySites.length === 0) {
        nearbyListDiv.innerHTML = "<p>No nearby places found.</p>";
      } else {
        nearbySites.forEach((site) => {
          const p = document.createElement("p");
          p.textContent = site.name;
          nearbyListDiv.appendChild(p);
        });
      }
      modalToggle("nearbyModal", true);
    }

    /***** GPS-based Nearby Sites Function *****/
    // Initialize map variables
    let map;
    let userMarker;
    let siteMarkers = [];
    let infoWindow;
    let currentView = 'list'; // 'list' or 'map'

    // Initialize the map
    function initMap() {
      // This function will be called by the Google Maps API
      // It's empty here because we'll initialize the map when needed
    }

    // Find nearby sites using GPS
    document.getElementById("findNearbyButton").addEventListener("click", function () {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(showNearbySitesByGPS, handleLocationError);
      } else {
        alert("Geolocation is not supported by this browser.");
      }
    });

    function showNearbySitesByGPS(position) {
      const userLat = position.coords.latitude;
      const userLon = position.coords.longitude;
      const nearbyList = document.getElementById("nearbyList");
      nearbyList.innerHTML = "";

      // Default radius in kilometers
      const radius = 50;

      // Filter sites within the radius
      const nearbySites = heritageSites[currentLanguage].filter(site => {
        const distance = getDistance(
          userLat,
          userLon,
          site.coords.lat,
          site.coords.lon
        );
        site.distance = distance;
        return distance <= radius;
      });

      // Sort by distance
      nearbySites.sort((a, b) => a.distance - b.distance);

      if (nearbySites.length === 0) {
        nearbyList.innerHTML = "<p>No heritage sites found within 50km of your location.</p>";
      } else {
        nearbySites.forEach(site => {
          const distance = site.distance.toFixed(1);

          const siteCard = document.createElement("div");
          siteCard.className = "site-card";

          // Translate the "View on Map" button text
          const viewOnMapText = currentLanguage === "en" ? "View on Map" : "வரைபடத்தில் காட்டு";
          const distanceText = currentLanguage === "en" ? "km away" : "கி.மீ தொலைவில்";

          siteCard.innerHTML = `
            <img src="${site.image}" alt="${site.name}">
            <div class="site-info">
              <h3>${site.name}</h3>
              <p>${site.location} (${distance} ${distanceText})</p>
              <p>${site.category}</p>
              <button class="view-on-map-btn" data-lat="${site.coords.lat}" data-lon="${site.coords.lon}" data-name="${site.name}">${viewOnMapText}</button>
            </div>
          `;

          // Add click event to navigate to the details page
          siteCard.querySelector('.site-info h3').addEventListener("click", function () {
            let detailsPage;

            // If we're in Tamil mode, first convert the Tamil site name to English
            if (currentLanguage === "ta" && tamilToEnglishNameMapping[site.name]) {
              const englishName = tamilToEnglishNameMapping[site.name];
              detailsPage = detailsPageMapping[englishName];
            } else {
              // Otherwise use the name directly
              detailsPage = detailsPageMapping[site.name];
            }

            if (detailsPage) {
              window.location.href = detailsPage;
            }
          });

          // Add click event for "View on Map" button
          siteCard.querySelector('.view-on-map-btn').addEventListener("click", function(e) {
            e.stopPropagation();
            const lat = parseFloat(this.getAttribute('data-lat'));
            const lon = parseFloat(this.getAttribute('data-lon'));
            const name = this.getAttribute('data-name');

            // Switch to map view if not already
            if (currentView !== 'map') {
              toggleView();
            }

            // Center map on the selected site
            map.setCenter({ lat: lat, lng: lon });
            map.setZoom(14);

            // Open info window for this site
            for (let marker of siteMarkers) {
              if (marker.title === name) {
                infoWindow.setContent(`<div><strong>${name}</strong></div>`);
                infoWindow.open(map, marker);
                break;
              }
            }
          });

          nearbyList.appendChild(siteCard);
        });
      }

      // Initialize the map
      initializeMap(userLat, userLon, nearbySites);

      // Show the modal
      modalToggle("nearbyModal", true);
    }

    function initializeMap(userLat, userLon, nearbySites) {
      // Create the map centered on user's location
      map = new google.maps.Map(document.getElementById("mapContainer"), {
        center: { lat: userLat, lng: userLon },
        zoom: 10,
        mapTypeControl: false,
        fullscreenControl: false
      });

      // Create info window for markers
      infoWindow = new google.maps.InfoWindow();

      // Add user marker
      userMarker = new google.maps.Marker({
        position: { lat: userLat, lng: userLon },
        map: map,
        title: "Your Location",
        icon: {
          path: google.maps.SymbolPath.CIRCLE,
          scale: 10,
          fillColor: "#4285F4",
          fillOpacity: 1,
          strokeColor: "#FFFFFF",
          strokeWeight: 2
        }
      });

      // Add markers for nearby sites
      siteMarkers = [];
      for (let site of nearbySites) {
        const marker = new google.maps.Marker({
          position: { lat: site.coords.lat, lng: site.coords.lon },
          map: map,
          title: site.name,
          animation: google.maps.Animation.DROP
        });

        // Add click event to marker
        marker.addListener("click", () => {
          // Get the correct details page URL
          let detailsPageUrl;
          if (currentLanguage === "ta" && tamilToEnglishNameMapping[site.name]) {
            const englishName = tamilToEnglishNameMapping[site.name];
            detailsPageUrl = detailsPageMapping[englishName];
          } else {
            detailsPageUrl = detailsPageMapping[site.name];
          }

          // Get translated "View Details" text
          const viewDetailsText = currentLanguage === "en" ? "View Details" : "விவரங்களைக் காண";

          infoWindow.setContent(`
            <div style="max-width: 200px;">
              <h3 style="margin: 0 0 5px 0;">${site.name}</h3>
              <p style="margin: 0 0 5px 0;">${site.category}</p>
              <p style="margin: 0 0 5px 0;">${site.location}</p>
              <a href="${detailsPageUrl}" style="color: #E63946;">${viewDetailsText}</a>
            </div>
          `);
          infoWindow.open(map, marker);
        });

        siteMarkers.push(marker);
      }

      // Initially hide the map
      document.getElementById("mapContainer").style.display = "none";
    }

    // Toggle between list and map view
    document.getElementById("toggleViewButton").addEventListener("click", toggleView);

    function toggleView() {
      // Get translated button text
      const showListText = currentLanguage === "en" ? "Show List" : "பட்டியலைக் காட்டு";
      const showMapText = currentLanguage === "en" ? "Show Map" : "வரைபடத்தைக் காட்டு";

      if (currentView === 'list') {
        // Switch to map view
        document.getElementById("nearbyList").style.display = "none";
        document.getElementById("mapContainer").style.display = "block";
        document.getElementById("toggleViewButton").textContent = showListText;
        currentView = 'map';

        // Trigger resize to ensure map displays correctly
        google.maps.event.trigger(map, 'resize');
      } else {
        // Switch to list view
        document.getElementById("nearbyList").style.display = "block";
        document.getElementById("mapContainer").style.display = "none";
        document.getElementById("toggleViewButton").textContent = showMapText;
        currentView = 'list';
      }
    }

    function handleLocationError(error) {
      let errorMessage;
      switch (error.code) {
        case error.PERMISSION_DENIED:
          errorMessage = "User denied the request for Geolocation.";
          break;
        case error.POSITION_UNAVAILABLE:
          errorMessage = "Location information is unavailable.";
          break;
        case error.TIMEOUT:
          errorMessage = "The request to get user location timed out.";
          break;
        case error.UNKNOWN_ERROR:
          errorMessage = "An unknown error occurred.";
          break;
      }
      alert(`Error: ${errorMessage}`);
    }

    // Calculate distance between two points using Haversine formula
    function getDistance(lat1, lon1, lat2, lon2) {
      const R = 6371; // Radius of the Earth in km
      const dLat = (lat2 - lat1) * (Math.PI / 180);
      const dLon = (lon2 - lon1) * (Math.PI / 180);
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(lat1 * (Math.PI / 180)) *
        Math.cos(lat2 * (Math.PI / 180)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      const distance = R * c; // Distance in km
      return distance;
    }

    /***** Share Feature *****/
    function shareSite(siteName) {
      const site = heritageSites[currentLanguage].find((s) => s.name === siteName);
      if (!site) return;
      const shareData = {
        title: site.name,
        text: site.description,
        url: window.location.href + "?site=" + encodeURIComponent(site.name)
      };
      if (navigator.share) {
        navigator
          .share(shareData)
          .then(() => console.log("Site shared successfully"))
          .catch((error) => console.error("Error sharing:", error));
      } else {
        navigator.clipboard
          .writeText(shareData.url)
          .then(() => alert("URL copied to clipboard"))
          .catch((err) => console.error("Could not copy text: ", err));
      }
    }

    /***** Back-to-Top Button Functionality *****/
    const backToTop = document.getElementById("backToTop");
    window.addEventListener("scroll", () => {
      if (window.scrollY > 300) {
        backToTop.style.display = "flex";
      } else {
        backToTop.style.display = "none";
      }
    });
    backToTop.addEventListener("click", () => {
      trackEvent("back_to_top_clicked", {});
      window.scrollTo({ top: 0, behavior: "smooth" });
    });

    /***** Category Buttons Functionality *****/
    function setupCategoryButtons() {
      const categoryButtons = document.querySelectorAll('.category-btn');

      categoryButtons.forEach(button => {
        button.addEventListener('click', function() {
          // Remove active class from all buttons
          categoryButtons.forEach(btn => btn.classList.remove('active'));

          // Add active class to clicked button
          this.classList.add('active');

          // Get the category value
          const category = this.getAttribute('data-category');

          // Filter sites by category
          let filteredSites = heritageSites[currentLanguage];
          if (category) {
            filteredSites = filteredSites.filter(site => site.category === category);
          }

          // Update the category filter dropdown to match
          document.getElementById('categoryFilter').value = category;

          // Render the filtered sites
          renderSites(currentLanguage, filteredSites);

          // Track the event
          trackEvent("filter_by_category_button", { category: category || "all" });
        });
      });
    }

    /***** Initialize Application *****/
    document.addEventListener("DOMContentLoaded", () => {
      populateLocationFilter();
      populateCategoryFilter();
      renderSites(currentLanguage);
      setupCategoryButtons();

      // Initialize category filter buttons
      const categoryFilterButtons = document.querySelectorAll('.category-filter-btn');
      categoryFilterButtons.forEach(button => {
        button.addEventListener('click', function() {
          const category = this.getAttribute('data-category');

          // Update active button
          categoryFilterButtons.forEach(btn => btn.classList.remove('active'));
          this.classList.add('active');

          // Update dropdown to match
          const categoryDropdown = document.getElementById('categoryFilter');
          categoryDropdown.value = category;

          // Filter sites
          let filteredSites = heritageSites[currentLanguage];
          const selectedLocation = document.getElementById("locationFilter").value;

          // Filter by category if selected
          if (category !== "") {
            filteredSites = filteredSites.filter(site => site.category === category);
          }

          // Filter by location if selected
          if (selectedLocation !== "") {
            filteredSites = filteredSites.filter(site => site.location === selectedLocation);
          }

          renderSites(currentLanguage, filteredSites);

          // Track the event
          trackEvent("filter_by_category_button", { category: category || "all" });
        });
      });
    });
  </script>

  <!-- Authentication Script -->
  <script src="auth.js"></script>
</body>
</html>
