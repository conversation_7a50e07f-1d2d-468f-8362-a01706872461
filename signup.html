<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sign Up - Heritage Explorer</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    :root {
      --primary-color: #e63946;
      --secondary-color: #457b9d;
      --accent-color: #1d3557;
      --light-color: #f1faee;
      --dark-color: #1d3557;
      --success-color: #2a9d8f;
      --warning-color: #e9c46a;
      --danger-color: #e76f51;
      --gray-100: #f8f9fa;
      --gray-200: #e9ecef;
      --gray-300: #dee2e6;
      --gray-400: #ced4da;
      --gray-500: #adb5bd;
      --gray-600: #6c757d;
      --gray-700: #495057;
      --gray-800: #343a40;
      --gray-900: #212529;
      --border-radius: 8px;
      --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      --transition: all 0.3s ease;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Poppins', sans-serif;
      background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 1rem;
    }

    .signup-container {
      background: white;
      border-radius: var(--border-radius);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      overflow: hidden;
      width: 100%;
      max-width: 500px;
      animation: slideUp 0.6s ease-out;
      max-height: 90vh;
      overflow-y: auto;
    }

    @keyframes slideUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .signup-header {
      background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
      color: white;
      padding: 2rem;
      text-align: center;
    }

    .signup-header h1 {
      font-size: 1.8rem;
      margin-bottom: 0.5rem;
      font-weight: 600;
    }

    .signup-header p {
      font-size: 0.9rem;
      opacity: 0.9;
    }

    .signup-form {
      padding: 2rem;
    }

    .form-row {
      display: flex;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .form-group {
      margin-bottom: 1.5rem;
      position: relative;
      flex: 1;
    }

    .form-group label {
      display: block;
      margin-bottom: 0.5rem;
      color: var(--gray-700);
      font-weight: 500;
      font-size: 0.9rem;
    }

    .form-group input,
    .form-group select {
      width: 100%;
      padding: 0.8rem 1rem;
      border: 2px solid var(--gray-300);
      border-radius: var(--border-radius);
      font-size: 1rem;
      transition: var(--transition);
      background-color: var(--gray-100);
    }

    .form-group input:focus,
    .form-group select:focus {
      outline: none;
      border-color: var(--secondary-color);
      background-color: white;
      box-shadow: 0 0 0 3px rgba(69, 123, 157, 0.1);
    }

    .form-group input.error {
      border-color: var(--danger-color);
    }

    .form-group input.success {
      border-color: var(--success-color);
    }

    .form-group .validation-message {
      font-size: 0.8rem;
      margin-top: 0.3rem;
      display: none;
    }

    .form-group .validation-message.error {
      color: var(--danger-color);
      display: block;
    }

    .form-group .validation-message.success {
      color: var(--success-color);
      display: block;
    }

    .password-toggle {
      position: absolute;
      right: 1rem;
      top: 2.2rem;
      cursor: pointer;
      color: var(--gray-500);
      transition: var(--transition);
    }

    .password-toggle:hover {
      color: var(--secondary-color);
    }

    .password-strength {
      margin-top: 0.5rem;
    }

    .strength-bar {
      height: 4px;
      background-color: var(--gray-300);
      border-radius: 2px;
      overflow: hidden;
      margin-bottom: 0.3rem;
    }

    .strength-fill {
      height: 100%;
      width: 0%;
      transition: var(--transition);
      border-radius: 2px;
    }

    .strength-fill.weak {
      background-color: var(--danger-color);
      width: 25%;
    }

    .strength-fill.fair {
      background-color: var(--warning-color);
      width: 50%;
    }

    .strength-fill.good {
      background-color: var(--secondary-color);
      width: 75%;
    }

    .strength-fill.strong {
      background-color: var(--success-color);
      width: 100%;
    }

    .strength-text {
      font-size: 0.8rem;
      color: var(--gray-600);
    }

    .terms-checkbox {
      display: flex;
      align-items: flex-start;
      gap: 0.5rem;
      margin-bottom: 1.5rem;
      font-size: 0.9rem;
    }

    .terms-checkbox input[type="checkbox"] {
      width: auto;
      margin: 0;
      margin-top: 0.2rem;
    }

    .terms-checkbox a {
      color: var(--secondary-color);
      text-decoration: none;
    }

    .terms-checkbox a:hover {
      color: var(--primary-color);
    }

    .signup-button {
      width: 100%;
      background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
      color: white;
      border: none;
      padding: 0.8rem;
      border-radius: var(--border-radius);
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: var(--transition);
      position: relative;
      overflow: hidden;
    }

    .signup-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(230, 57, 70, 0.3);
    }

    .signup-button:disabled {
      opacity: 0.7;
      cursor: not-allowed;
      transform: none;
    }

    .signup-button .spinner {
      display: none;
      width: 20px;
      height: 20px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s ease-in-out infinite;
      margin-right: 0.5rem;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    .signup-button.loading .spinner {
      display: inline-block;
    }

    .divider {
      text-align: center;
      margin: 1.5rem 0;
      position: relative;
      color: var(--gray-500);
      font-size: 0.9rem;
    }

    .divider::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: var(--gray-300);
      z-index: 1;
    }

    .divider span {
      background: white;
      padding: 0 1rem;
      position: relative;
      z-index: 2;
    }

    .login-link {
      text-align: center;
      margin-top: 1.5rem;
      padding-top: 1.5rem;
      border-top: 1px solid var(--gray-300);
    }

    .login-link a {
      color: var(--secondary-color);
      text-decoration: none;
      font-weight: 500;
      transition: var(--transition);
    }

    .login-link a:hover {
      color: var(--primary-color);
    }

    .back-to-home {
      position: absolute;
      top: 1rem;
      left: 1rem;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border: none;
      padding: 0.5rem;
      border-radius: 50%;
      cursor: pointer;
      transition: var(--transition);
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .back-to-home:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .alert {
      padding: 0.8rem;
      border-radius: var(--border-radius);
      margin-bottom: 1rem;
      font-size: 0.9rem;
      display: none;
    }

    .alert.success {
      background-color: rgba(42, 157, 143, 0.1);
      color: var(--success-color);
      border: 1px solid rgba(42, 157, 143, 0.3);
    }

    .alert.error {
      background-color: rgba(231, 111, 81, 0.1);
      color: var(--danger-color);
      border: 1px solid rgba(231, 111, 81, 0.3);
    }

    .social-signup {
      margin-top: 1rem;
    }

    .social-button {
      width: 100%;
      padding: 0.8rem;
      border: 2px solid var(--gray-300);
      border-radius: var(--border-radius);
      background: white;
      color: var(--gray-700);
      font-size: 0.9rem;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      margin-bottom: 0.5rem;
    }

    .social-button:hover {
      border-color: var(--secondary-color);
      background-color: var(--gray-100);
    }

    .social-button i {
      font-size: 1.1rem;
    }

    @media (max-width: 480px) {
      .signup-container {
        margin: 0.5rem;
      }
      
      .signup-header {
        padding: 1.5rem;
      }
      
      .signup-form {
        padding: 1.5rem;
      }

      .form-row {
        flex-direction: column;
        gap: 0;
      }
    }
  </style>
</head>
<body>
  <button class="back-to-home" onclick="window.location.href='website.html'" title="Back to Home">
    <i class="fas fa-arrow-left"></i>
  </button>

  <div class="signup-container">
    <div class="signup-header">
      <h1><i class="fas fa-gopuram"></i> Heritage Explorer</h1>
      <p>Join us to explore Tamil Nadu's rich heritage and culture</p>
    </div>

    <form class="signup-form" id="signupForm">
      <div class="alert" id="alertMessage"></div>

      <div class="form-group">
        <label for="fullName">Full Name *</label>
        <input type="text" id="fullName" name="full_name" required>
        <div class="validation-message" id="fullNameMessage"></div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="username">Username *</label>
          <input type="text" id="username" name="username" required>
          <div class="validation-message" id="usernameMessage"></div>
        </div>
        <div class="form-group">
          <label for="email">Email *</label>
          <input type="email" id="email" name="email" required>
          <div class="validation-message" id="emailMessage"></div>
        </div>
      </div>

      <div class="form-group">
        <label for="phone">Phone Number</label>
        <input type="tel" id="phone" name="phone" placeholder="+91 9876543210">
        <div class="validation-message" id="phoneMessage"></div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="dateOfBirth">Date of Birth</label>
          <input type="date" id="dateOfBirth" name="date_of_birth">
        </div>
        <div class="form-group">
          <label for="gender">Gender</label>
          <select id="gender" name="gender">
            <option value="">Select Gender</option>
            <option value="male">Male</option>
            <option value="female">Female</option>
            <option value="other">Other</option>
          </select>
        </div>
      </div>

      <div class="form-group">
        <label for="password">Password *</label>
        <input type="password" id="password" name="password" required>
        <i class="fas fa-eye password-toggle" onclick="togglePassword('password')"></i>
        <div class="password-strength">
          <div class="strength-bar">
            <div class="strength-fill" id="strengthFill"></div>
          </div>
          <div class="strength-text" id="strengthText">Password strength</div>
        </div>
        <div class="validation-message" id="passwordMessage"></div>
      </div>

      <div class="form-group">
        <label for="confirmPassword">Confirm Password *</label>
        <input type="password" id="confirmPassword" name="confirm_password" required>
        <i class="fas fa-eye password-toggle" onclick="togglePassword('confirmPassword')"></i>
        <div class="validation-message" id="confirmPasswordMessage"></div>
      </div>

      <div class="terms-checkbox">
        <input type="checkbox" id="agreeTerms" required>
        <label for="agreeTerms">
          I agree to the <a href="#" target="_blank">Terms of Service</a> and 
          <a href="#" target="_blank">Privacy Policy</a>
        </label>
      </div>

      <button type="submit" class="signup-button" id="signupButton">
        <span class="spinner"></span>
        <span class="button-text">Create Account</span>
      </button>

      <div class="divider">
        <span>or</span>
      </div>

      <div class="social-signup">
        <button type="button" class="social-button" onclick="alert('Google signup coming soon!')">
          <i class="fab fa-google"></i>
          Sign up with Google
        </button>
      </div>

      <div class="login-link">
        <p>Already have an account? <a href="login.html">Sign in here</a></p>
      </div>
    </form>
  </div>

  <script src="auth.js"></script>
  <script>
    function togglePassword(fieldId) {
      const passwordInput = document.getElementById(fieldId);
      const toggleIcon = passwordInput.nextElementSibling;
      
      if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
      } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
      }
    }

    // Initialize signup form
    document.addEventListener('DOMContentLoaded', function() {
      initializeSignupForm();
    });
  </script>
</body>
</html>
