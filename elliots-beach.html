<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON>'s Beach - Heritage Explorer</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
  <style>
    :root {
      --primary-color: #1a237e;
      --secondary-color: #457b9d;
      --accent-color: #e63946;
      --light-color: #f1faee;
      --dark-color: #1d3557;
      --success-color: #2e7d32;
      --warning-color: #ff9800;
      --danger-color: #c62828;
      --gray-100: #f8f9fa;
      --gray-200: #e9ecef;
      --gray-300: #dee2e6;
      --gray-400: #ced4da;
      --gray-500: #adb5bd;
      --gray-600: #6c757d;
      --gray-700: #495057;
      --gray-800: #343a40;
      --gray-900: #212529;
      --border-radius: 0.5rem;
      --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
      --transition: all 0.3s ease-in-out;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: var(--gray-800);
      background-color: var(--gray-100);
    }

    header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      text-align: center;
    }

    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      background-color: white;
      box-shadow: var(--box-shadow);
    }

    nav a {
      color: var(--gray-700);
      text-decoration: none;
      margin: 0 1rem;
      font-weight: 500;
      transition: var(--transition);
    }

    nav a:hover {
      color: var(--primary-color);
    }

    main {
      max-width: 1200px;
      margin: 2rem auto;
      padding: 0 1rem;
    }

    .site-details {
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    @media (min-width: 768px) {
      .site-details {
        grid-template-columns: 1fr 1fr;
      }
    }

    .site-image {
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
    }

    .site-image img {
      width: 100%;
      height: auto;
      display: block;
    }

    .site-info h1 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .site-info p {
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }

    .site-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .meta-item {
      display: flex;
      align-items: center;
      background-color: var(--gray-200);
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
    }

    .meta-item i {
      margin-right: 0.5rem;
      color: var(--secondary-color);
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      margin-top: 1.5rem;
    }

    .action-buttons button {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      background-color: var(--secondary-color);
      color: white;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
      display: flex;
      align-items: center;
    }

    .action-buttons button i {
      margin-right: 0.5rem;
    }

    .action-buttons button:hover {
      background-color: var(--primary-color);
    }

    .site-description {
      margin-bottom: 2rem;
    }

    .site-description h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .map-container {
      height: 400px;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      margin-bottom: 2rem;
    }

    .nearby-attractions {
      margin-bottom: 2rem;
    }

    .nearby-attractions h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .attractions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .attraction-card {
      background-color: white;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
    }

    .attraction-card:hover {
      transform: translateY(-5px);
    }

    .attraction-card img {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }

    .attraction-card-content {
      padding: 1rem;
    }

    .attraction-card-content h3 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    footer {
      background-color: var(--primary-color);
      color: white;
      text-align: center;
      padding: 1rem;
      margin-top: 2rem;
    }

    #backToTop {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      width: 3rem;
      height: 3rem;
      background-color: var(--primary-color);
      color: white;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
      display: none;
    }

    #backToTop:hover {
      background-color: var(--secondary-color);
    }

    /* Travel Information Section */
    .travel-info {
      margin-bottom: 2rem;
      background-color: white;
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      overflow: hidden;
    }

    .travel-info-header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .travel-info-header h2 {
      margin: 0;
      font-size: 1.5rem;
    }

    .travel-info-tabs {
      display: flex;
      background-color: var(--gray-200);
    }

    .travel-tab {
      padding: 1rem;
      cursor: pointer;
      flex: 1;
      text-align: center;
      font-weight: 500;
      transition: var(--transition);
      border-bottom: 3px solid transparent;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .travel-tab i {
      margin-right: 0.5rem;
    }

    .travel-tab.active {
      background-color: white;
      border-bottom-color: var(--accent-color);
    }

    .travel-tab:hover:not(.active) {
      background-color: var(--gray-300);
    }

    .travel-content {
      padding: 1.5rem;
    }

    .travel-panel {
      display: none;
    }

    .travel-panel.active {
      display: block;
    }

    /* Weather Panel */
    .weather-info {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      align-items: center;
    }

    .weather-current {
      display: flex;
      align-items: center;
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      flex: 1;
      min-width: 250px;
    }

    .weather-icon {
      font-size: 3rem;
      margin-right: 1rem;
      color: var(--secondary-color);
    }

    .weather-details h3 {
      margin: 0;
      color: var(--primary-color);
    }

    .weather-temp {
      font-size: 2rem;
      font-weight: bold;
    }

    .weather-desc {
      color: var(--gray-700);
    }

    .weather-forecast {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      flex: 2;
      min-width: 300px;
    }

    .forecast-day {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      text-align: center;
      flex: 1;
      min-width: 100px;
    }

    .forecast-day h4 {
      margin: 0 0 0.5rem 0;
      color: var(--primary-color);
    }

    .forecast-icon {
      font-size: 1.5rem;
      margin: 0.5rem 0;
      color: var(--secondary-color);
    }

    /* Transportation Panel */
    .transport-options {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .transport-option {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
      display: flex;
      flex-direction: column;
    }

    .transport-option h3 {
      display: flex;
      align-items: center;
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .transport-option h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .transport-option p {
      margin-bottom: 0.5rem;
    }

    .transport-option .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    /* Beach Activities Panel */
    .hours-container {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .beach-hours, .activities-info {
      flex: 1;
      min-width: 250px;
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .beach-hours h3, .activities-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .beach-hours h3 i, .activities-info h3 i {
      margin-right: 0.5rem;
    }

    .hours-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .hours-list li {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .hours-list li:last-child {
      border-bottom: none;
    }

    .day {
      font-weight: 500;
    }

    .activity-item {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .activity-item:last-child {
      border-bottom: none;
    }

    .activity-name {
      font-weight: 500;
    }

    .activity-price {
      font-weight: bold;
    }

    /* Amenities Panel */
    .amenities-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .amenity-card {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .amenity-card h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .amenity-card h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .amenity-card p {
      margin-bottom: 0.5rem;
    }

    .amenity-card .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    .amenity-card .rating {
      color: var(--warning-color);
      margin-bottom: 0.5rem;
    }

    .weather-tips h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .local-transport h3, .best-time h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .beach-safety {
      margin-top: 1.5rem;
    }

    .beach-safety h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .safety-tips {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      border-left: 4px solid var(--warning-color);
      margin-bottom: 1rem;
    }

    .safety-tips h4 {
      color: var(--warning-color);
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
    }

    .safety-tips h4 i {
      margin-right: 0.5rem;
    }

    .safety-tips ul {
      padding-left: 1.5rem;
      margin-bottom: 0;
    }

    .safety-tips li {
      margin-bottom: 0.25rem;
    }

    .food-scene {
      margin-top: 1.5rem;
    }

    .food-scene h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .food-type {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      margin-bottom: 1rem;
    }

    .food-type h4 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
    }

    .food-type h4 i {
      margin-right: 0.5rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>Heritage Explorer</h1>
    <p>Discover Tamil Nadu's rich heritage and culture</p>
  </header>

  <nav>
    <div>
      <a href="website.html"><i class="fas fa-home"></i> Home</a>
      <a href="#"><i class="fas fa-map-marked-alt"></i> Explore</a>
      <a href="#"><i class="fas fa-info-circle"></i> About</a>
    </div>
    <div>
      <a href="#" id="toggleLang">தமிழ்</a>
    </div>
  </nav>

  <main>
    <div class="site-details">
      <div class="site-image">
        <img src="https://images.fineartamerica.com/images/artworkimages/mediumlarge/1/sunrise-at-edward-elliots-beach-hariharan-ganesh.jpg" alt="Elliot's Beach">
      </div>
      <div class="site-info">
        <h1>Elliot's Beach</h1>
        <div class="site-meta">
          <div class="meta-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>Chennai, Tamil Nadu</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-umbrella-beach"></i>
            <span>Beach</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-monument"></i>
            <span>Karl Schmidt Memorial</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-utensils"></i>
            <span>Food Hub</span>
          </div>
        </div>
        <p>Elliot's Beach, also known as Besant Nagar Beach or "Bessie," is a serene coastal destination in southern Chennai. Less crowded than Marina Beach, it offers a clean shoreline, beautiful sunrise views, and a vibrant food scene. The beach is marked by the Karl Schmidt Memorial and surrounded by upscale residential areas, making it a popular spot for evening walks and relaxation.</p>
        <div class="action-buttons">
          <button id="playAudioButton"><i class="fas fa-volume-up"></i> Listen</button>
          <button id="favoriteButton"><i class="far fa-heart"></i> Favorite</button>
          <button id="shareButton"><i class="fas fa-share-alt"></i> Share</button>
        </div>
      </div>
    </div>

    <div class="site-description">
      <h2>About Elliot's Beach</h2>
      <p>Elliot's Beach, locally known as Besant Nagar Beach or simply "Bessie," is a charming coastal destination located in the Besant Nagar area of Chennai, Tamil Nadu. Named after Edward Elliot, the former Governor of Madras (now Chennai), the beach offers a more relaxed and less commercialized alternative to the city's famous Marina Beach. Situated at the southern end of the Chennai coastline, Elliot's Beach is known for its clean shoreline, relatively calm waters, and peaceful atmosphere that attracts both locals and tourists seeking a tranquil seaside experience.</p>
      <p>One of the most distinctive landmarks at Elliot's Beach is the Karl Schmidt Memorial, a white monument erected in memory of a Dutch sailor who lost his life while saving a drowning swimmer in the early 20th century. This memorial stands as a silent sentinel at the shore and has become an iconic symbol of the beach. The beach is also known for its beautiful sunrise views, making it a popular spot for morning walkers and photographers who come to capture the golden hues of the sun emerging from the Bay of Bengal.</p>
      <p>Unlike Marina Beach, which is known for its bustling atmosphere and vendors, Elliot's Beach has a more upscale ambiance, surrounded by some of Chennai's most affluent residential areas. The beach promenade is lined with a variety of food stalls, cafes, and restaurants offering everything from local South Indian delicacies to international cuisine. The area has evolved into a food lover's paradise, with the famous "Bessie Food Street" nearby, where visitors can sample a wide range of culinary delights after a relaxing time at the beach.</p>
      <p>Elliot's Beach is not just a recreational spot but also a cultural hub where various events, beach sports, and festivals are organized throughout the year. The beach comes alive in the evenings when families, couples, and friends gather to enjoy the cool sea breeze and stunning sunset views. The area around the beach also houses several important landmarks, including the Velankanni Church, the Ashtalakshmi Temple, and the Theosophical Society, adding cultural and spiritual dimensions to a visit to this coastal gem. Despite its popularity, Elliot's Beach has managed to maintain its charm and continues to be a favorite retreat for those looking to escape the hustle and bustle of city life.</p>
    </div>

    <div class="map-container">
      <!-- Map will be embedded here -->
      <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3919.0123456789!2d80.27!3d12.998!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3a525d639e274bd1%3A0xbd3602b5c4ae2bb7!2sElliot%27s%20Beach!5e0!3m2!1sen!2sin!4v1652345678901!5m2!1sen!2sin" width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy" title="Map of Elliot's Beach"></iframe>
    </div>

    <!-- Travel Information Section -->
    <div class="travel-info">
      <div class="travel-info-header">
        <h2>Practical Travel Information</h2>
        <div class="last-updated">Last updated: <span id="lastUpdated">Today</span></div>
      </div>

      <div class="travel-info-tabs">
        <div class="travel-tab active" data-tab="weather">
          <i class="fas fa-cloud-sun"></i> Weather
        </div>
        <div class="travel-tab" data-tab="transport">
          <i class="fas fa-bus"></i> Transportation
        </div>
        <div class="travel-tab" data-tab="hours">
          <i class="fas fa-umbrella-beach"></i> Beach Activities
        </div>
        <div class="travel-tab" data-tab="amenities">
          <i class="fas fa-concierge-bell"></i> Nearby Amenities
        </div>
      </div>

      <div class="travel-content">
        <!-- Weather Panel -->
        <div class="travel-panel active" id="weatherPanel">
          <div class="weather-info">
            <div class="weather-current">
              <div class="weather-icon">
                <i class="fas fa-sun"></i>
              </div>
              <div class="weather-details">
                <h3>Current Weather</h3>
                <div class="weather-temp">31°C</div>
                <div class="weather-desc">Sunny</div>
                <div class="weather-extra">Humidity: 75% | Wind: 10 km/h</div>
              </div>
            </div>

            <div class="weather-forecast">
              <div class="forecast-day">
                <h4>Tomorrow</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun"></i></div>
                <div class="forecast-temp">30°C</div>
                <div class="forecast-desc">Partly Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Wed</h4>
                <div class="forecast-icon"><i class="fas fa-cloud"></i></div>
                <div class="forecast-temp">29°C</div>
                <div class="forecast-desc">Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Thu</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun"></i></div>
                <div class="forecast-temp">31°C</div>
                <div class="forecast-desc">Partly Cloudy</div>
              </div>
            </div>
          </div>

          <div class="weather-tips">
            <h3>Weather Tips</h3>
            <p>Elliot's Beach enjoys a tropical climate with warm temperatures throughout the year. Located in Chennai, the beach experiences three main seasons: summer (March-June), monsoon (July-September), and winter (October-February). The best time to visit is during the winter months when the weather is relatively cooler and more pleasant for beach activities.</p>
            <p>Summer temperatures can soar up to 40°C, making midday beach visits uncomfortable. During this season, it's advisable to visit early morning (6:00 AM - 9:00 AM) or evening (4:00 PM onwards). The northeast monsoon (October-December) brings occasional heavy rainfall, which can limit beach activities. However, the post-monsoon period offers beautiful sunset views and a refreshing atmosphere.</p>
            <p>For the most comfortable experience, wear light cotton clothing, sunglasses, and a hat. Always apply sunscreen (SPF 50+) even on cloudy days, as UV exposure can be high. Carry plenty of water to stay hydrated, especially during summer months. The sea breeze can be quite strong in the evenings, so a light jacket might be useful if you're planning to stay after sunset. The beach is known for its beautiful sunrise and sunset views, so plan your visit accordingly if you want to capture these moments.</p>
          </div>

          <div class="beach-safety">
            <h3>Beach Safety Information</h3>
            <div class="safety-tips">
              <h4><i class="fas fa-exclamation-triangle"></i> Swimming Advisory</h4>
              <ul>
                <li>Swimming is generally not recommended at Elliot's Beach due to strong undercurrents.</li>
                <li>Red flags indicate dangerous conditions - do not enter the water when these are displayed.</li>
                <li>There are no dedicated lifeguards on duty, so enter the water at your own risk.</li>
                <li>The beach has a steep drop-off close to the shore in some areas.</li>
              </ul>
            </div>
            <p>Elliot's Beach is primarily a recreational beach rather than a swimming destination. The Bay of Bengal can have unpredictable currents, especially during and after the monsoon season. Visitors are advised to enjoy the beach from the shore and participate in beach activities rather than swimming. If you do decide to enter the water, never go alone and stay in very shallow areas (ankle to knee-deep only).</p>
            <p>The beach is generally safe for walking, jogging, and other shore activities. Police patrols are present, especially during evenings and weekends. However, it's advisable to avoid isolated areas after dark and keep valuables secure. Beach vendors are not officially permitted, so exercise caution when purchasing food or services from unofficial sellers.</p>
          </div>
        </div>

        <!-- Transportation Panel -->
        <div class="travel-panel" id="transportPanel">
          <div class="transport-options">
            <div class="transport-option">
              <h3><i class="fas fa-plane"></i> By Air</h3>
              <p>Chennai International Airport is the nearest airport.</p>
              <p class="distance">15 km from Elliot's Beach (approx. 45 minutes by car)</p>
              <p>Regular flights from all major Indian cities and international destinations.</p>
              <p>Airport taxis available (₹500-700).</p>
              <p>Pre-paid taxi counters available at the airport.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-train"></i> By Train</h3>
              <p>Chennai Central and Chennai Egmore are the major railway stations.</p>
              <p class="distance">12-15 km from Elliot's Beach (approx. 40-50 minutes by car)</p>
              <p>Tiruvanmiyur Railway Station is the nearest suburban railway station.</p>
              <p class="distance">3 km from Elliot's Beach (approx. 10 minutes by auto-rickshaw)</p>
              <p>Auto-rickshaws and taxis available from stations.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-bus"></i> By Bus</h3>
              <p>MTC (Metropolitan Transport Corporation) buses connect Besant Nagar to other parts of Chennai.</p>
              <p>Bus routes: 21G, 23C, 29C, and 5B stop near Elliot's Beach.</p>
              <p>Frequency: Every 10-15 minutes during peak hours.</p>
              <p>Journey time from city center: Approximately 45-60 minutes.</p>
              <p>Fare: ₹15-25 for ordinary buses, ₹25-40 for deluxe buses.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-car"></i> By Car/Taxi</h3>
              <p>Well-connected via East Coast Road (ECR) and Rajiv Gandhi Salai (OMR).</p>
              <p>Distance from Chennai city center: 10-12 km.</p>
              <p>App-based taxis (Uber, Ola) readily available (₹250-350 from city center).</p>
              <p>Limited parking available near the beach (can be crowded on weekends).</p>
              <p>Car rentals available in Chennai (₹1,200-2,000 per day).</p>
            </div>
          </div>

          <div class="local-transport">
            <h3>Local Transportation</h3>
            <p>Once you reach Besant Nagar, getting around is relatively easy. Auto-rickshaws are readily available for short distances within the area, with fares ranging from ₹30-100 depending on the distance. For more flexibility, consider using app-based services like Uber and Ola, which are widely available in this part of Chennai.</p>
            <p>Besant Nagar is a walkable neighborhood, and many attractions, restaurants, and shops are within walking distance of Elliot's Beach. The beach promenade and surrounding areas are pedestrian-friendly, making it pleasant to explore on foot, especially in the evenings when the weather is cooler. Some visitors also rent bicycles from nearby shops (₹100-150 per hour) to explore the area, as the roads in Besant Nagar are relatively less congested compared to other parts of Chennai.</p>
          </div>
        </div>

        <!-- Beach Activities Panel -->
        <div class="travel-panel" id="hoursPanel">
          <div class="hours-container">
            <div class="beach-hours">
              <h3><i class="far fa-clock"></i> Beach Hours & Safety</h3>
              <ul class="hours-list">
                <li><span class="day">Beach Access:</span> <span class="time">24 hours (unrestricted)</span></li>
                <li><span class="day">Best Visiting Hours:</span> <span class="time">5:30 AM - 8:00 AM, 4:00 PM - 7:00 PM</span></li>
                <li><span class="day">Police Patrol:</span> <span class="time">24 hours</span></li>
                <li><span class="day">Beach Cleaning:</span> <span class="time">5:00 AM - 7:00 AM</span></li>
                <li><span class="day">Vendor Hours:</span> <span class="time">4:00 PM - 10:00 PM</span></li>
              </ul>
              <p class="hours-note">Elliot's Beach is most crowded during weekends and public holidays, especially in the evenings. For a more peaceful experience, weekday mornings are recommended. The beach is well-lit in the evenings, making it safe for night walks along the promenade. However, swimming is not advised at any time due to strong undercurrents. The beach is regularly patrolled by police, ensuring a safe environment for visitors.</p>
            </div>

            <div class="activities-info">
              <h3><i class="fas fa-volleyball-ball"></i> Beach Activities</h3>
              <div class="activity-item">
                <span class="activity-name">Beach Volleyball</span>
                <span class="activity-price">Free (bring your own equipment)</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Beach Cricket</span>
                <span class="activity-price">Free (bring your own equipment)</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Kite Flying</span>
                <span class="activity-price">₹100-200 (to purchase kites)</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Horse Riding</span>
                <span class="activity-price">₹100-150 per ride</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Photography Sessions</span>
                <span class="activity-price">Free (professional shoots may require permission)</span>
              </div>
              <p class="activity-note">Unlike some other beaches, Elliot's Beach focuses more on relaxation than commercial water sports. The beach is popular for morning and evening walks, jogging, and casual beach games. Organized sports activities like beach volleyball are common during weekends. Photography enthusiasts will find the Karl Schmidt Memorial and sunrise/sunset views particularly appealing. Note that swimming is generally not recommended due to strong currents.</p>
            </div>
          </div>

          <div class="food-scene">
            <h3>Famous Food Scene</h3>
            <p>Elliot's Beach is renowned for its vibrant food scene, with the area around the beach often referred to as a food lover's paradise. Here are some of the popular food options available:</p>

            <div class="food-type">
              <h4><i class="fas fa-utensils"></i> Beach Side Snacks</h4>
              <p>Vendors along the beach offer a variety of snacks including roasted corn, cut fruits, ice creams, and coconut water. These are perfect for a quick refreshment while enjoying the beach. Prices range from ₹20-100 depending on the item. The corn on the cob roasted with lime and spices (₹30-40) is particularly popular among visitors.</p>
            </div>

            <div class="food-type">
              <h4><i class="fas fa-hamburger"></i> Bessie Food Street</h4>
              <p>Located near the beach, this famous food street offers a wide range of cuisines from traditional South Indian to international options. Popular spots include Murugan Idli Shop for authentic South Indian breakfast, Mash for burgers and sandwiches, and Pupil for coffee and desserts. Most restaurants are open from 8:00 AM to 11:00 PM, with prices ranging from ₹200-800 for a meal for two.</p>
            </div>

            <div class="food-type">
              <h4><i class="fas fa-coffee"></i> Cafes and Ice Cream Parlors</h4>
              <p>Besant Nagar is home to several popular cafes and ice cream parlors. Don't miss Amadora for artisanal ice creams, Sandy's Chocolate Laboratory for desserts, and Cafe de Paris for continental cuisine. These establishments are perfect for a post-beach treat and are generally open from 11:00 AM to 11:00 PM. A coffee and dessert at these places typically costs between ₹150-300 per person.</p>
            </div>
          </div>
        </div>

        <!-- Amenities Panel -->
        <div class="travel-panel" id="amenitiesPanel">
          <div class="amenities-grid">
            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Taj Fisherman's Cove Resort & Spa</strong></p>
              <p class="distance">8 km from Elliot's Beach</p>
              <p class="rating">★★★★★ (4.6/5)</p>
              <p>Luxury beachfront resort with colonial architecture.</p>
              <p>Price range: ₹15,000 - ₹30,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Somerset Greenways Chennai</strong></p>
              <p class="distance">5 km from Elliot's Beach</p>
              <p class="rating">★★★★☆ (4.3/5)</p>
              <p>Serviced apartments with modern amenities.</p>
              <p>Price range: ₹7,000 - ₹12,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>OYO and Budget Hotels</strong></p>
              <p class="distance">1-3 km from Elliot's Beach</p>
              <p class="rating">★★★☆☆ (3.5/5)</p>
              <p>Various budget options available in Besant Nagar.</p>
              <p>Price range: ₹1,500 - ₹3,500 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Murugan Idli Shop</strong></p>
              <p class="distance">1 km from beach</p>
              <p class="rating">★★★★☆ (4.2/5)</p>
              <p>Famous for authentic South Indian breakfast.</p>
              <p>Price range: ₹200 - ₹400 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Mash</strong></p>
              <p class="distance">500m from beach</p>
              <p class="rating">★★★★☆ (4.1/5)</p>
              <p>Popular for burgers, sandwiches, and continental cuisine.</p>
              <p>Price range: ₹500 - ₹800 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Amadora</strong></p>
              <p class="distance">800m from beach</p>
              <p class="rating">★★★★★ (4.5/5)</p>
              <p>Artisanal ice cream parlor with unique flavors.</p>
              <p>Price range: ₹300 - ₹500 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-shopping-bag"></i> Shopping</h3>
              <p><strong>Besant Nagar Market</strong></p>
              <p class="distance">1 km from beach</p>
              <p>Local market with clothing, accessories, and souvenirs.</p>
              <p>Open from 10:00 AM to 9:00 PM daily.</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-first-aid"></i> Medical Facilities</h3>
              <p><strong>Apollo Hospitals</strong></p>
              <p class="distance">5 km from beach</p>
              <p>Multi-specialty hospital with 24/7 emergency services.</p>
              <p>Contact: +91 44 2829 3333</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-toilet"></i> Public Facilities</h3>
              <p><strong>Beach Restrooms</strong></p>
              <p class="distance">At the beach entrance</p>
              <p>Public toilets available (₹5-10 per use).</p>
              <p>Changing rooms available for a small fee.</p>
            </div>
          </div>

          <div class="best-time">
            <h3>Best Time to Visit</h3>
            <p>The ideal time to visit Elliot's Beach is from October to February when the weather is pleasant with temperatures ranging from 20°C to 30°C. This period offers the most comfortable conditions for beach activities and exploring the surrounding areas. The winter months (December-January) are particularly pleasant with cool sea breezes making evening walks along the promenade especially enjoyable.</p>
            <p>Sunrise at Elliot's Beach (around 5:30 AM - 6:30 AM depending on the season) offers a serene experience with fewer crowds and beautiful photography opportunities. The beach is also famous for its sunset views, with the best time being between 5:30 PM and 6:30 PM. Weekday evenings are less crowded compared to weekends, when the beach can get quite busy with locals and tourists.</p>
            <p>If you're interested in experiencing the local culture and food scene, visit during the evenings (after 4:00 PM) when the beach comes alive with food vendors and local activities. For a more peaceful experience, early mornings are recommended. The beach is also a popular spot for New Year celebrations and local festivals, offering a glimpse into Chennai's vibrant culture if you visit during these times.</p>
          </div>
        </div>
      </div>
    </div>

    <div class="nearby-attractions">
      <h2>Nearby Attractions</h2>
      <div class="attractions-grid">
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Ashtalakshmi_Temple_20190212182003.jpg" alt="Ashtalakshmi Temple">
          <div class="attraction-card-content">
            <h3>Ashtalakshmi Temple</h3>
            <p>A colorful temple dedicated to the eight forms of Goddess Lakshmi, featuring unique architecture with each floor representing a different form of the goddess.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Velankanni_Church_20190212182004.jpg" alt="Velankanni Church">
          <div class="attraction-card-content">
            <h3>Velankanni Church</h3>
            <p>A beautiful shrine dedicated to Our Lady of Good Health, featuring Gothic architecture and attracting devotees from various religious backgrounds.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Theosophical_Society_20190212182005.jpg" alt="Theosophical Society">
          <div class="attraction-card-content">
            <h3>Theosophical Society</h3>
            <p>A serene campus with lush gardens, a library, and a 450-year-old banyan tree, promoting the study of philosophy, religion, and science.</p>
          </div>
        </div>
      </div>
    </div>
  </main>

  <footer>
    <p>&copy; 2025 Heritage Explorer | Explore Tamil Nadu</p>
  </footer>

  <div id="backToTop">
    <i class="fas fa-arrow-up"></i>
  </div>

  <script>
    // Back to Top Button
    const backToTop = document.getElementById("backToTop");
    window.addEventListener("scroll", () => {
      if (window.scrollY > 300) {
        backToTop.style.display = "flex";
      } else {
        backToTop.style.display = "none";
      }
    });
    backToTop.addEventListener("click", () => {
      window.scrollTo({ top: 0, behavior: "smooth" });
    });

    // Audio Playback
    document.getElementById("playAudioButton").addEventListener("click", () => {
      const description = document.querySelector(".site-info p").textContent;
      const utterance = new SpeechSynthesisUtterance(description);
      utterance.lang = "en-US";
      speechSynthesis.speak(utterance);
    });

    // Favorite Button
    document.getElementById("favoriteButton").addEventListener("click", function() {
      const icon = this.querySelector("i");
      if (icon.classList.contains("far")) {
        icon.classList.remove("far");
        icon.classList.add("fas");
        alert("Added to favorites!");
      } else {
        icon.classList.remove("fas");
        icon.classList.add("far");
        alert("Removed from favorites!");
      }
    });

    // Share Button
    document.getElementById("shareButton").addEventListener("click", () => {
      const shareData = {
        title: "Elliot's Beach - Heritage Explorer",
        text: "Check out this beautiful beach in Tamil Nadu!",
        url: window.location.href
      };

      if (navigator.share) {
        navigator.share(shareData)
          .then(() => console.log("Shared successfully"))
          .catch((error) => console.error("Error sharing:", error));
      } else {
        navigator.clipboard.writeText(shareData.url)
          .then(() => alert("URL copied to clipboard"))
          .catch((err) => console.error("Could not copy text: ", err));
      }
    });

    // Initialize the travel info tabs
    document.addEventListener("DOMContentLoaded", function() {
      // Set the last updated date
      const today = new Date();
      document.getElementById("lastUpdated").textContent = today.toLocaleDateString();

      // Tab functionality
      const tabs = document.querySelectorAll(".travel-tab");
      tabs.forEach(tab => {
        tab.addEventListener("click", function() {
          // Remove active class from all tabs
          tabs.forEach(t => t.classList.remove("active"));

          // Add active class to clicked tab
          this.classList.add("active");

          // Hide all panels
          const panels = document.querySelectorAll(".travel-panel");
          panels.forEach(panel => panel.classList.remove("active"));

          // Show the corresponding panel
          const tabId = this.getAttribute("data-tab");
          document.getElementById(tabId + "Panel").classList.add("active");
        });
      });

      // Fetch weather data (mock)
      fetchWeatherData();
    });

    // Weather API Integration (Mock)
    function fetchWeatherData() {
      // Coordinates for Elliot's Beach
      const lat = 12.9980;
      const lon = 80.2700;

      // Mock weather data
      const mockWeatherData = {
        current: {
          temp: 31,
          humidity: 75,
          wind_speed: 10,
          weather: [{ main: "Clear", description: "Sunny", icon: "01d" }]
        },
        daily: [
          {
            dt: Date.now() + 86400000, // Tomorrow
            temp: { day: 30 },
            weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
          },
          {
            dt: Date.now() + 172800000, // Day after tomorrow
            temp: { day: 29 },
            weather: [{ main: "Clouds", description: "Cloudy", icon: "03d" }]
          },
          {
            dt: Date.now() + 259200000, // 3 days from now
            temp: { day: 31 },
            weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
          }
        ]
      };

      updateWeatherUI(mockWeatherData);
    }

    function updateWeatherUI(data) {
      // Update current weather
      document.querySelector(".weather-temp").textContent = `${data.current.temp}°C`;
      document.querySelector(".weather-desc").textContent = data.current.weather[0].description;
      document.querySelector(".weather-extra").textContent = `Humidity: ${data.current.humidity}% | Wind: ${data.current.wind_speed} km/h`;

      // Update weather icon
      const weatherIconElement = document.querySelector(".weather-icon i");
      updateWeatherIcon(weatherIconElement, data.current.weather[0].main);

      // Update forecast
      const forecastDays = document.querySelectorAll(".forecast-day");
      data.daily.forEach((day, index) => {
        if (index < forecastDays.length) {
          const dayElement = forecastDays[index];
          const date = new Date(day.dt);
          const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });

          dayElement.querySelector("h4").textContent = index === 0 ? "Tomorrow" : dayName;
          dayElement.querySelector(".forecast-temp").textContent = `${day.temp.day}°C`;
          dayElement.querySelector(".forecast-desc").textContent = day.weather[0].description;

          // Update forecast icon
          const iconElement = dayElement.querySelector(".forecast-icon i");
          updateWeatherIcon(iconElement, day.weather[0].main);
        }
      });
    }

    function updateWeatherIcon(iconElement, weatherType) {
      // Remove all existing weather classes
      iconElement.className = "";

      // Add the appropriate weather icon class
      switch (weatherType) {
        case "Clear":
          iconElement.classList.add("fas", "fa-sun");
          break;
        case "Clouds":
          iconElement.classList.add("fas", "fa-cloud-sun");
          break;
        case "Rain":
        case "Drizzle":
          iconElement.classList.add("fas", "fa-cloud-rain");
          break;
        case "Thunderstorm":
          iconElement.classList.add("fas", "fa-bolt");
          break;
        case "Snow":
          iconElement.classList.add("fas", "fa-snowflake");
          break;
        case "Mist":
        case "Fog":
        case "Haze":
          iconElement.classList.add("fas", "fa-smog");
          break;
        default:
          iconElement.classList.add("fas", "fa-cloud-sun");
      }
    }
  </script>

  <!-- Language Toggle Script -->
  <script src="language-toggle.js"></script>
</body>
</html>
