<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Marina Beach - Heritage Explorer</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
  <style>
    :root {
      --primary-color: #1a237e;
      --secondary-color: #457b9d;
      --accent-color: #e63946;
      --light-color: #f1faee;
      --dark-color: #1d3557;
      --success-color: #2e7d32;
      --warning-color: #ff9800;
      --danger-color: #c62828;
      --gray-100: #f8f9fa;
      --gray-200: #e9ecef;
      --gray-300: #dee2e6;
      --gray-400: #ced4da;
      --gray-500: #adb5bd;
      --gray-600: #6c757d;
      --gray-700: #495057;
      --gray-800: #343a40;
      --gray-900: #212529;
      --border-radius: 0.5rem;
      --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
      --transition: all 0.3s ease-in-out;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: var(--gray-800);
      background-color: var(--gray-100);
    }

    header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      text-align: center;
    }

    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      background-color: white;
      box-shadow: var(--box-shadow);
    }

    nav a {
      color: var(--gray-700);
      text-decoration: none;
      margin: 0 1rem;
      font-weight: 500;
      transition: var(--transition);
    }

    nav a:hover {
      color: var(--primary-color);
    }

    main {
      max-width: 1200px;
      margin: 2rem auto;
      padding: 0 1rem;
    }

    .site-details {
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    @media (min-width: 768px) {
      .site-details {
        grid-template-columns: 1fr 1fr;
      }
    }

    .site-image {
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
    }

    .site-image img {
      width: 100%;
      height: auto;
      display: block;
    }

    .site-info h1 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .site-info p {
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }

    .site-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .meta-item {
      display: flex;
      align-items: center;
      background-color: var(--gray-200);
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
    }

    .meta-item i {
      margin-right: 0.5rem;
      color: var(--secondary-color);
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      margin-top: 1.5rem;
    }

    .action-buttons button {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      background-color: var(--secondary-color);
      color: white;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
      display: flex;
      align-items: center;
    }

    .action-buttons button i {
      margin-right: 0.5rem;
    }

    .action-buttons button:hover {
      background-color: var(--primary-color);
    }

    .site-description {
      margin-bottom: 2rem;
    }

    .site-description h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .map-container {
      height: 400px;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      margin-bottom: 2rem;
    }

    .nearby-attractions {
      margin-bottom: 2rem;
    }

    .nearby-attractions h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .attractions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .attraction-card {
      background-color: white;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
    }

    .attraction-card:hover {
      transform: translateY(-5px);
    }

    .attraction-card img {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }

    .attraction-card-content {
      padding: 1rem;
    }

    .attraction-card-content h3 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    footer {
      background-color: var(--primary-color);
      color: white;
      text-align: center;
      padding: 1rem;
      margin-top: 2rem;
    }

    #backToTop {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      width: 3rem;
      height: 3rem;
      background-color: var(--primary-color);
      color: white;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
      display: none;
    }

    #backToTop:hover {
      background-color: var(--secondary-color);
    }

    /* Travel Information Section */
    .travel-info {
      margin-bottom: 2rem;
      background-color: white;
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      overflow: hidden;
    }

    .travel-info-header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .travel-info-header h2 {
      margin: 0;
      font-size: 1.5rem;
    }

    .travel-info-tabs {
      display: flex;
      background-color: var(--gray-200);
    }

    .travel-tab {
      padding: 1rem;
      cursor: pointer;
      flex: 1;
      text-align: center;
      font-weight: 500;
      transition: var(--transition);
      border-bottom: 3px solid transparent;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .travel-tab i {
      margin-right: 0.5rem;
    }

    .travel-tab.active {
      background-color: white;
      border-bottom-color: var(--accent-color);
    }

    .travel-tab:hover:not(.active) {
      background-color: var(--gray-300);
    }

    .travel-content {
      padding: 1.5rem;
    }

    .travel-panel {
      display: none;
    }

    .travel-panel.active {
      display: block;
    }

    /* Weather Panel */
    .weather-info {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      align-items: center;
    }

    .weather-current {
      display: flex;
      align-items: center;
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      flex: 1;
      min-width: 250px;
    }

    .weather-icon {
      font-size: 3rem;
      margin-right: 1rem;
      color: var(--secondary-color);
    }

    .weather-details h3 {
      margin: 0;
      color: var(--primary-color);
    }

    .weather-temp {
      font-size: 2rem;
      font-weight: bold;
    }

    .weather-desc {
      color: var(--gray-700);
    }

    .weather-forecast {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      flex: 2;
      min-width: 300px;
    }

    .forecast-day {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      text-align: center;
      flex: 1;
      min-width: 100px;
    }

    .forecast-day h4 {
      margin: 0 0 0.5rem 0;
      color: var(--primary-color);
    }

    .forecast-icon {
      font-size: 1.5rem;
      margin: 0.5rem 0;
      color: var(--secondary-color);
    }

    /* Transportation Panel */
    .transport-options {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .transport-option {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
      display: flex;
      flex-direction: column;
    }

    .transport-option h3 {
      display: flex;
      align-items: center;
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .transport-option h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .transport-option p {
      margin-bottom: 0.5rem;
    }

    .transport-option .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    /* Beach Activities Panel */
    .hours-container {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .beach-hours, .activities-info {
      flex: 1;
      min-width: 250px;
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .beach-hours h3, .activities-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .beach-hours h3 i, .activities-info h3 i {
      margin-right: 0.5rem;
    }

    .hours-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .hours-list li {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .hours-list li:last-child {
      border-bottom: none;
    }

    .day {
      font-weight: 500;
    }

    .activity-item {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .activity-item:last-child {
      border-bottom: none;
    }

    .activity-name {
      font-weight: 500;
    }

    .activity-price {
      font-weight: bold;
    }

    /* Amenities Panel */
    .amenities-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .amenity-card {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .amenity-card h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .amenity-card h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .amenity-card p {
      margin-bottom: 0.5rem;
    }

    .amenity-card .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    .amenity-card .rating {
      color: var(--warning-color);
      margin-bottom: 0.5rem;
    }

    .weather-tips h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .local-transport h3, .best-time h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .beach-attractions {
      margin-top: 1.5rem;
    }

    .beach-attractions h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .attraction-item {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      margin-bottom: 1rem;
    }

    .attraction-item h4 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
    }

    .attraction-item h4 i {
      margin-right: 0.5rem;
    }

    .attraction-details {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 0.5rem;
    }

    .attraction-detail {
      display: flex;
      align-items: center;
      font-size: 0.9rem;
      color: var(--gray-700);
    }

    .attraction-detail i {
      margin-right: 0.25rem;
      font-size: 0.8rem;
    }

    .tide-info {
      margin-top: 1.5rem;
    }

    .tide-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .tide-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 1rem;
    }

    .tide-table th, .tide-table td {
      padding: 0.75rem;
      text-align: left;
      border-bottom: 1px solid var(--gray-300);
    }

    .tide-table th {
      background-color: var(--gray-200);
      font-weight: 600;
      color: var(--primary-color);
    }

    .tide-table tr:last-child td {
      border-bottom: none;
    }

    .beach-events {
      margin-top: 1.5rem;
    }

    .beach-events h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .event-item {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      margin-bottom: 1rem;
    }

    .event-item h4 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    .event-date {
      font-weight: bold;
      color: var(--secondary-color);
      margin-bottom: 0.5rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>Heritage Explorer</h1>
    <p>Discover Tamil Nadu's rich heritage and culture</p>
  </header>

  <nav>
    <div>
      <a href="website.html"><i class="fas fa-home"></i> Home</a>
      <a href="#"><i class="fas fa-map-marked-alt"></i> Explore</a>
      <a href="#"><i class="fas fa-info-circle"></i> About</a>
    </div>
    <div>
      <a href="#" id="toggleLang">தமிழ்</a>
    </div>
  </nav>

  <main>
    <div class="site-details">
      <div class="site-image">
        <img src="https://thumbs.dreamstime.com/b/marina-beach-chennai-city-tamil-nadu-india-bay-bengal-madras-view-light-house-marina-beach-chennai-city-tamil-nadu-india-182911193.jpg" alt="Marina Beach">
      </div>
      <div class="site-info">
        <h1>Marina Beach</h1>
        <div class="site-meta">
          <div class="meta-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>Chennai, Tamil Nadu</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-umbrella-beach"></i>
            <span>Beach</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-ruler-combined"></i>
            <span>13 km long</span>
          </div>
        </div>
        <p>The second-longest urban beach in the world, stretching 13 km along the Bay of Bengal, Marina Beach is Chennai's iconic landmark offering stunning sunrise views, historic monuments, and a vibrant atmosphere with food stalls and local vendors.</p>
        <div class="action-buttons">
          <button id="playAudioButton"><i class="fas fa-volume-up"></i> Listen</button>
          <button id="favoriteButton"><i class="far fa-heart"></i> Favorite</button>
          <button id="shareButton"><i class="fas fa-share-alt"></i> Share</button>
        </div>
      </div>
    </div>

    <div class="site-description">
      <h2>About Marina Beach</h2>
      <p>Marina Beach is a natural urban beach along the Bay of Bengal in Chennai, Tamil Nadu. Stretching about 13 kilometers from Fort St. George in the north to Foreshore Estate in the south, it is the second-longest urban beach in the world. The beach is primarily made of sand that is carried by the Cooum River, which drains into the Bay of Bengal just north of the beach. The wide sandy shore provides a stark contrast to the bustling city behind it, offering residents and visitors a place to escape the urban chaos.</p>
      <p>The beach is not just a natural attraction but also a cultural and historical landmark. It is lined with several important monuments and statues, including those of Mahatma Gandhi, poet Bharathidasan, and former Tamil Nadu Chief Minister M.G. Ramachandran. The iconic lighthouse at the southern end of the beach offers panoramic views of the coastline and the city. The beach is also home to the Anna Swimming Pool complex, the Marina Cricket Ground, and the Triumph of Labour statue, which was erected in 1959 to commemorate the labor movement in India.</p>
      <p>Marina Beach is a hub of activity, especially in the evenings when locals and tourists flock to enjoy the sea breeze and sunset. The beach is dotted with food stalls selling local delicacies like sundal (spiced chickpeas), bajji (fritters), and fresh seafood. Horse rides, kite flying, and beach games are popular activities. While swimming is officially prohibited due to strong undercurrents, the beach remains a favorite spot for morning walks, jogging, and social gatherings. During festivals like Pongal and New Year, the beach becomes even more vibrant with special events and celebrations.</p>
    </div>

    <div class="map-container">
      <!-- Map will be embedded here -->
      <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3886.8969402184316!2d80.28047731482252!3d13.050398990802076!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3a5267ed15c41681%3A0x5f9981c5a43b77a9!2sMarina%20Beach!5e0!3m2!1sen!2sin!4v1652345678901!5m2!1sen!2sin" width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy" title="Map of Marina Beach"></iframe>
    </div>

    <!-- Travel Information Section -->
    <div class="travel-info">
      <div class="travel-info-header">
        <h2>Practical Travel Information</h2>
        <div class="last-updated">Last updated: <span id="lastUpdated">Today</span></div>
      </div>

      <div class="travel-info-tabs">
        <div class="travel-tab active" data-tab="weather">
          <i class="fas fa-cloud-sun"></i> Weather
        </div>
        <div class="travel-tab" data-tab="transport">
          <i class="fas fa-bus"></i> Transportation
        </div>
        <div class="travel-tab" data-tab="hours">
          <i class="fas fa-water"></i> Beach Activities
        </div>
        <div class="travel-tab" data-tab="amenities">
          <i class="fas fa-concierge-bell"></i> Nearby Amenities
        </div>
      </div>

      <div class="travel-content">
        <!-- Weather Panel -->
        <div class="travel-panel active" id="weatherPanel">
          <div class="weather-info">
            <div class="weather-current">
              <div class="weather-icon">
                <i class="fas fa-sun"></i>
              </div>
              <div class="weather-details">
                <h3>Current Weather</h3>
                <div class="weather-temp">32°C</div>
                <div class="weather-desc">Sunny</div>
                <div class="weather-extra">Humidity: 80% | Wind: 14 km/h</div>
              </div>
            </div>

            <div class="weather-forecast">
              <div class="forecast-day">
                <h4>Tomorrow</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun"></i></div>
                <div class="forecast-temp">31°C</div>
                <div class="forecast-desc">Partly Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Wed</h4>
                <div class="forecast-icon"><i class="fas fa-cloud"></i></div>
                <div class="forecast-temp">30°C</div>
                <div class="forecast-desc">Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Thu</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun"></i></div>
                <div class="forecast-temp">32°C</div>
                <div class="forecast-desc">Partly Cloudy</div>
              </div>
            </div>
          </div>

          <div class="weather-tips">
            <h3>Weather Tips</h3>
            <p>Marina Beach experiences a tropical climate with hot and humid conditions throughout most of the year. The beach is located along the Coromandel Coast of Tamil Nadu, which has three main seasons: summer (March-June), monsoon (July-November), and winter (December-February).</p>
            <p>The best time to visit Marina Beach is from November to February when the weather is relatively cooler with temperatures ranging from 20°C to 30°C. This period offers comfortable conditions for beach activities and sightseeing. Summer months (March-June) can be extremely hot with temperatures often exceeding 35°C, making midday visits uncomfortable. The northeast monsoon (October-December) brings heavy rainfall to Chennai, which can occasionally lead to flooding in the city and limited beach access.</p>
            <p>Morning hours (6:00 AM - 9:00 AM) and evening hours (4:00 PM - 7:00 PM) are the best times to visit the beach, as the midday sun can be quite intense. The beach is particularly beautiful during sunrise and sunset, offering spectacular views and photo opportunities. Always carry sunscreen (SPF 50+), sunglasses, a hat, and plenty of water when visiting the beach. Light cotton clothing is recommended due to the humidity. During the evening, the sea breeze can make it feel cooler, so a light jacket might be useful, especially in winter months.</p>
          </div>

          <div class="tide-info">
            <h3>Tide Information</h3>
            <p>While swimming is prohibited at Marina Beach due to strong undercurrents and rough seas, understanding tide patterns is still important for beach activities and safety. Below is the tide schedule for the current week:</p>
            <table class="tide-table">
              <thead>
                <tr>
                  <th>Day</th>
                  <th>High Tide</th>
                  <th>Low Tide</th>
                  <th>Beach Conditions</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Today</td>
                  <td>5:45 AM, 6:12 PM</td>
                  <td>11:30 AM, 12:15 AM</td>
                  <td>Moderate waves, strong currents</td>
                </tr>
                <tr>
                  <td>Tomorrow</td>
                  <td>6:30 AM, 6:55 PM</td>
                  <td>12:15 PM, 1:00 AM</td>
                  <td>Moderate waves, strong currents</td>
                </tr>
                <tr>
                  <td>Wednesday</td>
                  <td>7:15 AM, 7:40 PM</td>
                  <td>1:00 PM, 1:45 AM</td>
                  <td>Calmer morning, rough evening</td>
                </tr>
                <tr>
                  <td>Thursday</td>
                  <td>8:00 AM, 8:25 PM</td>
                  <td>1:45 PM, 2:30 AM</td>
                  <td>Calmer morning, rough evening</td>
                </tr>
              </tbody>
            </table>
            <p>Note: Red flags along the beach indicate dangerous swimming conditions. Always respect warning signs and never attempt to swim at Marina Beach regardless of tide conditions. The beach patrol and lifeguards are present to enforce safety regulations. During high tide, the water reaches closer to the promenade, reducing the beach area available for activities. Low tide offers more beach space for walking, games, and other activities.</p>
          </div>
        </div>

        <!-- Transportation Panel -->
        <div class="travel-panel" id="transportPanel">
          <div class="transport-options">
            <div class="transport-option">
              <h3><i class="fas fa-plane"></i> By Air</h3>
              <p>Chennai International Airport is the nearest airport.</p>
              <p class="distance">15 km from Marina Beach (approx. 30-45 minutes by car)</p>
              <p>Regular flights from all major Indian cities and international destinations.</p>
              <p>Pre-paid taxis available from the airport (₹400-600).</p>
              <p>App-based cabs like Ola and Uber also operate from the airport.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-train"></i> By Train</h3>
              <p>Chennai Central and Chennai Egmore are the major railway stations.</p>
              <p class="distance">5-7 km from Marina Beach (approx. 15-20 minutes by car)</p>
              <p>Well-connected to all major cities across India.</p>
              <p>Auto-rickshaws and taxis available from the stations (₹100-200).</p>
              <p>Local trains to Chepauk/Beach station bring you closer to Marina Beach.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-bus"></i> By Bus</h3>
              <p>Chennai Mofussil Bus Terminus (CMBT) is the main bus terminal.</p>
              <p class="distance">12 km from Marina Beach (approx. 30-40 minutes by car)</p>
              <p>City buses (routes 21, 29C, 27B) stop near Marina Beach.</p>
              <p>Fare: ₹10-25 depending on the bus type and distance.</p>
              <p>Frequency: Every 5-10 minutes during daytime.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-subway"></i> By Metro</h3>
              <p>Government Estate Metro Station is the closest to Marina Beach.</p>
              <p class="distance">1.5 km from Marina Beach (15-20 minute walk)</p>
              <p>Blue Line connects to Chennai Central and Airport.</p>
              <p>Fare: ₹10-60 depending on the distance.</p>
              <p>Frequency: Every 10 minutes from 6:00 AM to 10:00 PM.</p>
            </div>
          </div>

          <div class="local-transport">
            <h3>Local Transportation</h3>
            <p>Getting around Marina Beach and its surroundings is relatively easy with several transportation options available. Auto-rickshaws are abundant and can be hired for short distances within the city. While they should operate on meters, negotiating the fare beforehand (₹30-150 depending on distance) is common practice. App-based services like Ola and Uber provide a more convenient option with fixed fares.</p>
            <p>Chennai's Metropolitan Transport Corporation (MTC) operates numerous bus routes that pass by or near Marina Beach. Bus stops are located along Kamarajar Salai (Beach Road) at regular intervals. Buses are an economical option with fares ranging from ₹5-25 depending on the distance and bus type (ordinary, deluxe, or air-conditioned). The Chennai Metro Blue Line provides a quick and comfortable way to reach areas near Marina Beach, with Government Estate and High Court stations being the closest.</p>
            <p>For those who prefer walking, Marina Beach has a well-maintained promenade that stretches for several kilometers. Walking is actually the best way to explore the beach and its numerous statues, memorials, and attractions. Bicycle rentals are not commonly available near the beach, but some hotels offer this service to their guests. For visitors planning to explore multiple attractions in Chennai in a single day, hiring a taxi for the day (₹1,500-2,500) might be the most convenient option.</p>
          </div>
        </div>

        <!-- Beach Activities Panel -->
        <div class="travel-panel" id="hoursPanel">
          <div class="hours-container">
            <div class="beach-hours">
              <h3><i class="far fa-clock"></i> Beach Hours & Safety</h3>
              <ul class="hours-list">
                <li><span class="day">Beach Access:</span> <span class="time">24 hours (unrestricted)</span></li>
                <li><span class="day">Best Visiting Hours:</span> <span class="time">5:00 AM - 8:00 AM, 4:00 PM - 7:00 PM</span></li>
                <li><span class="day">Lifeguard Hours:</span> <span class="time">6:00 AM - 7:00 PM</span></li>
                <li><span class="day">Beach Patrol:</span> <span class="time">24 hours</span></li>
                <li><span class="day">Lighting Hours:</span> <span class="time">6:00 PM - 10:00 PM</span></li>
              </ul>
              <p class="hours-note">Swimming is strictly prohibited at Marina Beach due to strong undercurrents and rough sea conditions. The beach is patrolled by police and lifeguards who enforce this rule for visitor safety. Despite the swimming ban, Marina Beach offers numerous other activities and attractions. The beach is busiest during evenings, especially on weekends and holidays. Early mornings are ideal for those seeking a more peaceful experience. During the northeast monsoon season (October-December), parts of the beach may be temporarily closed due to high tides and flooding.</p>
            </div>

            <div class="activities-info">
              <h3><i class="fas fa-umbrella-beach"></i> Beach Activities</h3>
              <div class="activity-item">
                <span class="activity-name">Horse Riding (10 minutes)</span>
                <span class="activity-price">₹100 - ₹200</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Pony Riding for Children (5 minutes)</span>
                <span class="activity-price">₹50 - ₹100</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Balloon Shooting Game</span>
                <span class="activity-price">₹30 - ₹50 per round</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Beach Photography (with props)</span>
                <span class="activity-price">₹50 - ₹100 per photo</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Kite Flying (kite purchase)</span>
                <span class="activity-price">₹20 - ₹100</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Beach Toy Vendors</span>
                <span class="activity-price">₹20 - ₹200</span>
              </div>
              <p class="activity-note">Prices are approximate and may vary based on season, vendor, and negotiation. Most activities are available in the evening hours when the beach is most active. Some vendors may not be officially licensed, so exercise caution and agree on prices before engaging in activities. During festivals and special events, additional activities and entertainment options may be available at varying prices.</p>
            </div>
          </div>

          <div class="beach-attractions">
            <h3>Popular Beach Attractions</h3>
            <p>Marina Beach is not just a stretch of sand but a cultural and historical landmark with numerous attractions along its 13 km length. Here are some of the most notable features:</p>

            <div class="attraction-item">
              <h4><i class="fas fa-monument"></i> Statues and Memorials</h4>
              <div class="attraction-details">
                <span class="attraction-detail"><i class="fas fa-map-marker-alt"></i> Throughout the beach promenade</span>
                <span class="attraction-detail"><i class="fas fa-clock"></i> Best viewed: Early morning or evening</span>
              </div>
              <p>Marina Beach is home to numerous statues of Tamil scholars, freedom fighters, and political leaders. Notable ones include Mahatma Gandhi, Triumph of Labour statue, Robert Caldwell, Kambar, Avvaiyar, and several former Chief Ministers of Tamil Nadu. Each statue has historical significance and represents an important figure in Tamil culture or Indian history. The statues are spread along the beach promenade and offer interesting insights into local history and culture.</p>
            </div>

            <div class="attraction-item">
              <h4><i class="fas fa-landmark"></i> Marina Lighthouse</h4>
              <div class="attraction-details">
                <span class="attraction-detail"><i class="fas fa-map-marker-alt"></i> Southern end of Marina Beach</span>
                <span class="attraction-detail"><i class="fas fa-clock"></i> Open: 10:00 AM - 1:00 PM, 3:00 PM - 6:00 PM (Closed Mondays)</span>
                <span class="attraction-detail"><i class="fas fa-ticket-alt"></i> Entry Fee: ₹20 for Indians, ₹100 for foreigners</span>
              </div>
              <p>The iconic red and white lighthouse offers panoramic views of the coastline and the city from its observation deck. Built in 1976, this 46-meter tall structure replaced the old lighthouse built by the British in 1844. Visitors can climb to the top (either by stairs or elevator) for breathtaking views of the Bay of Bengal and Chennai's skyline. The lighthouse also houses a small museum showcasing the history of lighthouses along the Indian coast.</p>
            </div>

            <div class="attraction-item">
              <h4><i class="fas fa-store"></i> Beach Markets and Food Stalls</h4>
              <div class="attraction-details">
                <span class="attraction-detail"><i class="fas fa-map-marker-alt"></i> Throughout the beach</span>
                <span class="attraction-detail"><i class="fas fa-clock"></i> Best time: 4:00 PM - 8:00 PM</span>
              </div>
              <p>Marina Beach comes alive in the evenings with numerous food stalls and vendors selling local snacks and street food. Popular items include sundal (spiced chickpeas), bajji (vegetable fritters), roasted corn, cotton candy, and fresh coconut water. The beach also has vendors selling toys, handicrafts, and souvenirs. While enjoying the local cuisine, be mindful of hygiene and preferably choose vendors with good crowds, indicating popular and fresh food.</p>
            </div>
          </div>

          <div class="beach-events">
            <h3>Annual Events</h3>
            <div class="event-item">
              <h4>Pongal Celebrations</h4>
              <p class="event-date">Mid-January (typically January 14-17)</p>
              <p>During the Tamil harvest festival of Pongal, Marina Beach hosts special cultural programs, traditional games, and food festivals. The beach is decorated with kolam (rangoli) designs, and visitors can experience traditional Tamil celebrations.</p>
            </div>
            <div class="event-item">
              <h4>Chennai Sangamam</h4>
              <p class="event-date">January (dates vary)</p>
              <p>This cultural festival showcases Tamil Nadu's folk arts, music, and dance forms with performances held at various locations including Marina Beach. The event features traditional art forms that are rarely seen in urban settings.</p>
            </div>
            <div class="event-item">
              <h4>Independence Day and Republic Day</h4>
              <p class="event-date">August 15 and January 26</p>
              <p>Marina Beach sees special flag hoisting ceremonies, parades, and cultural programs during these national holidays. The beach is particularly crowded and festive on these days.</p>
            </div>
          </div>
        </div>

        <!-- Amenities Panel -->
        <div class="travel-panel" id="amenitiesPanel">
          <div class="amenities-grid">
            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Taj Coromandel</strong></p>
              <p class="distance">3 km from Marina Beach</p>
              <p class="rating">★★★★★ (4.7/5)</p>
              <p>Luxury hotel with elegant rooms and fine dining options.</p>
              <p>Price range: ₹12,000 - ₹25,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>The Raintree, Anna Salai</strong></p>
              <p class="distance">2.5 km from Marina Beach</p>
              <p class="rating">★★★★☆ (4.3/5)</p>
              <p>Business hotel with rooftop pool and multiple restaurants.</p>
              <p>Price range: ₹7,000 - ₹12,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Hotel Palmyra</strong></p>
              <p class="distance">1 km from Marina Beach</p>
              <p class="rating">★★★☆☆ (3.8/5)</p>
              <p>Budget-friendly hotel with basic amenities.</p>
              <p>Price range: ₹2,000 - ₹4,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Buhari Hotel</strong></p>
              <p class="distance">2 km from Marina Beach</p>
              <p class="rating">★★★★☆ (4.2/5)</p>
              <p>Famous for biryani and Indo-Chinese cuisine.</p>
              <p>Price range: ₹500 - ₹1,000 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Saravana Bhavan</strong></p>
              <p class="distance">1.5 km from Marina Beach</p>
              <p class="rating">★★★★☆ (4.1/5)</p>
              <p>Popular vegetarian restaurant serving South Indian cuisine.</p>
              <p>Price range: ₹400 - ₹700 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Beach Vendors</strong></p>
              <p class="distance">On Marina Beach</p>
              <p class="rating">★★★☆☆ (3.5/5)</p>
              <p>Street food stalls selling local snacks and beverages.</p>
              <p>Price range: ₹50 - ₹200 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-shopping-bag"></i> Shopping</h3>
              <p><strong>Spencer Plaza</strong></p>
              <p class="distance">2 km from Marina Beach</p>
              <p>One of India's oldest shopping malls with various retail outlets.</p>
              <p>Good for clothing, electronics, and souvenirs.</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-first-aid"></i> Medical Facilities</h3>
              <p><strong>Apollo Hospitals</strong></p>
              <p class="distance">5 km from Marina Beach</p>
              <p>Multi-specialty hospital with 24/7 emergency services.</p>
              <p>Contact: +91 44 2829 3333</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-info-circle"></i> Tourist Information</h3>
              <p><strong>Tamil Nadu Tourism Office</strong></p>
              <p class="distance">2 km from Marina Beach (at Wallajah Road)</p>
              <p>Provides maps, brochures, and guided tour information.</p>
              <p>Contact: +91 44 2536 8841</p>
            </div>
          </div>

          <div class="best-time">
            <h3>Best Time to Visit</h3>
            <p>The ideal time to visit Marina Beach is from November to February when the weather is pleasant with temperatures ranging from 20°C to 30°C. This period offers comfortable conditions for exploring the beach and its surroundings. Early mornings (5:00 AM - 8:00 AM) are perfect for peaceful walks, jogging, and watching the sunrise over the Bay of Bengal. Evenings (4:00 PM - 7:00 PM) are more vibrant with food stalls, activities, and the opportunity to watch the sunset.</p>
            <p>Weekdays are generally less crowded than weekends. If you visit on weekends, be prepared for large crowds, especially in the evenings. The beach is particularly crowded during public holidays and festivals. The summer months (March-June) can be extremely hot and humid, making midday visits uncomfortable. The northeast monsoon season (October-December) brings heavy rainfall to Chennai, which can occasionally lead to flooding and limited beach access.</p>
            <p>For photography enthusiasts, the golden hours during sunrise and sunset offer the best lighting conditions. The beach is illuminated in the evenings, creating a beautiful atmosphere for night photography. If you're interested in cultural events, plan your visit during Pongal (mid-January) or Chennai Sangamam (January) when the beach hosts special performances and celebrations.</p>
          </div>
        </div>
      </div>
    </div>

    <div class="nearby-attractions">
      <h2>Nearby Attractions</h2>
      <div class="attractions-grid">
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Marina_Lighthouse_20190212182003.jpg" alt="Marina Lighthouse">
          <div class="attraction-card-content">
            <h3>Marina Lighthouse</h3>
            <p>A historic lighthouse offering panoramic views of the coastline and the city of Chennai from its observation deck.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Santhome_Basilica_20190212182004.jpg" alt="Santhome Basilica">
          <div class="attraction-card-content">
            <h3>Santhome Basilica</h3>
            <p>A neo-Gothic church built over the tomb of St. Thomas, one of the twelve apostles of Jesus, who is believed to have visited India.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/MGR_Memorial_20190212182005.jpg" alt="MGR Memorial">
          <div class="attraction-card-content">
            <h3>MGR Memorial</h3>
            <p>A memorial dedicated to M.G. Ramachandran, a popular actor and former Chief Minister of Tamil Nadu, located along the beach.</p>
          </div>
        </div>
      </div>
    </div>
  </main>

  <footer>
    <p>&copy; 2025 Heritage Explorer | Explore Tamil Nadu</p>
  </footer>

  <div id="backToTop">
    <i class="fas fa-arrow-up"></i>
  </div>

  <script>
    // Back to Top Button
    const backToTop = document.getElementById("backToTop");
    window.addEventListener("scroll", () => {
      if (window.scrollY > 300) {
        backToTop.style.display = "flex";
      } else {
        backToTop.style.display = "none";
      }
    });
    backToTop.addEventListener("click", () => {
      window.scrollTo({ top: 0, behavior: "smooth" });
    });

    // Audio Playback
    document.getElementById("playAudioButton").addEventListener("click", () => {
      const description = document.querySelector(".site-info p").textContent;
      const utterance = new SpeechSynthesisUtterance(description);
      utterance.lang = "en-US";
      speechSynthesis.speak(utterance);
    });

    // Favorite Button
    document.getElementById("favoriteButton").addEventListener("click", function() {
      const icon = this.querySelector("i");
      if (icon.classList.contains("far")) {
        icon.classList.remove("far");
        icon.classList.add("fas");
        alert("Added to favorites!");
      } else {
        icon.classList.remove("fas");
        icon.classList.add("far");
        alert("Removed from favorites!");
      }
    });

    // Share Button
    document.getElementById("shareButton").addEventListener("click", () => {
      const shareData = {
        title: "Marina Beach - Heritage Explorer",
        text: "Check out this beautiful beach in Tamil Nadu!",
        url: window.location.href
      };

      if (navigator.share) {
        navigator.share(shareData)
          .then(() => console.log("Shared successfully"))
          .catch((error) => console.error("Error sharing:", error));
      } else {
        navigator.clipboard.writeText(shareData.url)
          .then(() => alert("URL copied to clipboard"))
          .catch((err) => console.error("Could not copy text: ", err));
      }
    });

    // Initialize the travel info tabs
    document.addEventListener("DOMContentLoaded", function() {
      // Set the last updated date
      const today = new Date();
      document.getElementById("lastUpdated").textContent = today.toLocaleDateString();

      // Tab functionality
      const tabs = document.querySelectorAll(".travel-tab");
      tabs.forEach(tab => {
        tab.addEventListener("click", function() {
          // Remove active class from all tabs
          tabs.forEach(t => t.classList.remove("active"));

          // Add active class to clicked tab
          this.classList.add("active");

          // Hide all panels
          const panels = document.querySelectorAll(".travel-panel");
          panels.forEach(panel => panel.classList.remove("active"));

          // Show the corresponding panel
          const tabId = this.getAttribute("data-tab");
          document.getElementById(tabId + "Panel").classList.add("active");
        });
      });

      // Fetch weather data (mock)
      fetchWeatherData();
    });

    // Weather API Integration (Mock)
    function fetchWeatherData() {
      // Coordinates for Marina Beach
      const lat = 13.0500;
      const lon = 80.2824;

      // Mock weather data
      const mockWeatherData = {
        current: {
          temp: 32,
          humidity: 80,
          wind_speed: 14,
          weather: [{ main: "Clear", description: "Sunny", icon: "01d" }]
        },
        daily: [
          {
            dt: Date.now() + 86400000, // Tomorrow
            temp: { day: 31 },
            weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
          },
          {
            dt: Date.now() + 172800000, // Day after tomorrow
            temp: { day: 30 },
            weather: [{ main: "Clouds", description: "Cloudy", icon: "03d" }]
          },
          {
            dt: Date.now() + 259200000, // 3 days from now
            temp: { day: 32 },
            weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
          }
        ]
      };

      updateWeatherUI(mockWeatherData);
    }

    function updateWeatherUI(data) {
      // Update current weather
      document.querySelector(".weather-temp").textContent = `${data.current.temp}°C`;
      document.querySelector(".weather-desc").textContent = data.current.weather[0].description;
      document.querySelector(".weather-extra").textContent = `Humidity: ${data.current.humidity}% | Wind: ${data.current.wind_speed} km/h`;

      // Update weather icon
      const weatherIconElement = document.querySelector(".weather-icon i");
      updateWeatherIcon(weatherIconElement, data.current.weather[0].main);

      // Update forecast
      const forecastDays = document.querySelectorAll(".forecast-day");
      data.daily.forEach((day, index) => {
        if (index < forecastDays.length) {
          const dayElement = forecastDays[index];
          const date = new Date(day.dt);
          const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });

          dayElement.querySelector("h4").textContent = index === 0 ? "Tomorrow" : dayName;
          dayElement.querySelector(".forecast-temp").textContent = `${day.temp.day}°C`;
          dayElement.querySelector(".forecast-desc").textContent = day.weather[0].description;

          // Update forecast icon
          const iconElement = dayElement.querySelector(".forecast-icon i");
          updateWeatherIcon(iconElement, day.weather[0].main);
        }
      });
    }

    function updateWeatherIcon(iconElement, weatherType) {
      // Remove all existing weather classes
      iconElement.className = "";

      // Add the appropriate weather icon class
      switch (weatherType) {
        case "Clear":
          iconElement.classList.add("fas", "fa-sun");
          break;
        case "Clouds":
          iconElement.classList.add("fas", "fa-cloud");
          break;
        case "Rain":
        case "Drizzle":
          iconElement.classList.add("fas", "fa-cloud-rain");
          break;
        case "Thunderstorm":
          iconElement.classList.add("fas", "fa-bolt");
          break;
        case "Snow":
          iconElement.classList.add("fas", "fa-snowflake");
          break;
        case "Mist":
        case "Fog":
        case "Haze":
          iconElement.classList.add("fas", "fa-smog");
          break;
        default:
          iconElement.classList.add("fas", "fa-cloud-sun");
      }
    }
  </script>

  <!-- Language Toggle Script -->
  <script src="language-toggle.js"></script>
</body>
</html>
