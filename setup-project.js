#!/usr/bin/env node

/**
 * Heritage Explorer - Project Setup Script
 * Helps set up the project for development
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('\n🏛️ Heritage Explorer - Project Setup\n');

// Check if we're on Windows
const isWindows = process.platform === 'win32';

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

function log(type, message) {
    const timestamp = new Date().toLocaleTimeString();
    switch (type) {
        case 'success':
            console.log(`${colors.green}✅ [${timestamp}] ${message}${colors.reset}`);
            break;
        case 'error':
            console.log(`${colors.red}❌ [${timestamp}] ${message}${colors.reset}`);
            break;
        case 'warning':
            console.log(`${colors.yellow}⚠️  [${timestamp}] ${message}${colors.reset}`);
            break;
        case 'info':
            console.log(`${colors.blue}ℹ️  [${timestamp}] ${message}${colors.reset}`);
            break;
        default:
            console.log(`[${timestamp}] ${message}`);
    }
}

// Check project structure
function checkProjectStructure() {
    log('info', 'Checking project structure...');
    
    const requiredFiles = [
        'website.html',
        'auth.php',
        'config.php',
        'auth.js'
    ];
    
    const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));
    
    if (missingFiles.length > 0) {
        log('warning', `Missing files: ${missingFiles.join(', ')}`);
        return false;
    }
    
    log('success', 'All required files found');
    return true;
}

// Check for XAMPP installation
function checkXAMPP() {
    log('info', 'Checking for XAMPP installation...');
    
    const xamppPaths = [
        'C:\\xampp\\xampp-control.exe',
        '/Applications/XAMPP/xamppfiles/xampp',
        '/opt/lampp/lampp'
    ];
    
    const xamppFound = xamppPaths.some(path => fs.existsSync(path));
    
    if (xamppFound) {
        log('success', 'XAMPP installation found');
        return true;
    } else {
        log('warning', 'XAMPP not found');
        log('info', 'Download XAMPP from: https://www.apachefriends.org/');
        return false;
    }
}

// Create development configuration
function createDevConfig() {
    log('info', 'Creating development configuration...');
    
    const devConfig = {
        name: 'Heritage Explorer',
        version: '1.0.0',
        mode: 'development',
        server: {
            port: 3000,
            host: 'localhost'
        },
        features: {
            authentication: false,
            database: false,
            mockData: true
        },
        xampp: {
            required: true,
            webRoot: isWindows ? 'C:\\xampp\\htdocs\\heritage-explorer' : '/opt/lampp/htdocs/heritage-explorer'
        }
    };
    
    fs.writeFileSync('dev-config.json', JSON.stringify(devConfig, null, 2));
    log('success', 'Development configuration created');
}

// Show setup instructions
function showInstructions() {
    console.log('\n' + '='.repeat(50));
    console.log('🏛️  HERITAGE EXPLORER SETUP COMPLETE');
    console.log('='.repeat(50));
    
    console.log('\n📋 Available Commands:');
    console.log(`${colors.cyan}pnpm dev${colors.reset}      - Start development server (Node.js)`);
    console.log(`${colors.cyan}pnpm xampp${colors.reset}    - Setup and run with XAMPP (full features)`);
    console.log(`${colors.cyan}pnpm test${colors.reset}     - Test server functionality`);
    console.log(`${colors.cyan}pnpm help${colors.reset}     - Show help information`);
    
    console.log('\n🚀 Quick Start:');
    console.log('1. For frontend development:');
    console.log(`   ${colors.green}pnpm dev${colors.reset}`);
    console.log('   Then open: http://localhost:3000');
    
    console.log('\n2. For full functionality (recommended):');
    console.log(`   ${colors.green}pnpm xampp${colors.reset}`);
    console.log('   Then open: http://localhost/heritage-explorer/');
    
    console.log('\n⚠️  Development vs Production:');
    console.log('• Development server (pnpm dev): Frontend only, mocked PHP');
    console.log('• XAMPP server (pnpm xampp): Full PHP + MySQL functionality');
    
    console.log('\n🔗 URLs:');
    console.log('• Development: http://localhost:3000/website.html');
    console.log('• Production:  http://localhost/heritage-explorer/website.html');
    
    console.log('\n💡 Tips:');
    console.log('• Use development server for quick frontend changes');
    console.log('• Use XAMPP for testing authentication and database features');
    console.log('• Check project-status.html for detailed setup information');
    
    console.log('\n' + '='.repeat(50) + '\n');
}

// Main setup function
async function setup() {
    try {
        log('info', 'Starting Heritage Explorer setup...');
        
        // Check project structure
        const structureOk = checkProjectStructure();
        
        // Check XAMPP
        const xamppOk = checkXAMPP();
        
        // Create dev config
        createDevConfig();
        
        // Show final instructions
        showInstructions();
        
        if (!structureOk) {
            log('warning', 'Some project files are missing. The project may not work correctly.');
        }
        
        if (!xamppOk) {
            log('warning', 'XAMPP not found. You can still use the development server, but authentication features will be mocked.');
        }
        
        log('success', 'Setup completed successfully!');
        
    } catch (error) {
        log('error', `Setup failed: ${error.message}`);
        process.exit(1);
    }
}

// Run setup
if (require.main === module) {
    setup();
}

module.exports = { setup, checkProjectStructure, checkXAMPP };
