<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audio Guide Demo Files</title>
    <style>
        body {
            font-family: 'Poppins', <PERSON>l, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1 {
            color: #e63946;
            border-bottom: 2px solid #e63946;
            padding-bottom: 10px;
        }
        h2 {
            color: #457b9d;
            margin-top: 30px;
        }
        .audio-container {
            background-color: #f1faee;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .audio-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        audio {
            width: 100%;
            margin-top: 10px;
        }
        .language {
            display: inline-block;
            background-color: #a8dadc;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            margin-right: 10px;
        }
        .note {
            background-color: #ffe8e8;
            padding: 15px;
            border-left: 4px solid #e63946;
            margin: 20px 0;
        }
        .chapters {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
        }
        .chapter {
            margin-bottom: 5px;
        }
        .time {
            color: #1d3557;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Audio Guide Demo Files</h1>
    
    <div class="note">
        <p><strong>Note:</strong> This page contains demo audio files for the Heritage Explorer website's multilingual audio guide feature. In a production environment, these would be actual MP3 files with professional narration in English and Tamil.</p>
        <p>For demonstration purposes, we're using browser-generated audio from the Web Speech API.</p>
    </div>
    
    <h2>Shore Temple</h2>
    
    <div class="audio-container">
        <div class="audio-title">
            <span class="language">English</span> Shore Temple Audio Guide
        </div>
        <div class="chapters">
            <div class="chapter"><span class="time">0:00</span> - Introduction</div>
            <div class="chapter"><span class="time">1:00</span> - Historical Background</div>
            <div class="chapter"><span class="time">2:00</span> - Architecture</div>
            <div class="chapter"><span class="time">3:00</span> - Sculptures & Carvings</div>
            <div class="chapter"><span class="time">4:00</span> - Religious Significance</div>
            <div class="chapter"><span class="time">5:00</span> - Conservation Efforts</div>
        </div>
        <button onclick="playDemoAudio('shore-temple', 'en')">Generate Demo Audio</button>
        <div id="shore-temple-en-player"></div>
    </div>
    
    <div class="audio-container">
        <div class="audio-title">
            <span class="language">தமிழ்</span> கடற்கரை கோவில் ஒலி வழிகாட்டி
        </div>
        <div class="chapters">
            <div class="chapter"><span class="time">0:00</span> - அறிமுகம்</div>
            <div class="chapter"><span class="time">1:00</span> - வரலாற்றுப் பின்னணி</div>
            <div class="chapter"><span class="time">2:00</span> - கட்டிடக்கலை</div>
            <div class="chapter"><span class="time">3:00</span> - சிற்பங்கள் & செதுக்கல்கள்</div>
            <div class="chapter"><span class="time">4:00</span> - மத முக்கியத்துவம்</div>
            <div class="chapter"><span class="time">5:00</span> - பாதுகாப்பு முயற்சிகள்</div>
        </div>
        <button onclick="playDemoAudio('shore-temple', 'ta')">Generate Demo Audio</button>
        <div id="shore-temple-ta-player"></div>
    </div>
    
    <h2>Brihadeeswarar Temple</h2>
    
    <div class="audio-container">
        <div class="audio-title">
            <span class="language">English</span> Brihadeeswarar Temple Audio Guide
        </div>
        <div class="chapters">
            <div class="chapter"><span class="time">0:00</span> - Introduction</div>
            <div class="chapter"><span class="time">1:00</span> - Historical Background</div>
            <div class="chapter"><span class="time">2:00</span> - Architecture</div>
            <div class="chapter"><span class="time">3:00</span> - Sculptures & Carvings</div>
            <div class="chapter"><span class="time">4:00</span> - Religious Significance</div>
            <div class="chapter"><span class="time">5:00</span> - Conservation Efforts</div>
        </div>
        <button onclick="playDemoAudio('brihadeeswarar-temple', 'en')">Generate Demo Audio</button>
        <div id="brihadeeswarar-temple-en-player"></div>
    </div>
    
    <div class="audio-container">
        <div class="audio-title">
            <span class="language">தமிழ்</span> பிரகதீஸ்வரர் கோவில் ஒலி வழிகாட்டி
        </div>
        <div class="chapters">
            <div class="chapter"><span class="time">0:00</span> - அறிமுகம்</div>
            <div class="chapter"><span class="time">1:00</span> - வரலாற்றுப் பின்னணி</div>
            <div class="chapter"><span class="time">2:00</span> - கட்டிடக்கலை</div>
            <div class="chapter"><span class="time">3:00</span> - சிற்பங்கள் & செதுக்கல்கள்</div>
            <div class="chapter"><span class="time">4:00</span> - மத முக்கியத்துவம்</div>
            <div class="chapter"><span class="time">5:00</span> - பாதுகாப்பு முயற்சிகள்</div>
        </div>
        <button onclick="playDemoAudio('brihadeeswarar-temple', 'ta')">Generate Demo Audio</button>
        <div id="brihadeeswarar-temple-ta-player"></div>
    </div>
    
    <h2>Ooty Hill Station</h2>
    
    <div class="audio-container">
        <div class="audio-title">
            <span class="language">English</span> Ooty Hill Station Audio Guide
        </div>
        <div class="chapters">
            <div class="chapter"><span class="time">0:00</span> - Introduction</div>
            <div class="chapter"><span class="time">1:00</span> - Historical Background</div>
            <div class="chapter"><span class="time">2:00</span> - Natural Attractions</div>
            <div class="chapter"><span class="time">3:00</span> - Colonial Heritage</div>
            <div class="chapter"><span class="time">4:00</span> - Tea Plantations</div>
            <div class="chapter"><span class="time">5:00</span> - Travel Tips</div>
        </div>
        <button onclick="playDemoAudio('ooty', 'en')">Generate Demo Audio</button>
        <div id="ooty-en-player"></div>
    </div>
    
    <div class="audio-container">
        <div class="audio-title">
            <span class="language">தமிழ்</span> ஊட்டி மலை நிலையம் ஒலி வழிகாட்டி
        </div>
        <div class="chapters">
            <div class="chapter"><span class="time">0:00</span> - அறிமுகம்</div>
            <div class="chapter"><span class="time">1:00</span> - வரலாற்றுப் பின்னணி</div>
            <div class="chapter"><span class="time">2:00</span> - இயற்கை சுற்றுலா தலங்கள்</div>
            <div class="chapter"><span class="time">3:00</span> - காலனித்துவ பாரம்பரியம்</div>
            <div class="chapter"><span class="time">4:00</span> - தேயிலைத் தோட்டங்கள்</div>
            <div class="chapter"><span class="time">5:00</span> - பயண குறிப்புகள்</div>
        </div>
        <button onclick="playDemoAudio('ooty', 'ta')">Generate Demo Audio</button>
        <div id="ooty-ta-player"></div>
    </div>
    
    <script>
        // Demo audio content
        const audioContent = {
            'shore-temple': {
                'en': "Welcome to the Shore Temple audio guide. This magnificent temple was built during the Pallava dynasty in the 8th century CE. It stands on the shores of the Bay of Bengal and is one of the oldest structural stone temples in South India. The temple is dedicated to Lord Shiva and features intricate carvings and sculptures that showcase the artistic excellence of the Pallava craftsmen.",
                'ta': "கடற்கரை கோவில் ஒலி வழிகாட்டிக்கு வரவேற்கிறோம். இந்த அற்புதமான கோவில் 8 ஆம் நூற்றாண்டில் பல்லவ வம்சத்தின் போது கட்டப்பட்டது. இது வங்காள விரிகுடாவின் கரையில் நிற்கிறது மற்றும் தென்னிந்தியாவின் மிகப் பழமையான கட்டமைப்பு கல் கோவில்களில் ஒன்றாகும். கோவில் சிவபெருமானுக்கு அர்ப்பணிக்கப்பட்டுள்ளது மற்றும் பல்லவ கைவினைஞர்களின் கலை சிறப்பைக் காட்டும் சிக்கலான செதுக்கல்கள் மற்றும் சிற்பங்களைக் கொண்டுள்ளது."
            },
            'brihadeeswarar-temple': {
                'en': "Welcome to the Brihadeeswarar Temple audio guide. This magnificent temple, also known as Rajarajesvaram or Peruvudaiyar Kovil, is a Hindu temple dedicated to Lord Shiva located in Thanjavur, Tamil Nadu. Built by Raja Raja Chola I between 1003 and 1010 CE, the temple is a part of the UNESCO World Heritage Site known as the Great Living Chola Temples.",
                'ta': "பிரகதீஸ்வரர் கோவில் ஒலி வழிகாட்டிக்கு வரவேற்கிறோம். ராஜராஜேஸ்வரம் அல்லது பெருவுடையார் கோவில் என்றும் அழைக்கப்படும் இந்த அற்புதமான கோவில், தமிழ்நாட்டின் தஞ்சாவூரில் அமைந்துள்ள சிவபெருமானுக்கு அர்ப்பணிக்கப்பட்ட ஒரு இந்து கோவிலாகும். ராஜ ராஜ சோழன் I ஆல் 1003 மற்றும் 1010 CE இடையே கட்டப்பட்ட இந்த கோவில், மகத்தான வாழும் சோழர் கோவில்கள் என்று அழைக்கப்படும் யுனெஸ்கோ உலக பாரம்பரிய தளத்தின் ஒரு பகுதியாகும்."
            },
            'ooty': {
                'en': "Welcome to the Ooty Hill Station audio guide. Ooty, officially known as Udhagamandalam, is a popular hill station located in the Nilgiri Hills of Tamil Nadu. Situated at an altitude of 2,240 meters above sea level, it is often referred to as the 'Queen of Hill Stations' due to its scenic beauty and pleasant climate throughout the year.",
                'ta': "ஊட்டி மலை நிலையம் ஒலி வழிகாட்டிக்கு வரவேற்கிறோம். அதிகாரப்பூர்வமாக உதகமண்டலம் என்று அழைக்கப்படும் ஊட்டி, தமிழ்நாட்டின் நீலகிரி மலைகளில் அமைந்துள்ள ஒரு பிரபலமான மலை நிலையமாகும். கடல் மட்டத்திலிருந்து 2,240 மீட்டர் உயரத்தில் அமைந்துள்ள இது, அதன் இயற்கை அழகு மற்றும் ஆண்டு முழுவதும் இனிமையான காலநிலை காரணமாக 'மலை நிலையங்களின் ராணி' என்று அழைக்கப்படுகிறது."
            }
        };
        
        function playDemoAudio(site, lang) {
            const content = audioContent[site][lang];
            const utterance = new SpeechSynthesisUtterance(content);
            utterance.lang = lang === 'en' ? 'en-US' : 'ta-IN';
            
            // Create audio player
            const playerDiv = document.getElementById(`${site}-${lang}-player`);
            playerDiv.innerHTML = `
                <audio controls>
                    <source src="data:audio/mpeg;base64,AAAA" type="audio/mpeg">
                    Your browser does not support the audio element.
                </audio>
                <p><em>This is a simulated audio player. In a production environment, this would play an actual MP3 file.</em></p>
                <p><strong>Text being spoken:</strong><br>${content}</p>
            `;
            
            speechSynthesis.speak(utterance);
        }
    </script>
</body>
</html>
