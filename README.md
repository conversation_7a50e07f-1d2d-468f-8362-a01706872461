# Heritage Explorer - Authentication Setup Guide

## Overview
Heritage Explorer is a comprehensive web application for discovering Tamil Nadu's rich heritage and culture. This guide will help you set up the authentication system using MongoDB.

## Prerequisites

### 1. MongoDB Installation
- **Download MongoDB Community Server**: https://www.mongodb.com/try/download/community
- **Download MongoDB Compass**: https://www.mongodb.com/try/download/compass
- Install both applications following the setup wizards

### 2. Node.js Installation
- **Download Node.js**: https://nodejs.org/ (LTS version recommended)
- Verify installation: `node --version` and `npm --version`

## Setup Instructions

### Step 1: MongoDB Setup

1. **Start MongoDB Service**:
   ```bash
   # Windows (Run as Administrator)
   net start MongoDB
   
   # Or start manually
   mongod
   ```

2. **Open MongoDB Compass**:
   - Launch MongoDB Compass
   - Connect to: `mongodb://localhost:27017`
   - Create database: `heritage_explorer`

### Step 2: Project Setup

1. **Install Dependencies**:
   ```bash
   npm install
   ```

2. **Environment Configuration**:
   - The `.env` file is already configured with default settings
   - Update if needed:
     ```env
     MONGODB_URI=mongodb://localhost:27017/heritage_explorer
     JWT_SECRET=your_super_secret_jwt_key_change_this_in_production
     PORT=3000
     ```

3. **Initialize Database**:
   ```bash
   node scripts/init-database.js
   ```

### Step 3: Start the Application

1. **Start the Server**:
   ```bash
   npm start
   # or for development with auto-restart
   npm run dev
   ```

2. **Access the Application**:
   - Main Website: http://localhost:3000
   - Login Page: http://localhost:3000/login.html
   - Signup Page: http://localhost:3000/signup.html

## Features

### Authentication System
- ✅ User Registration with validation
- ✅ User Login with JWT tokens
- ✅ Password strength checking
- ✅ Secure password hashing (bcrypt)
- ✅ Session management
- ✅ Protected routes

### User Features
- ✅ User profiles
- ✅ Favorites system
- ✅ Visited sites tracking
- ✅ Personalized preferences
- ✅ Bilingual support (English/Tamil)

### Security Features
- ✅ JWT token authentication
- ✅ Password encryption
- ✅ Input validation
- ✅ Rate limiting
- ✅ CORS protection
- ✅ Helmet security headers

## API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user
- `POST /api/auth/logout` - User logout

### User Management
- `GET /api/user/profile` - Get user profile
- `PUT /api/user/profile` - Update user profile
- `POST /api/user/favorites` - Add to favorites
- `DELETE /api/user/favorites/:siteId` - Remove from favorites
- `GET /api/user/favorites` - Get user favorites
- `POST /api/user/visited` - Add visited site
- `GET /api/user/visited` - Get visited sites

## Database Schema

### Users Collection
```javascript
{
  username: String (unique),
  email: String (unique),
  password: String (hashed),
  firstName: String,
  lastName: String,
  favorites: [{ siteName, siteId, category, addedAt }],
  visitedSites: [{ siteName, siteId, visitedAt, rating, review }],
  preferences: { language, notifications, theme },
  isActive: Boolean,
  lastLogin: Date,
  createdAt: Date,
  updatedAt: Date
}
```

## Testing the Authentication

### 1. Register a New User
- Go to http://localhost:3000/signup.html
- Fill in the registration form
- Password must contain uppercase, lowercase, and number
- Submit to create account

### 2. Login
- Go to http://localhost:3000/login.html
- Use email/username and password
- Successful login redirects to main website

### 3. Test Protected Features
- Add sites to favorites
- Mark sites as visited
- Update profile information

## Default Admin Account
After running the database initialization script, a default admin account is created:
- **Username**: admin
- **Email**: <EMAIL>
- **Password**: Admin123!

## Troubleshooting

### MongoDB Connection Issues
1. Ensure MongoDB service is running
2. Check if port 27017 is available
3. Verify MongoDB URI in .env file

### Authentication Issues
1. Clear browser localStorage
2. Check JWT_SECRET in .env file
3. Verify token expiration settings

### Port Issues
1. Change PORT in .env file if 3000 is occupied
2. Update FRONTEND_URL accordingly

## File Structure
```
heritage-explorer/
├── models/
│   └── User.js              # User database model
├── routes/
│   ├── auth.js              # Authentication routes
│   └── user.js              # User management routes
├── middleware/
│   └── auth.js              # JWT authentication middleware
├── scripts/
│   └── init-database.js     # Database initialization
├── auth.js                  # Client-side authentication
├── login.html               # Login page
├── signup.html              # Signup page
├── server.js                # Main server file
├── package.json             # Dependencies
├── .env                     # Environment variables
└── README.md                # This file
```

## Next Steps
1. Customize the UI/UX as needed
2. Add email verification (optional)
3. Implement password reset functionality
4. Add social login options
5. Deploy to production server

## Support
For issues or questions, please check the troubleshooting section or create an issue in the project repository.
