<?php
// Test database connection
echo "<h2>Heritage Explorer - Database Connection Test</h2>";

// Database configuration
$host = 'localhost';
$port = '3306';
$dbname = 'tourism_db';
$username = 'root';
$password = '<PERSON><PERSON>oj@3010';

echo "<h3>Testing MySQL Connection...</h3>";

try {
    // Test basic MySQL connection (without database)
    echo "1. Testing MySQL server connection...<br>";
    $dsn = "mysql:host=$host;port=$port;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ MySQL server connection successful!<br><br>";
    
    // Check if database exists
    echo "2. Checking if database '$dbname' exists...<br>";
    $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
    $stmt->execute([$dbname]);
    $dbExists = $stmt->fetch();
    
    if ($dbExists) {
        echo "✅ Database '$dbname' exists!<br><br>";
    } else {
        echo "❌ Database '$dbname' does not exist. Creating it...<br>";
        $pdo->exec("CREATE DATABASE $dbname CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "✅ Database '$dbname' created successfully!<br><br>";
    }
    
    // Test connection to the specific database
    echo "3. Testing connection to database '$dbname'...<br>";
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connection successful!<br><br>";
    
    // Check if tables exist
    echo "4. Checking existing tables...<br>";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "❌ No tables found. You need to run the database.sql script.<br>";
        echo "<strong>Next steps:</strong><br>";
        echo "1. Open MySQL Workbench<br>";
        echo "2. Copy and paste the contents of database.sql<br>";
        echo "3. Execute the script<br>";
        echo "4. Refresh this page<br><br>";
    } else {
        echo "✅ Found " . count($tables) . " tables:<br>";
        foreach ($tables as $table) {
            echo "- $table<br>";
        }
        echo "<br>";
        
        // Check if users table has data
        if (in_array('users', $tables)) {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
            $userCount = $stmt->fetch()['count'];
            echo "👥 Users table has $userCount records<br>";
        }
        
        if (in_array('heritage_sites', $tables)) {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM heritage_sites");
            $siteCount = $stmt->fetch()['count'];
            echo "🏛️ Heritage sites table has $siteCount records<br>";
        }
    }
    
    echo "<br><h3>✅ Database setup is ready!</h3>";
    echo "<p><a href='website.html'>Go to Heritage Explorer Website</a></p>";
    
} catch (PDOException $e) {
    echo "<h3>❌ Connection failed!</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    
    echo "<h4>Troubleshooting steps:</h4>";
    echo "<ol>";
    echo "<li>Make sure XAMPP/WAMP is running</li>";
    echo "<li>Check if MySQL service is started</li>";
    echo "<li>Verify the password 'Devmanoj@3010' is correct</li>";
    echo "<li>Try accessing phpMyAdmin at <a href='http://localhost/phpmyadmin'>http://localhost/phpmyadmin</a></li>";
    echo "</ol>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background-color: #f5f5f5;
}

h2 {
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

h3 {
    color: #34495e;
    margin-top: 30px;
}

p, li {
    line-height: 1.6;
}

a {
    color: #3498db;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}

.success {
    color: #27ae60;
}

.error {
    color: #e74c3c;
}
</style>
