<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <!-- SEO Meta Tags -->
  <meta name="description" content="Discover the historic Brihadeeswarar Temple – a UNESCO World Heritage Site renowned for its architectural brilliance and cultural significance." />
  <meta name="keywords" content="Brihadeeswarar Temple, Heritage Center, UNESCO, Thanjavur, Dravidian Architecture, Historical Site" />
  <meta name="author" content="Heritage Explorer" />
  <meta name="robots" content="index, follow" />
  <link rel="canonical" href="https://www.example.com/heritage-center-details.html" />

  <!-- Title & Favicon -->
  <title>Heritage Center Details - Brihadeeswarar Temple</title>
  <link rel="icon" href="favicon.ico" />

  <!-- Open Graph Meta Tags for Social Media -->
  <meta property="og:title" content="Heritage Center: Brihadeeswarar Temple" />
  <meta property="og:description" content="Discover the historic Brihadeeswarar Temple – a UNESCO World Heritage Site renowned for its architectural brilliance and cultural significance." />
  <meta property="og:url" content="https://www.example.com/heritage-center-details.html" />
  <meta property="og:type" content="website" />
  <meta property="og:image" content="https://tse2.mm.bing.net/th?id=OIP.U1BUbQpP1XjOdYLy1rrkmwHaFj&pid=Api&P=0&h=220" />

  <!-- Twitter Card Meta Tags -->
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content="Heritage Center: Brihadeeswarar Temple" />
  <meta name="twitter:description" content="Discover the historic Brihadeeswarar Temple – a UNESCO World Heritage Site renowned for its architectural brilliance and cultural significance." />
  <meta name="twitter:image" content="https://tse2.mm.bing.net/th?id=OIP.U1BUbQpP1XjOdYLy1rrkmwHaFj&pid=Api&P=0&h=220" />

  <!-- Structured Data in JSON-LD -->
  <script type="application/ld+json">
  
  </script>

  <!-- CSS Styles -->
  <style>
    /* CSS Variables for Theme Colors */
    :root {
      --primary-color: #007bff;
      --secondary-color: #0056b3;
      --accent-color: #28a745;
      --accent-hover: #218838;
      --info-color: #17a2b8;
      --info-hover: #138496;
      --light-bg: #f9f9f9;
      --secondary-bg: #e6f0ff;
      --text-color: #333;
      --white: #ffffff;
      --shadow-color: rgba(0, 0, 0, 0.1);
      --shadow-hover: #00000033;
    }
    /* Dark Mode Overrides */
    body.dark-mode {
      --primary-color: #1f1f1f;
      --secondary-color: #272727;
      --accent-color: #03dac6;
      --accent-hover: #02c5b1;
      --info-color: #03dac6;
      --info-hover: #02c5b1;
      --light-bg: #121212;
      --secondary-bg: #1f1f1f;
      --text-color: #e0e0e0;
      --white: #ffffff;
      --shadow-color: rgba(255, 255, 255, 0.1);
      --shadow-hover: rgba(255, 255, 255, 0.2);
    }
    
    /* Global Styles */
    * { box-sizing: border-box; }
    body {
      font-family: 'Arial', sans-serif;
      background: linear-gradient(135deg, var(--light-bg), var(--secondary-bg));
      margin: 0;
      padding: 0;
      color: var(--text-color);
      line-height: 1.6;
      animation: fadeIn 1s ease;
    }
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    a { text-decoration: none; color: inherit; }

    /* Header Styling */
    header {
      background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
      color: var(--white);
      padding: 20px;
      text-align: center;
      font-size: 28px;
      font-weight: bold;
      box-shadow: 0 2px 8px var(--shadow-color);
      position: sticky;
      top: 0;
      z-index: 1000;
      transition: background 0.5s ease;
    }
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      margin-left: 100px;
    }
    .header-content button {
      background-color: var(--accent-color);
      border: none;
      border-radius: 4px;
      padding: 8px 12px;
      font-size: 14px;
      color: var(--white);
      cursor: pointer;
      transition: background-color 0.3s ease, transform 0.2s ease;
    }
    .header-content button:hover {
      background-color: var(--accent-hover);
      transform: scale(1.05);
    }

    /* Heritage Container */
    .heritage-container {
      max-width: 1200px;
      margin: 30px auto;
      background-color: var(--white);
      border-radius: 12px;
      box-shadow: 0 6px 12px var(--shadow-color);
      overflow: hidden;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .heritage-container:hover {
      transform: scale(1.02);
      box-shadow: 0 8px 16px var(--shadow-hover);
    }

    /* Carousel Section */
    .carousel-wrapper {
      overflow: hidden;
      position: relative;
      width: 100%;
    }
    .image-carousel {
      display: flex;
      gap: 15px;
      padding: 15px;
      background-color: #f1f1f1;
      width: calc(400px * 8);
      animation: slideAnimation 30s linear infinite;
    }
    .image-carousel img {
      flex-shrink: 0;
      width: 100%;
      max-width: 400px;
      height: auto;
      border-radius: 8px;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .image-carousel:hover { animation-play-state: paused; }
    .image-carousel img:hover {
      transform: scale(1.05);
      box-shadow: 0 4px 10px var(--shadow-hover);
    }
    @keyframes slideAnimation {
      0% { transform: translateX(0); }
      100% { transform: translateX(-50%); }
    }

    /* Main Content Section */
    main { padding: 25px 30px; }
    main h2 {
      font-size: 30px;
      color: #222;
      margin-bottom: 15px;
      border-bottom: 2px solid var(--primary-color);
      display: inline-block;
      padding-bottom: 5px;
    }
    .description-text {
      font-size: 17px;
      color: #555;
      margin-bottom: 15px;
      text-align: justify;
      line-height: 1.8;
    }

    /* Button Styling */
    .btn-container {
      margin-top: 25px;
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
    }
    button {
      padding: 12px 18px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 16px;
      transition: background-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
    }
    button:focus { outline: none; }
    .view-on-map-btn {
      background-color: var(--accent-color);
      color: var(--white);
    }
    .view-on-map-btn:hover {
      background-color: var(--accent-hover);
      box-shadow: 0 3px 8px rgba(40, 167, 69, 0.5);
    }
    .read-description-btn {
      background-color: var(--info-color);
      color: var(--white);
    }
    .read-description-btn:hover {
      background-color: var(--info-hover);
      box-shadow: 0 3px 8px rgba(23, 162, 184, 0.5);
    }
    .virtual-tour-btn {
      background-color: var(--primary-color);
      color: var(--white);
    }
    .virtual-tour-btn:hover {
      background-color: var(--secondary-color);
      box-shadow: 0 3px 8px rgba(0, 123, 255, 0.5);
    }

    /* Footer Styling */
    footer {
      text-align: center;
      padding: 20px 0;
      background-color: var(--primary-color);
      font-size: 15px;
      color: var(--white);
      border-top: 3px solid var(--secondary-color);
      margin-top: 30px;
      transition: background-color 0.3s ease;
    }

    /* Virtual Tour Modal */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.6);
      z-index: 900;
      display: none;
      animation: fadeInOverlay 0.5s ease;
    }
    @keyframes fadeInOverlay {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    .modal {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: var(--white);
      padding: 2rem;
      border-radius: 10px;
      box-shadow: 0 8px 16px var(--shadow-hover);
      width: 90%;
      max-width: 500px;
      z-index: 1000;
      display: none;
      animation: fadeIn 0.5s ease;
    }
    .modal h2 {
      margin-bottom: 1rem;
      font-size: 2rem;
      color: var(--primary-color);
    }
    .modal p {
      margin-bottom: 1.5rem;
      color: #555;
      font-size: 1rem;
    }
    .modal button {
      background: var(--primary-color);
      color: var(--white);
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 25px;
      cursor: pointer;
      font-weight: 500;
      transition: background-color 0.3s ease;
      margin-right: 1rem;
    }
    .modal button:hover {
      background-color: var(--secondary-color);
    }

    /* Live Chat Support Widget */
    #liveChatWidget {
      position: fixed;
      bottom: 20px;
      left: 20px;
      width: 320px;
      max-height: 400px;
      background: var(--white);
      border-radius: 10px;
      box-shadow: 0 8px 16px var(--shadow-hover);
      display: none;
      flex-direction: column;
      overflow: hidden;
      z-index: 1100;
      animation: fadeIn 0.5s ease;
    }
    #liveChatHeader {
      background: var(--primary-color);
      color: var(--white);
      padding: 0.75rem;
      text-align: center;
      font-size: 1.2rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    #liveChatHeader span {
      cursor: pointer;
      font-size: 1.5rem;
    }
    #liveChatMessages {
      padding: 1rem;
      overflow-y: auto;
      flex-grow: 1;
      background: var(--secondary-bg);
    }
    #liveChatMessages div {
      margin-bottom: 0.75rem;
      padding: 0.5rem 0.75rem;
      border-radius: 20px;
      max-width: 80%;
      word-wrap: break-word;
      background: #f1f1f1;
    }
    #liveChatMessages div.user {
      background: var(--accent-color);
      color: var(--white);
      margin-left: auto;
      text-align: right;
    }
    #liveChatMessages div.bot {
      background: var(--info-color);
      color: var(--white);
      margin-right: auto;
      text-align: left;
    }
    #liveChatInputContainer {
      display: flex;
      border-top: 1px solid #ddd;
    }
    #liveChatInput {
      flex: 1;
      padding: 0.75rem;
      border: none;
      font-size: 1rem;
      outline: none;
    }
    #liveChatSendButton {
      background: var(--primary-color);
      color: var(--white);
      border: none;
      padding: 0 1rem;
      cursor: pointer;
      font-size: 1rem;
      transition: background-color 0.3s ease;
    }
    #liveChatSendButton:hover {
      background-color: var(--secondary-color);
    }

    /* Back-to-Top Button */
    #backToTop {
      position: fixed;
      bottom: 90px;
      right: 20px;
      background: var(--primary-color);
      color: var(--white);
      border: none;
      border-radius: 50%;
      width: 45px;
      height: 45px;
      cursor: pointer;
      font-size: 1.2rem;
      display: none;
      justify-content: center;
      align-items: center;
      box-shadow: 0 4px 8px var(--shadow-hover);
      transition: background-color 0.3s ease;
    }
    #backToTop:hover {
      background-color: var(--secondary-color);
    }
    
    /* Responsive Design */
    @media (max-width: 768px) {
      .image-carousel {
        width: auto;
        animation: none;
        overflow-x: auto;
      }
      .image-carousel img {
        max-width: 300px;
      }
      main { padding: 20px; }
      #liveChatWidget { width: 90%; left: 5%; }
    }
    #backToDashboard {
      position: fixed;
      top: 15px;
      left: 15px;
      background: var(--primary-color);
      color: var(--white);
      border: none;
      padding: 10px 15px;
      font-size: 16px;
      cursor: pointer;
      border-radius: 5px;
      transition: background 0.3s ease;
    }

    #backToDashboard:hover {
      background: var(--secondary-color);
    }

    </style>
  </head>
  <body>
    <!-- Header with Dark Mode Toggle -->
    <header role="banner">
    <div class="header-content">
      <button 
      id="backToDashboard" 
      aria-label="Back to Dashboard" 
      onclick="window.location.href='website.html';">&#8592; Back
      </button>
      <span>Heritage Center: Brihadeeswarar Temple</span>
      <button id="darkModeToggle" aria-label="Toggle Dark Mode">Toggle Dark Mode</button>
    </div>
    </header>
  
  
  <div class="heritage-container">
    <!-- Image Carousel -->
    <section class="carousel-wrapper" aria-label="Image Carousel showcasing Brihadeeswarar Temple">
      <div class="image-carousel">
        <img src="https://tse2.mm.bing.net/th?id=OIP.U1BUbQpP1XjOdYLy1rrkmwHaFj&pid=Api&P=0&h=220" alt="Brihadeeswarar Temple view 1" />
        <img src="data:image/jpeg;base64,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" alt="Brihadeeswarar Temple view 2" />
        <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQPWOf3G95cHmI26iX0C-n9_-TaIX8xgBeScA&s" alt="Brihadeeswarar Temple view 3" />
        <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRbI4JWc197eJhO5IOF1FyrWnePjpcsYj8h2w&s" alt="Brihadeeswarar Temple view 4" />
        <!-- Duplicate images for seamless looping -->
        <img src="https://tse2.mm.bing.net/th?id=OIP.U1BUbQpP1XjOdYLy1rrkmwHaFj&pid=Api&P=0&h=220" alt="Brihadeeswarar Temple view 1 duplicate" />
        <img src="data:image/jpeg;base64,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" alt="Brihadeeswarar Temple view 2 duplicate" />
        <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQPWOf3G95cHmI26iX0C-n9_-TaIX8xgBeScA&s" alt="Brihadeeswarar Temple view 3 duplicate" />
        <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRbI4JWc197eJhO5IOF1FyrWnePjpcsYj8h2w&s" alt="Brihadeeswarar Temple view 4 duplicate" />
      </div>
    </section>
    
    <!-- Main Content -->
    <main role="main">
      <h2>Brihadeeswarar Temple</h2>
      <p class="description-text">
        The Brihadeeswarar Temple, also known as the "Big Temple," is a UNESCO World Heritage Site located in Thanjavur, Tamil Nadu. Built during the Chola dynasty in the 11th century by King Raja Raja Chola I, this architectural marvel is a testament to the grandeur and innovation of Dravidian architecture.
      </p>
      <p class="description-text">
        The temple is renowned for its massive dome, constructed from a single granite block weighing approximately 80 tons. Intricate carvings and exquisite sculptures adorn its walls, depicting scenes from Hindu mythology and Chola history. Its towering vimana stands at a remarkable 216 feet, making it one of the tallest structures of its kind.
      </p>
      <p class="description-text">
        Beyond its architectural splendor, the temple continues to serve as a vibrant center of culture and spirituality, attracting thousands of visitors and pilgrims each year. Its inscriptions provide invaluable insights into the history, art, and administration of the Chola era.
      </p>
      
      <!-- Button Container -->
      <div class="btn-container">
        <button id="viewOnMapBtn" class="view-on-map-btn" aria-label="View location on Google Maps">View on Map</button>
        <button id="readDescriptionBtn" class="read-description-btn" aria-label="Read the description aloud">Read Description</button>
        <button id="virtualTourBtn" class="virtual-tour-btn" aria-label="Take a Virtual Tour">Virtual Tour</button>
      </div>
    </main>
  </div>
  
  <!-- Footer -->
  <footer role="contentinfo">© 2025 Heritage Explorer. All rights reserved.</footer>
  
  <!-- Virtual Tour Modal -->
  <div class="modal-overlay" id="virtualTourOverlay"></div>
  <div class="modal" id="virtualTourModal" role="dialog" aria-modal="true" aria-labelledby="virtualTourModalTitle">
    <h2 id="virtualTourModalTitle">Virtual Tour</h2>
    <iframe width="100%" height="315" src="https://www.youtube.com/embed/GJ-sB3tDAcw" title="Virtual Tour of Brihadeeswarar Temple" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
    <button id="closeVirtualTourModalButton">Close</button>
  </div>
  
  <!-- Live Chat Support Widget (New Important Feature) -->
  <div id="liveChatWidget">
    <div id="liveChatHeader">
      <span>Live Support Chat</span>
      <span id="closeChatWidget">&times;</span>
    </div>
    <div id="liveChatMessages"></div>
    <div id="liveChatInputContainer">
      <input type="text" id="liveChatInput" placeholder="Type your message..." />
      <button id="liveChatSendButton">Send</button>
    </div>
  </div>
  
  <!-- Back-to-Top Button -->
  <button id="backToTop">&#8679;</button>
  
  <!-- JavaScript Enhancements -->
  <script>
    /***** Dark Mode Toggle Feature *****/
    if (localStorage.getItem("darkMode") === "enabled") {
      document.body.classList.add("dark-mode");
    }
    document.getElementById("darkModeToggle").addEventListener("click", function() {
      document.body.classList.toggle("dark-mode");
      if(document.body.classList.contains("dark-mode")){
          localStorage.setItem("darkMode", "enabled");
      } else {
          localStorage.setItem("darkMode", "disabled");
      }
    });

    /***** Button Enhancements *****/
    document.getElementById('viewOnMapBtn').addEventListener('click', function() {
      const lat = 10.7820;
      const lng = 79.1319;
      const url = `https://www.google.com/maps?q=${lat},${lng}`;
      window.open(url, '_blank');
    });
    
    const readDescriptionBtn = document.getElementById('readDescriptionBtn');
    let isReading = false;
    let speechUtterance;
    readDescriptionBtn.addEventListener('click', function() {
      if (isReading) {
        speechSynthesis.cancel();
        isReading = false;
        readDescriptionBtn.textContent = 'Read Description';
        return;
      }
      const descriptions = document.querySelectorAll('.description-text');
      const text = Array.from(descriptions).map(desc => desc.textContent).join(' ');
      speechUtterance = new SpeechSynthesisUtterance(text);
      speechUtterance.lang = 'en-US';
      speechSynthesis.speak(speechUtterance);
      isReading = true;
      readDescriptionBtn.textContent = 'Stop Reading';
      speechUtterance.onend = () => {
        isReading = false;
        readDescriptionBtn.textContent = 'Read Description';
      };
    });
    
    document.getElementById('virtualTourBtn').addEventListener('click', function() {
      modalToggle("virtualTourModal", true);
    });
    document.getElementById('closeVirtualTourModalButton').addEventListener('click', function() {
      modalToggle("virtualTourModal", false);
    });
    
    /***** Modal Toggle Function *****/
    function modalToggle(modalId, state) {
      const modals = {
        siteModal: [document.getElementById("siteModal"), document.getElementById("modalOverlay")],
        nearbyModal: [document.getElementById("nearbyModal"), document.getElementById("nearbyOverlay")],
        favoritesModal: [document.getElementById("favoritesModal"), document.getElementById("favoritesOverlay")],
        reviewsModal: [document.getElementById("reviewsModal"), document.getElementById("reviewsOverlay")],
        virtualTourModal: [document.getElementById("virtualTourModal"), document.getElementById("virtualTourOverlay")]
      };
      const [modalElement, overlayElement] = modals[modalId];
      modalElement.style.display = state ? "block" : "none";
      overlayElement.style.display = state ? "block" : "none";
    }
    document.getElementById("modalOverlay")?.addEventListener("click", () => modalToggle("siteModal", false));
    document.getElementById("nearbyOverlay")?.addEventListener("click", () => modalToggle("nearbyModal", false));
    document.getElementById("favoritesOverlay")?.addEventListener("click", () => modalToggle("favoritesModal", false));
    document.getElementById("reviewsOverlay")?.addEventListener("click", () => modalToggle("reviewsModal", false));
    document.getElementById("virtualTourOverlay").addEventListener("click", () => modalToggle("virtualTourModal", false));
    
    /***** Additional Important Feature: Live Chat Support Widget *****/
    // Show the live chat widget after a 5-second delay.
    setTimeout(() => {
      const liveChatWidget = document.getElementById("liveChatWidget");
      liveChatWidget.style.display = "flex";
    }, 5000);
    
    // Live Chat Widget functionality
    const liveChatWidget = document.getElementById("liveChatWidget");
    const liveChatHeader = document.getElementById("liveChatHeader");
    const liveChatMessages = document.getElementById("liveChatMessages");
    const liveChatInput = document.getElementById("liveChatInput");
    const liveChatSendButton = document.getElementById("liveChatSendButton");
    document.getElementById("closeChatWidget").addEventListener("click", () => {
      liveChatWidget.style.display = "none";
    });
    liveChatSendButton.addEventListener("click", () => {
      const message = liveChatInput.value.trim();
      if(message !== "") {
        const messageDiv = document.createElement("div");
        messageDiv.textContent = message;
        messageDiv.classList.add("user");
        messageDiv.style.textAlign = "right";
        messageDiv.style.marginBottom = "8px";
        liveChatMessages.appendChild(messageDiv);
        liveChatInput.value = "";
        liveChatMessages.scrollTop = liveChatMessages.scrollHeight;
        // Simulate a bot response after 500ms
        setTimeout(() => {
          const botMessageDiv = document.createElement("div");
          botMessageDiv.textContent = "Thank you for reaching out. How can I help you today?";
          botMessageDiv.classList.add("bot");
          botMessageDiv.style.textAlign = "left";
          botMessageDiv.style.marginBottom = "8px";
          liveChatMessages.appendChild(botMessageDiv);
          liveChatMessages.scrollTop = liveChatMessages.scrollHeight;
        }, 500);
      }
    });
    
    /***** Back-to-Top Button *****/
    const backToTopButton = document.getElementById("backToTop");
    window.addEventListener("scroll", () => {
      backToTopButton.style.display = window.scrollY > 300 ? "flex" : "none";
    });
    backToTopButton.addEventListener("click", () => {
      window.scrollTo({ top: 0, behavior: "smooth" });
    });
    
    /***** Navigation Function *****/
    function navigateToDetails(siteName) {
      trackEvent("navigate_to_details", { site: siteName });
      const detailsPage = detailsPageMapping[siteName];
      if (detailsPage) {
        window.location.href = detailsPage;
      } else {
        window.location.href =`site-details.html?name=${encodeURIComponent(siteName)}`;
      }
    }
    
    /***** Render Sites (Assuming renderSites and currentLanguage are defined elsewhere) *****/
    const currentLanguage = 'en'; // Define the current language
    function renderSites(language) {
      // Add your implementation for rendering sites based on the language
      console.log(`Rendering sites in ${language}`);
    }
    renderSites(currentLanguage);
  </script>
</body>
</html>