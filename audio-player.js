/**
 * Heritage Explorer - Multilingual Audio Guide Player
 * This script handles the audio guide functionality with multilingual support
 */

class AudioGuidePlayer {
  constructor() {
    this.audioElement = null;
    this.currentSite = '';
    this.currentLanguage = 'en'; // Default language
    this.isPlaying = false;
    this.duration = 0;
    this.currentTime = 0;
    this.chapters = [];
    this.currentChapterIndex = 0;
    
    // UI Elements (will be initialized when setupPlayer is called)
    this.playerContainer = null;
    this.playPauseBtn = null;
    this.progressBar = null;
    this.currentTimeDisplay = null;
    this.durationDisplay = null;
    this.languageSelector = null;
    this.chapterSelector = null;
    this.volumeControl = null;
    this.playbackRateControl = null;
    this.downloadBtn = null;
  }

  /**
   * Initialize the audio player for a specific site
   * @param {string} siteId - The ID of the heritage site
   * @param {Array} chapters - Array of chapter objects with {title, startTime}
   */
  setupPlayer(siteId, chapters = []) {
    this.currentSite = siteId;
    this.chapters = chapters;
    
    // Create audio element if it doesn't exist
    if (!this.audioElement) {
      this.audioElement = document.createElement('audio');
      this.audioElement.id = 'audioGuide';
      document.body.appendChild(this.audioElement);
      
      // Set up event listeners for the audio element
      this.audioElement.addEventListener('loadedmetadata', () => this.onAudioLoaded());
      this.audioElement.addEventListener('timeupdate', () => this.onTimeUpdate());
      this.audioElement.addEventListener('ended', () => this.onAudioEnded());
      this.audioElement.addEventListener('error', (e) => this.onAudioError(e));
    }
    
    // Load the audio file for the current site and language
    this.loadAudio();
    
    // Create or update the player UI
    this.createPlayerUI();
  }

  /**
   * Load the audio file based on current site and language
   */
  loadAudio() {
    const audioPath = `audio-guides/${this.currentLanguage}/${this.currentSite}.mp3`;
    this.audioElement.src = audioPath;
    this.audioElement.load();
  }

  /**
   * Create the player UI elements
   */
  createPlayerUI() {
    // Check if player container already exists
    let container = document.getElementById('audioGuidePlayer');
    
    if (!container) {
      // Create new player container
      container = document.createElement('div');
      container.id = 'audioGuidePlayer';
      container.className = 'audio-guide-player';
      
      // Add the container to the page
      const targetElement = document.querySelector('.site-info');
      if (targetElement) {
        targetElement.appendChild(container);
      } else {
        document.body.appendChild(container);
      }
    }
    
    this.playerContainer = container;
    
    // Create the player HTML structure
    container.innerHTML = `
      <div class="audio-player-header">
        <h3><i class="fas fa-headphones"></i> Audio Guide</h3>
        <div class="language-controls">
          <select id="audioLanguageSelector" aria-label="Select language">
            <option value="en">English</option>
            <option value="ta">தமிழ்</option>
          </select>
        </div>
      </div>
      <div class="audio-player-controls">
        <button id="playPauseBtn" aria-label="Play">
          <i class="fas fa-play"></i>
        </button>
        <div class="progress-container">
          <div class="time-display" id="currentTime">0:00</div>
          <div class="progress-bar-container">
            <div class="progress-bar">
              <div class="progress-bar-fill" id="progressBarFill"></div>
            </div>
          </div>
          <div class="time-display" id="duration">0:00</div>
        </div>
      </div>
      <div class="audio-player-options">
        <div class="chapters-container">
          <label for="chapterSelector">Chapters:</label>
          <select id="chapterSelector" aria-label="Select chapter">
            ${this.chapters.map((chapter, index) => 
              `<option value="${index}">${chapter.title}</option>`
            ).join('')}
          </select>
        </div>
        <div class="audio-controls">
          <div class="volume-control">
            <i class="fas fa-volume-up"></i>
            <input type="range" id="volumeControl" min="0" max="1" step="0.1" value="1" aria-label="Volume">
          </div>
          <div class="playback-rate">
            <label for="playbackRateControl">Speed:</label>
            <select id="playbackRateControl" aria-label="Playback speed">
              <option value="0.75">0.75x</option>
              <option value="1" selected>1x</option>
              <option value="1.25">1.25x</option>
              <option value="1.5">1.5x</option>
              <option value="2">2x</option>
            </select>
          </div>
        </div>
        <button id="downloadBtn" class="download-btn" aria-label="Download audio guide">
          <i class="fas fa-download"></i> Download
        </button>
      </div>
    `;
    
    // Initialize UI element references
    this.playPauseBtn = document.getElementById('playPauseBtn');
    this.progressBarFill = document.getElementById('progressBarFill');
    this.currentTimeDisplay = document.getElementById('currentTime');
    this.durationDisplay = document.getElementById('duration');
    this.languageSelector = document.getElementById('audioLanguageSelector');
    this.chapterSelector = document.getElementById('chapterSelector');
    this.volumeControl = document.getElementById('volumeControl');
    this.playbackRateControl = document.getElementById('playbackRateControl');
    this.downloadBtn = document.getElementById('downloadBtn');
    
    // Set the current language in the selector
    this.languageSelector.value = this.currentLanguage;
    
    // Add event listeners to UI elements
    this.addEventListeners();
    
    // Add styles for the player
    this.addPlayerStyles();
  }

  /**
   * Add event listeners to player UI elements
   */
  addEventListeners() {
    // Play/Pause button
    this.playPauseBtn.addEventListener('click', () => this.togglePlayPause());
    
    // Progress bar click
    const progressBar = document.querySelector('.progress-bar');
    progressBar.addEventListener('click', (e) => this.onProgressBarClick(e));
    
    // Language selector
    this.languageSelector.addEventListener('change', () => {
      this.currentLanguage = this.languageSelector.value;
      const wasPlaying = this.isPlaying;
      this.currentTime = this.audioElement.currentTime; // Save current position
      this.loadAudio();
      if (wasPlaying) {
        this.audioElement.play().catch(e => console.error('Error playing audio:', e));
      }
    });
    
    // Chapter selector
    this.chapterSelector.addEventListener('change', () => {
      const chapterIndex = parseInt(this.chapterSelector.value);
      this.skipToChapter(chapterIndex);
    });
    
    // Volume control
    this.volumeControl.addEventListener('input', () => {
      this.audioElement.volume = this.volumeControl.value;
    });
    
    // Playback rate control
    this.playbackRateControl.addEventListener('change', () => {
      this.audioElement.playbackRate = parseFloat(this.playbackRateControl.value);
    });
    
    // Download button
    this.downloadBtn.addEventListener('click', () => this.downloadAudio());
  }

  /**
   * Add CSS styles for the audio player
   */
  addPlayerStyles() {
    // Check if styles already exist
    if (document.getElementById('audioPlayerStyles')) return;
    
    const styleElement = document.createElement('style');
    styleElement.id = 'audioPlayerStyles';
    styleElement.textContent = `
      .audio-guide-player {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin: 1.5rem 0;
        padding: 1rem;
        font-family: 'Poppins', sans-serif;
      }
      
      .audio-player-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
      }
      
      .audio-player-header h3 {
        margin: 0;
        color: var(--primary-color, #e63946);
        display: flex;
        align-items: center;
      }
      
      .audio-player-header h3 i {
        margin-right: 0.5rem;
      }
      
      .language-controls select {
        padding: 0.5rem;
        border-radius: 4px;
        border: 1px solid var(--gray-300, #dee2e6);
        background-color: white;
        font-family: inherit;
      }
      
      .audio-player-controls {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
      }
      
      #playPauseBtn {
        background-color: var(--primary-color, #e63946);
        color: white;
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.2s;
      }
      
      #playPauseBtn:hover {
        background-color: var(--accent-color, #1d3557);
      }
      
      .progress-container {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
      
      .time-display {
        font-size: 0.8rem;
        color: var(--gray-700, #495057);
        min-width: 40px;
      }
      
      .progress-bar-container {
        flex: 1;
      }
      
      .progress-bar {
        height: 6px;
        background-color: var(--gray-200, #e9ecef);
        border-radius: 3px;
        cursor: pointer;
        position: relative;
      }
      
      .progress-bar-fill {
        height: 100%;
        background-color: var(--secondary-color, #457b9d);
        border-radius: 3px;
        width: 0%;
        transition: width 0.1s linear;
      }
      
      .audio-player-options {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
      }
      
      .chapters-container {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
      
      .chapters-container select {
        padding: 0.5rem;
        border-radius: 4px;
        border: 1px solid var(--gray-300, #dee2e6);
        background-color: white;
        font-family: inherit;
      }
      
      .audio-controls {
        display: flex;
        gap: 1rem;
        align-items: center;
      }
      
      .volume-control {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
      
      .volume-control i {
        color: var(--gray-600, #6c757d);
      }
      
      .volume-control input {
        width: 80px;
      }
      
      .playback-rate {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
      
      .playback-rate select {
        padding: 0.25rem;
        border-radius: 4px;
        border: 1px solid var(--gray-300, #dee2e6);
        background-color: white;
        font-family: inherit;
      }
      
      .download-btn {
        background-color: var(--gray-200, #e9ecef);
        border: none;
        border-radius: 4px;
        padding: 0.5rem 1rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: background-color 0.2s;
      }
      
      .download-btn:hover {
        background-color: var(--gray-300, #dee2e6);
      }
      
      @media (max-width: 768px) {
        .audio-player-options {
          flex-direction: column;
          align-items: flex-start;
        }
        
        .audio-controls {
          width: 100%;
          justify-content: space-between;
        }
        
        .download-btn {
          align-self: flex-end;
        }
      }
    `;
    
    document.head.appendChild(styleElement);
  }

  /**
   * Toggle play/pause state
   */
  togglePlayPause() {
    if (this.isPlaying) {
      this.audioElement.pause();
      this.playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
      this.playPauseBtn.setAttribute('aria-label', 'Play');
    } else {
      this.audioElement.play().catch(e => console.error('Error playing audio:', e));
      this.playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
      this.playPauseBtn.setAttribute('aria-label', 'Pause');
    }
    
    this.isPlaying = !this.isPlaying;
  }

  /**
   * Handle progress bar click to seek
   */
  onProgressBarClick(e) {
    const progressBar = document.querySelector('.progress-bar');
    const rect = progressBar.getBoundingClientRect();
    const clickPosition = (e.clientX - rect.left) / rect.width;
    
    // Set the current time based on click position
    this.audioElement.currentTime = this.audioElement.duration * clickPosition;
  }

  /**
   * Skip to a specific chapter
   */
  skipToChapter(chapterIndex) {
    if (chapterIndex >= 0 && chapterIndex < this.chapters.length) {
      this.currentChapterIndex = chapterIndex;
      this.audioElement.currentTime = this.chapters[chapterIndex].startTime;
      
      // If not playing, start playing
      if (!this.isPlaying) {
        this.togglePlayPause();
      }
    }
  }

  /**
   * Download the current audio guide
   */
  downloadAudio() {
    const link = document.createElement('a');
    link.href = this.audioElement.src;
    link.download = `${this.currentSite}_audio_guide_${this.currentLanguage}.mp3`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  /**
   * Handle audio loaded event
   */
  onAudioLoaded() {
    this.duration = this.audioElement.duration;
    this.durationDisplay.textContent = this.formatTime(this.duration);
    
    // Try to restore previous position
    if (this.currentTime > 0 && this.currentTime < this.duration) {
      this.audioElement.currentTime = this.currentTime;
    }
  }

  /**
   * Handle time update event
   */
  onTimeUpdate() {
    this.currentTime = this.audioElement.currentTime;
    this.currentTimeDisplay.textContent = this.formatTime(this.currentTime);
    
    // Update progress bar
    const progress = (this.currentTime / this.duration) * 100;
    this.progressBarFill.style.width = `${progress}%`;
    
    // Update current chapter if needed
    this.updateCurrentChapter();
  }

  /**
   * Update the current chapter based on playback position
   */
  updateCurrentChapter() {
    if (this.chapters.length === 0) return;
    
    // Find the current chapter based on time
    for (let i = this.chapters.length - 1; i >= 0; i--) {
      if (this.currentTime >= this.chapters[i].startTime) {
        if (this.currentChapterIndex !== i) {
          this.currentChapterIndex = i;
          this.chapterSelector.value = i;
        }
        break;
      }
    }
  }

  /**
   * Handle audio ended event
   */
  onAudioEnded() {
    this.isPlaying = false;
    this.playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
    this.playPauseBtn.setAttribute('aria-label', 'Play');
    this.progressBarFill.style.width = '0%';
    this.audioElement.currentTime = 0;
    this.currentTime = 0;
    this.currentTimeDisplay.textContent = this.formatTime(0);
  }

  /**
   * Handle audio error event
   */
  onAudioError(e) {
    console.error('Audio error:', e);
    alert(`Error loading audio guide. Please try again later.`);
  }

  /**
   * Format time in seconds to MM:SS format
   */
  formatTime(timeInSeconds) {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}

// Create a global instance of the audio player
const audioGuidePlayer = new AudioGuidePlayer();

// Function to initialize the audio player on a page
function initAudioGuide(siteId, chapters = []) {
  // Wait for DOM to be fully loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      audioGuidePlayer.setupPlayer(siteId, chapters);
    });
  } else {
    audioGuidePlayer.setupPlayer(siteId, chapters);
  }
}
