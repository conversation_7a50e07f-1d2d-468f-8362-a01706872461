@echo off
color 0B
title Heritage Explorer - Project Launcher
echo.
echo ==========================================
echo    🏛️  HERITAGE EXPLORER PROJECT LAUNCHER
echo ==========================================
echo.

:: Check if XAMPP is installed
echo [1/6] Checking XAMPP installation...
if exist "C:\xampp\xampp-control.exe" (
    echo ✅ XAMPP found!
) else (
    echo ❌ XAMPP not found!
    echo.
    echo Installing XAMPP is required to run this project.
    echo Opening download page...
    start https://www.apachefriends.org/download.html
    echo.
    echo Please install XAMPP and run this script again.
    pause
    exit /b 1
)

:: Create project directory
echo [2/6] Setting up project directory...
if not exist "C:\xampp\htdocs\heritage-explorer" (
    mkdir "C:\xampp\htdocs\heritage-explorer"
    echo ✅ Created project directory
) else (
    echo ✅ Project directory exists
)

:: Check if files are copied
echo [3/6] Checking project files...
if exist "C:\xampp\htdocs\heritage-explorer\website.html" (
    echo ✅ Project files found
) else (
    echo ⚠️  Project files not found in XAMPP directory
    echo.
    echo Opening both folders for you to copy files:
    echo - Source: Your current project folder
    echo - Destination: C:\xampp\htdocs\heritage-explorer\
    echo.
    echo Please copy ALL files from source to destination folder.
    echo.
    start explorer "%~dp0"
    start explorer "C:\xampp\htdocs\heritage-explorer"
    echo.
    echo After copying files, press any key to continue...
    pause > nul
)

:: Start XAMPP services
echo [4/6] Starting XAMPP services...
echo.
echo Starting XAMPP Control Panel...
start "" "C:\xampp\xampp-control.exe"
echo.
echo ⚠️  IMPORTANT: In XAMPP Control Panel, click START for:
echo    - Apache (must show green "Running")
echo    - MySQL (must show green "Running")
echo.
echo Press any key after starting both services...
pause > nul

:: Setup database
echo [5/6] Setting up database...
echo.
echo Opening database setup page...
timeout /t 2 > nul
start http://localhost/heritage-explorer/setup-database.php
echo.
echo ⚠️  Wait for "Setup completed successfully!" message
echo    then press any key to continue...
pause > nul

:: Launch website
echo [6/6] Launching Heritage Explorer website...
echo.
echo 🎉 Opening Heritage Explorer in your browser...
timeout /t 2 > nul
start http://localhost/heritage-explorer/website.html
echo.
echo ==========================================
echo    🎉 PROJECT LAUNCHED SUCCESSFULLY!
echo ==========================================
echo.
echo Your Heritage Explorer website is now running at:
echo 👉 http://localhost/heritage-explorer/website.html
echo.
echo Available pages:
echo 🏠 Main Website: http://localhost/heritage-explorer/website.html
echo 🔐 Login Page:   http://localhost/heritage-explorer/login.html
echo 📝 Signup Page:  http://localhost/heritage-explorer/signup.html
echo 🗄️  Database Test: http://localhost/heritage-explorer/test-connection.php
echo.
echo Default admin login:
echo 👤 Username: admin
echo 🔑 Password: admin123
echo.
echo ⚠️  REMEMBER: Always use http://localhost/heritage-explorer/
echo    Never use 127.0.0.1:5500 or other ports!
echo.
echo Press any key to exit...
pause > nul
