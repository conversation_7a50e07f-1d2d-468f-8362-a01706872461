<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Ooty Hill Station - Heritage Explorer</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
  <style>
    :root {
      --primary-color: #1a237e;
      --secondary-color: #457b9d;
      --accent-color: #e63946;
      --light-color: #f1faee;
      --dark-color: #1d3557;
      --success-color: #2e7d32;
      --warning-color: #ff9800;
      --danger-color: #c62828;
      --gray-100: #f8f9fa;
      --gray-200: #e9ecef;
      --gray-300: #dee2e6;
      --gray-400: #ced4da;
      --gray-500: #adb5bd;
      --gray-600: #6c757d;
      --gray-700: #495057;
      --gray-800: #343a40;
      --gray-900: #212529;
      --border-radius: 0.5rem;
      --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
      --transition: all 0.3s ease-in-out;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: var(--gray-800);
      background-color: var(--gray-100);
    }

    header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      text-align: center;
    }

    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      background-color: white;
      box-shadow: var(--box-shadow);
    }

    nav a {
      color: var(--gray-700);
      text-decoration: none;
      margin: 0 1rem;
      font-weight: 500;
      transition: var(--transition);
    }

    nav a:hover {
      color: var(--primary-color);
    }

    main {
      max-width: 1200px;
      margin: 2rem auto;
      padding: 0 1rem;
    }

    .site-details {
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    @media (min-width: 768px) {
      .site-details {
        grid-template-columns: 1fr 1fr;
      }
    }

    .site-image {
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
    }

    .site-image img {
      width: 100%;
      height: auto;
      display: block;
    }

    .site-info h1 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .site-info p {
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }

    .site-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .meta-item {
      display: flex;
      align-items: center;
      background-color: var(--gray-200);
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
    }

    .meta-item i {
      margin-right: 0.5rem;
      color: var(--secondary-color);
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      margin-top: 1.5rem;
    }

    .action-buttons button {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      background-color: var(--secondary-color);
      color: white;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
      display: flex;
      align-items: center;
    }

    .action-buttons button i {
      margin-right: 0.5rem;
    }

    .action-buttons button:hover {
      background-color: var(--primary-color);
    }

    .site-description {
      margin-bottom: 2rem;
    }

    .site-description h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .map-container {
      height: 400px;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      margin-bottom: 2rem;
    }

    .nearby-attractions {
      margin-bottom: 2rem;
    }

    .nearby-attractions h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .attractions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .attraction-card {
      background-color: white;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
    }

    .attraction-card:hover {
      transform: translateY(-5px);
    }

    .attraction-card img {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }

    .attraction-card-content {
      padding: 1rem;
    }

    .attraction-card-content h3 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    footer {
      background-color: var(--primary-color);
      color: white;
      text-align: center;
      padding: 1rem;
      margin-top: 2rem;
    }

    #backToTop {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      width: 3rem;
      height: 3rem;
      background-color: var(--primary-color);
      color: white;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
      display: none;
    }

    #backToTop:hover {
      background-color: var(--secondary-color);
    }

    /* Travel Information Section */
    .travel-info {
      margin-bottom: 2rem;
      background-color: white;
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      overflow: hidden;
    }

    .travel-info-header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .travel-info-header h2 {
      margin: 0;
      font-size: 1.5rem;
    }

    .travel-info-tabs {
      display: flex;
      background-color: var(--gray-200);
    }

    .travel-tab {
      padding: 1rem;
      cursor: pointer;
      flex: 1;
      text-align: center;
      font-weight: 500;
      transition: var(--transition);
      border-bottom: 3px solid transparent;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .travel-tab i {
      margin-right: 0.5rem;
    }

    .travel-tab.active {
      background-color: white;
      border-bottom-color: var(--accent-color);
    }

    .travel-tab:hover:not(.active) {
      background-color: var(--gray-300);
    }

    .travel-content {
      padding: 1.5rem;
    }

    .travel-panel {
      display: none;
    }

    .travel-panel.active {
      display: block;
    }

    /* Weather Panel */
    .weather-info {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      align-items: center;
    }

    .weather-current {
      display: flex;
      align-items: center;
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      flex: 1;
      min-width: 250px;
    }

    .weather-icon {
      font-size: 3rem;
      margin-right: 1rem;
      color: var(--secondary-color);
    }

    .weather-details h3 {
      margin: 0;
      color: var(--primary-color);
    }

    .weather-temp {
      font-size: 2rem;
      font-weight: bold;
    }

    .weather-desc {
      color: var(--gray-700);
    }

    .weather-forecast {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      flex: 2;
      min-width: 300px;
    }

    .forecast-day {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      text-align: center;
      flex: 1;
      min-width: 100px;
    }

    .forecast-day h4 {
      margin: 0 0 0.5rem 0;
      color: var(--primary-color);
    }

    .forecast-icon {
      font-size: 1.5rem;
      margin: 0.5rem 0;
      color: var(--secondary-color);
    }

    /* Transportation Panel */
    .transport-options {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .transport-option {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
      display: flex;
      flex-direction: column;
    }

    .transport-option h3 {
      display: flex;
      align-items: center;
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .transport-option h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .transport-option p {
      margin-bottom: 0.5rem;
    }

    .transport-option .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    /* Hours & Activities Panel */
    .hours-container {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .opening-hours, .activities-info {
      flex: 1;
      min-width: 250px;
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .opening-hours h3, .activities-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .opening-hours h3 i, .activities-info h3 i {
      margin-right: 0.5rem;
    }

    .hours-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .hours-list li {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .hours-list li:last-child {
      border-bottom: none;
    }

    .day {
      font-weight: 500;
    }

    .activity-item {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .activity-item:last-child {
      border-bottom: none;
    }

    .activity-name {
      font-weight: 500;
    }

    .activity-price {
      font-weight: bold;
    }

    /* Amenities Panel */
    .amenities-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .amenity-card {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .amenity-card h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .amenity-card h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .amenity-card p {
      margin-bottom: 0.5rem;
    }

    .amenity-card .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    .amenity-card .rating {
      color: var(--warning-color);
      margin-bottom: 0.5rem;
    }

    .weather-tips h3, .local-transport h3, .best-time h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .seasonal-activities {
      margin-top: 1.5rem;
    }

    .seasonal-activities h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .season-item {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      margin-bottom: 1rem;
    }

    .season-item h4 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
    }

    .season-item h4 i {
      margin-right: 0.5rem;
    }

    .season-details {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 0.5rem;
    }

    .season-detail {
      display: flex;
      align-items: center;
      font-size: 0.9rem;
      color: var(--gray-700);
    }

    .season-detail i {
      margin-right: 0.25rem;
      font-size: 0.8rem;
    }

    .festival-events {
      margin-top: 1.5rem;
    }

    .festival-events h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .event-item {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      margin-bottom: 1rem;
    }

    .event-item h4 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    .event-date {
      font-weight: bold;
      color: var(--secondary-color);
      margin-bottom: 0.5rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>Heritage Explorer</h1>
    <p>Discover Tamil Nadu's rich heritage and culture</p>
  </header>

  <nav>
    <div>
      <a href="website.html"><i class="fas fa-home"></i> Home</a>
      <a href="#"><i class="fas fa-map-marked-alt"></i> Explore</a>
      <a href="#"><i class="fas fa-info-circle"></i> About</a>
    </div>
    <div>
      <a href="#" id="toggleLang">தமிழ்</a>
    </div>
  </nav>

  <main>
    <div class="site-details">
      <div class="site-image">
        <img src="https://tse4.mm.bing.net/th?id=OIP.WK3IXY-POi8acLl6iYaAywHaE9&pid=Api&P=0&h=220" alt="Ooty Hill Station">
      </div>
      <div class="site-info">
        <h1>Ooty Hill Station</h1>
        <div class="site-meta">
          <div class="meta-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>Nilgiris, Tamil Nadu</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-mountain"></i>
            <span>Hill Station</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-ruler-vertical"></i>
            <span>2,240 meters above sea level</span>
          </div>
        </div>
        <p>Ooty, officially known as Udhagamandalam, is a picturesque hill station in the Nilgiris district of Tamil Nadu. Famous for its tea gardens, botanical gardens, and pleasant climate, it's often called the "Queen of Hill Stations" and offers a perfect retreat from the summer heat.</p>
        <div class="action-buttons">
          <button id="playAudioButton"><i class="fas fa-volume-up"></i> Listen</button>
          <button id="favoriteButton"><i class="far fa-heart"></i> Favorite</button>
          <button id="shareButton"><i class="fas fa-share-alt"></i> Share</button>
        </div>
      </div>
    </div>

    <div class="site-description">
      <h2>About Ooty Hill Station</h2>
      <p>Ooty, officially known as Udhagamandalam, is a popular hill station located in the Nilgiri Hills of Tamil Nadu. Situated at an altitude of 2,240 meters above sea level, it enjoys a pleasant climate throughout the year, with temperatures rarely exceeding 25°C in summer or dropping below 5°C in winter.</p>
      <p>The hill station was established in the early 19th century by the British as a summer retreat to escape the scorching heat of the plains. John Sullivan, the then Collector of Coimbatore, is credited with discovering this beautiful location in 1819. The town still retains much of its colonial charm with stone churches, bungalows, and well-laid gardens.</p>
      <p>Ooty is renowned for its lush tea plantations that carpet the surrounding hills in emerald green. The tea industry, established in the 1830s, continues to be a major economic activity in the region. Visitors can tour tea factories to learn about the tea-making process and sample some of the finest varieties of Nilgiri tea.</p>
      <p>The hill station is home to the Government Botanical Garden, established in 1848, which houses over 1,000 species of plants, including rare ferns, trees, and flowering plants. The annual flower show held in May is a major attraction. Other notable landmarks include the Ooty Lake, a man-made lake created in 1824, the Nilgiri Mountain Railway (a UNESCO World Heritage Site), and the Doddabetta Peak, the highest point in the Nilgiri Hills offering panoramic views of the surrounding landscape.</p>
    </div>

    <div class="map-container">
      <!-- Map will be embedded here -->
      <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d31476.62311012707!2d76.68351687910156!3d11.413!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3ba8bd84b5f3d78d%3A0x179bdb14c93e3f42!2sOoty%2C%20Tamil%20Nadu!5e0!3m2!1sen!2sin!4v1652345678901!5m2!1sen!2sin" width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy" title="Map of Ooty"></iframe>
    </div>

    <!-- Travel Information Section -->
    <div class="travel-info">
      <div class="travel-info-header">
        <h2>Practical Travel Information</h2>
        <div class="last-updated">Last updated: <span id="lastUpdated">Today</span></div>
      </div>

      <div class="travel-info-tabs">
        <div class="travel-tab active" data-tab="weather">
          <i class="fas fa-cloud-sun"></i> Weather
        </div>
        <div class="travel-tab" data-tab="transport">
          <i class="fas fa-bus"></i> Transportation
        </div>
        <div class="travel-tab" data-tab="hours">
          <i class="far fa-clock"></i> Hours & Activities
        </div>
        <div class="travel-tab" data-tab="amenities">
          <i class="fas fa-concierge-bell"></i> Nearby Amenities
        </div>
      </div>

      <div class="travel-content">
        <!-- Weather Panel -->
        <div class="travel-panel active" id="weatherPanel">
          <div class="weather-info">
            <div class="weather-current">
              <div class="weather-icon">
                <i class="fas fa-cloud-sun"></i>
              </div>
              <div class="weather-details">
                <h3>Current Weather</h3>
                <div class="weather-temp">16°C</div>
                <div class="weather-desc">Partly Cloudy</div>
                <div class="weather-extra">Humidity: 85% | Wind: 8 km/h</div>
              </div>
            </div>

            <div class="weather-forecast">
              <div class="forecast-day">
                <h4>Tomorrow</h4>
                <div class="forecast-icon"><i class="fas fa-sun"></i></div>
                <div class="forecast-temp">18°C</div>
                <div class="forecast-desc">Mostly Sunny</div>
              </div>
              <div class="forecast-day">
                <h4>Wed</h4>
                <div class="forecast-icon"><i class="fas fa-cloud"></i></div>
                <div class="forecast-temp">15°C</div>
                <div class="forecast-desc">Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Thu</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-rain"></i></div>
                <div class="forecast-temp">14°C</div>
                <div class="forecast-desc">Light Rain</div>
              </div>
            </div>
          </div>

          <div class="weather-tips">
            <h3>Weather Tips</h3>
            <p>Ooty experiences a pleasant climate throughout the year due to its high altitude. The region has three distinct seasons: summer (March-May), monsoon (June-September), and winter (October-February).</p>
            <p>The best time to visit Ooty is from April to June when the weather is mild and pleasant with temperatures ranging from 10°C to 20°C. This period coincides with the annual flower show in May, making it an ideal time for garden enthusiasts. October to March is also a good time to visit, though it can get quite cold in December and January with temperatures occasionally dropping to near freezing at night.</p>
            <p>The monsoon season (June-September) brings heavy rainfall, making some outdoor activities challenging, but the landscape becomes lush and vibrant. If you're visiting during this period, carry rain gear and waterproof footwear. For winter visits (December-February), pack warm clothing including sweaters, jackets, and thermal wear, especially for early mornings and evenings when temperatures can drop significantly.</p>
            <p>Early mornings in Ooty often have mist and fog, which typically clears by mid-morning. For the best views from lookout points like Doddabetta Peak, plan to visit between 10:00 AM and 2:00 PM when visibility is usually at its best. Sunset views are spectacular from Dolphin's Nose and Lamb's Rock, but be prepared for cooler temperatures as the sun goes down.</p>
          </div>
        </div>

        <!-- Transportation Panel -->
        <div class="travel-panel" id="transportPanel">
          <div class="transport-options">
            <div class="transport-option">
              <h3><i class="fas fa-plane"></i> By Air</h3>
              <p>Coimbatore International Airport is the nearest airport.</p>
              <p class="distance">88 km from Ooty (approx. 3 hours by car)</p>
              <p>Regular flights from Chennai, Bangalore, Mumbai, and Delhi.</p>
              <p>Pre-paid taxis available from the airport (₹2,000-2,500).</p>
              <p>App-based cabs like Ola and Uber operate from the airport.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-train"></i> By Train</h3>
              <p>Mettupalayam Railway Station is the nearest major station.</p>
              <p class="distance">40 km from Ooty (approx. 1.5 hours by car)</p>
              <p>Connected to Coimbatore and Chennai by regular trains.</p>
              <p>The famous Nilgiri Mountain Railway (toy train) runs from Mettupalayam to Ooty.</p>
              <p>Toy train journey takes about 5 hours and offers spectacular views.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-bus"></i> By Bus</h3>
              <p>Well-connected by government and private buses.</p>
              <p>Regular services from Coimbatore (3 hours), Mysore (5 hours), and Bangalore (8 hours).</p>
              <p>TNSTC and KSRTC operate government buses to Ooty.</p>
              <p>Fare: ₹100-300 for government buses, ₹300-800 for private buses.</p>
              <p>The ghat road has 36 hairpin bends - consider motion sickness medication if needed.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-car"></i> By Car</h3>
              <p>Well-connected by road from major cities in South India.</p>
              <p>From Bangalore: NH275 and NH181 (270 km, approx. 6 hours)</p>
              <p>From Chennai: NH32 and NH181 (550 km, approx. 10 hours)</p>
              <p>From Coimbatore: NH181 (85 km, approx. 3 hours)</p>
              <p>The drive through Bandipur/Mudumalai forests is scenic but has night driving restrictions (9 PM - 6 AM).</p>
            </div>
          </div>

          <div class="local-transport">
            <h3>Local Transportation</h3>
            <p>Within Ooty, auto-rickshaws are the most convenient mode of transportation for short distances. Most charge by meter, but it's advisable to confirm the fare before starting your journey. Taxis can be hired for sightseeing packages, typically ranging from ₹1,500-3,000 for a half-day tour covering major attractions.</p>
            <p>The Tamil Nadu Tourism Development Corporation (TTDC) operates daily bus tours that cover major attractions in and around Ooty. These tours are cost-effective and convenient for first-time visitors. Tickets can be booked at the TTDC office near the Boat House or at major hotels.</p>
            <p>For a more leisurely experience, bicycles are available for rent (₹100-200 per day) from shops near the lake area. Electric bikes are also available for rent (₹500-700 per day) and are a good option for navigating the hilly terrain. For those who enjoy walking, the central area of Ooty is compact enough to explore on foot, though the hilly terrain can be challenging for some visitors.</p>
          </div>
        </div>

        <!-- Hours & Activities Panel -->
        <div class="travel-panel" id="hoursPanel">
          <div class="hours-container">
            <div class="opening-hours">
              <h3><i class="far fa-clock"></i> Opening Hours</h3>
              <ul class="hours-list">
                <li><span class="day">Botanical Garden:</span> <span class="time">8:30 AM - 6:30 PM</span></li>
                <li><span class="day">Ooty Lake:</span> <span class="time">9:00 AM - 6:00 PM</span></li>
                <li><span class="day">Rose Garden:</span> <span class="time">8:30 AM - 6:00 PM</span></li>
                <li><span class="day">Doddabetta Peak:</span> <span class="time">7:00 AM - 6:00 PM</span></li>
                <li><span class="day">Tea Factory & Museum:</span> <span class="time">9:00 AM - 6:30 PM</span></li>
              </ul>
              <p class="hours-note">Most attractions are open all days of the week, though some may have reduced hours on public holidays. The Botanical Garden can get crowded on weekends and during the annual flower show in May. For a more peaceful experience, visit on weekdays and arrive early in the morning.</p>
            </div>

            <div class="activities-info">
              <h3><i class="fas fa-hiking"></i> Activities & Prices</h3>
              <div class="activity-item">
                <span class="activity-name">Botanical Garden Entry:</span>
                <span class="activity-price">₹30 per person</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Ooty Lake Boating:</span>
                <span class="activity-price">₹180 for 30 min (motor boat)</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Rose Garden Entry:</span>
                <span class="activity-price">₹20 per person</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Doddabetta Peak Entry:</span>
                <span class="activity-price">₹10 per person</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Nilgiri Mountain Railway:</span>
                <span class="activity-price">₹200-400 per person</span>
              </div>
              <p class="hours-note">Camera fees may apply at certain attractions (typically ₹30-50). Most places accept cash only, so it's advisable to carry sufficient cash. Student discounts are available at some attractions with valid ID.</p>
            </div>
          </div>

          <div class="seasonal-activities">
            <h3>Seasonal Activities</h3>
            <div class="season-item">
              <h4><i class="fas fa-sun"></i> Summer (March-May)</h4>
              <div class="season-details">
                <span class="season-detail"><i class="fas fa-thermometer-half"></i> Temp: 10-20°C</span>
                <span class="season-detail"><i class="fas fa-cloud-rain"></i> Rainfall: Low</span>
              </div>
              <p>The peak tourist season with pleasant weather. The annual flower show in May is a major highlight. This is the best time for outdoor activities like trekking, horse riding, and boating on Ooty Lake. The Botanical Garden is in full bloom during this period. Advance booking for accommodation is highly recommended as this is the busiest season.</p>
            </div>

            <div class="season-item">
              <h4><i class="fas fa-cloud-rain"></i> Monsoon (June-September)</h4>
              <div class="season-details">
                <span class="season-detail"><i class="fas fa-thermometer-half"></i> Temp: 12-18°C</span>
                <span class="season-detail"><i class="fas fa-cloud-rain"></i> Rainfall: High</span>
              </div>
              <p>The landscape turns lush green, and waterfalls in the region are at their full glory. Outdoor activities may be limited due to rainfall, but it's a good time for photography enthusiasts. The tea plantations look particularly beautiful during this season. Pykara Falls and Catherine Falls are spectacular after the rains. Carry rain gear and waterproof footwear if visiting during this period.</p>
            </div>

            <div class="season-item">
              <h4><i class="fas fa-snowflake"></i> Winter (October-February)</h4>
              <div class="season-details">
                <span class="season-detail"><i class="fas fa-thermometer-half"></i> Temp: 5-15°C</span>
                <span class="season-detail"><i class="fas fa-cloud-rain"></i> Rainfall: Low</span>
              </div>
              <p>The weather is cool and crisp, with occasional frost in December and January. This is a good time for nature walks and wildlife spotting in the nearby Mudumalai National Park. The clear skies offer excellent views from viewpoints like Dolphin's Nose and Lamb's Rock. Winter nights can be quite cold, so warm clothing is essential. The Christmas and New Year period sees an influx of tourists, so book accommodation in advance if visiting during this time.</p>
            </div>
          </div>

          <div class="festival-events">
            <h3>Festivals & Events</h3>
            <div class="event-item">
              <h4>Annual Flower Show</h4>
              <p class="event-date">May (usually the second or third weekend)</p>
              <p>Held at the Government Botanical Garden, this is Ooty's most famous event. The show features elaborate flower arrangements, rare plant species, and vegetable carvings. Special flower displays and cultural programs are organized during the five-day event. The show attracts thousands of visitors, so expect crowds and book accommodation well in advance.</p>
            </div>
            <div class="event-item">
              <h4>Tea & Tourism Festival</h4>
              <p class="event-date">January</p>
              <p>A celebration of Nilgiri's famous tea culture, featuring tea tasting sessions, cultural performances, and exhibitions on tea production. The festival is organized by the Tamil Nadu Tourism Department and offers insights into the region's tea heritage. Special tours of tea estates and factories are arranged during this period.</p>
            </div>
            <div class="event-item">
              <h4>Ooty Summer Festival</h4>
              <p class="event-date">April-May</p>
              <p>A month-long celebration featuring various cultural events, boat races on Ooty Lake, flower shows, and food festivals. The festival showcases the cultural heritage of the Nilgiri region, including tribal dances and music. Various sporting events and adventure activities are also organized during this period.</p>
            </div>
          </div>
        </div>

        <!-- Amenities Panel -->
        <div class="travel-panel" id="amenitiesPanel">
          <div class="amenities-grid">
            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Taj Savoy Hotel</strong></p>
              <p class="distance">1 km from town center</p>
              <p class="rating">★★★★★ (4.5/5)</p>
              <p>Luxury heritage hotel with colonial architecture and beautiful gardens.</p>
              <p>Price range: ₹10,000 - ₹20,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Sterling Ooty Fern Hill</strong></p>
              <p class="distance">3 km from town center</p>
              <p class="rating">★★★★☆ (4.2/5)</p>
              <p>Mid-range resort with valley views and family-friendly amenities.</p>
              <p>Price range: ₹5,000 - ₹8,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>YWCA Anandagiri</strong></p>
              <p class="distance">1.5 km from town center</p>
              <p class="rating">★★★☆☆ (3.8/5)</p>
              <p>Budget-friendly option with clean rooms and basic amenities.</p>
              <p>Price range: ₹1,500 - ₹3,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Earls Secret</strong></p>
              <p class="distance">In town center</p>
              <p class="rating">★★★★★ (4.6/5)</p>
              <p>Fine dining restaurant serving Continental and Indian cuisine.</p>
              <p>Price range: ₹1,000 - ₹1,500 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Nahar Restaurant</strong></p>
              <p class="distance">0.5 km from town center</p>
              <p class="rating">★★★★☆ (4.3/5)</p>
              <p>Popular for South Indian and North Indian cuisine.</p>
              <p>Price range: ₹600 - ₹900 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-shopping-bag"></i> Shopping</h3>
              <p><strong>Ooty Chocolate Factory</strong></p>
              <p class="distance">1 km from town center</p>
              <p class="rating">★★★★☆ (4.4/5)</p>
              <p>Famous for handmade chocolates in various flavors.</p>
              <p>Must-buy: Homemade chocolates, tea, spices, and essential oils</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-first-aid"></i> Medical Facilities</h3>
              <p><strong>Government Headquarters Hospital</strong></p>
              <p class="distance">1.5 km from town center</p>
              <p>24/7 emergency services and general medical care.</p>
              <p>Contact: +91 423 244 3400</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-info-circle"></i> Tourist Information</h3>
              <p><strong>Tamil Nadu Tourism Office</strong></p>
              <p class="distance">Near Boat House</p>
              <p>Provides maps, brochures, and guided tour information.</p>
              <p>Contact: +91 423 244 3977</p>
            </div>
          </div>

          <div class="best-time">
            <h3>Visitor Tips</h3>
            <p>Ooty can get quite crowded during weekends and public holidays, especially in the summer months. For a more relaxed experience, consider visiting on weekdays. The town center is compact and can be explored on foot, but the terrain is hilly, so comfortable walking shoes are essential. For exploring attractions outside the town, hiring a taxi for the day is the most convenient option.</p>
            <p>Mobile network coverage is generally good in the town center, with Airtel, Jio, and BSNL providing the best connectivity. However, signal strength may be limited in some valley areas and remote viewpoints. Most hotels and cafes offer Wi-Fi, though the speed may vary. ATMs are readily available in the town center, but it's advisable to carry cash when visiting remote attractions.</p>
            <p>Altitude sickness is rare in Ooty due to its moderate elevation, but some visitors may experience mild symptoms like headaches or breathlessness. Stay hydrated and avoid strenuous activities on your first day to acclimatize. The sun's UV rays can be strong at this altitude even when the temperature is cool, so use sunscreen even on cloudy days. If you're planning to visit during the peak season (April-June), book accommodation at least 2-3 months in advance to secure the best options.</p>
          </div>
        </div>
      </div>
    </div>

    <div class="nearby-attractions">
      <h2>Nearby Attractions</h2>
      <div class="attractions-grid">
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Botanical_Gardens_Ooty_20190212182003.jpg" alt="Botanical Gardens">
          <div class="attraction-card-content">
            <h3>Botanical Gardens</h3>
            <p>Established in 1848, these terraced gardens house over 1,000 species of plants, including rare ferns, trees, and a 20-million-year-old fossilized tree.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Nilgiri_Mountain_Railway_20190212182004.jpg" alt="Nilgiri Mountain Railway">
          <div class="attraction-card-content">
            <h3>Nilgiri Mountain Railway</h3>
            <p>A UNESCO World Heritage Site, this toy train offers a scenic journey through tunnels, bridges, and tea plantations from Mettupalayam to Ooty.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Doddabetta_Peak_20190212182005.jpg" alt="Doddabetta Peak">
          <div class="attraction-card-content">
            <h3>Doddabetta Peak</h3>
            <p>The highest point in the Nilgiri Hills at 2,637 meters, offering panoramic views of the surrounding landscape and a telescope house for better viewing.</p>
          </div>
        </div>
      </div>
    </div>
  </main>

  <footer>
    <p>&copy; 2025 Heritage Explorer | Explore Tamil Nadu</p>
  </footer>

  <div id="backToTop">
    <i class="fas fa-arrow-up"></i>
  </div>

  <script>
    // Back to Top Button
    const backToTop = document.getElementById("backToTop");
    window.addEventListener("scroll", () => {
      if (window.scrollY > 300) {
        backToTop.style.display = "flex";
      } else {
        backToTop.style.display = "none";
      }
    });
    backToTop.addEventListener("click", () => {
      window.scrollTo({ top: 0, behavior: "smooth" });
    });

    // Audio Playback
    document.getElementById("playAudioButton").addEventListener("click", () => {
      const description = document.querySelector(".site-info p").textContent;
      const utterance = new SpeechSynthesisUtterance(description);
      utterance.lang = "en-US";
      speechSynthesis.speak(utterance);
    });

    // Favorite Button
    document.getElementById("favoriteButton").addEventListener("click", function() {
      const icon = this.querySelector("i");
      if (icon.classList.contains("far")) {
        icon.classList.remove("far");
        icon.classList.add("fas");
        alert("Added to favorites!");
      } else {
        icon.classList.remove("fas");
        icon.classList.add("far");
        alert("Removed from favorites!");
      }
    });

    // Share Button
    document.getElementById("shareButton").addEventListener("click", () => {
      const shareData = {
        title: "Ooty Hill Station - Heritage Explorer",
        text: "Check out this beautiful hill station in Tamil Nadu!",
        url: window.location.href
      };

      if (navigator.share) {
        navigator.share(shareData)
          .then(() => console.log("Shared successfully"))
          .catch((error) => console.error("Error sharing:", error));
      } else {
        navigator.clipboard.writeText(shareData.url)
          .then(() => alert("URL copied to clipboard"))
          .catch((err) => console.error("Could not copy text: ", err));
      }
    });

    // Initialize the travel info tabs
    document.addEventListener("DOMContentLoaded", function() {
      // Set the last updated date
      const today = new Date();
      document.getElementById("lastUpdated").textContent = today.toLocaleDateString();

      // Tab functionality
      const tabs = document.querySelectorAll(".travel-tab");
      tabs.forEach(tab => {
        tab.addEventListener("click", function() {
          // Remove active class from all tabs
          tabs.forEach(t => t.classList.remove("active"));

          // Add active class to clicked tab
          this.classList.add("active");

          // Hide all panels
          const panels = document.querySelectorAll(".travel-panel");
          panels.forEach(panel => panel.classList.remove("active"));

          // Show the corresponding panel
          const tabId = this.getAttribute("data-tab");
          document.getElementById(tabId + "Panel").classList.add("active");
        });
      });

      // Fetch weather data (mock)
      fetchWeatherData();
    });

    // Weather API Integration (Mock)
    function fetchWeatherData() {
      // Coordinates for Ooty
      const lat = 11.413;
      const lon = 76.695;

      // Mock weather data
      const mockWeatherData = {
        current: {
          temp: 16,
          humidity: 85,
          wind_speed: 8,
          weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
        },
        daily: [
          {
            dt: Date.now() + 86400000, // Tomorrow
            temp: { day: 18 },
            weather: [{ main: "Clear", description: "Mostly Sunny", icon: "01d" }]
          },
          {
            dt: Date.now() + 172800000, // Day after tomorrow
            temp: { day: 15 },
            weather: [{ main: "Clouds", description: "Cloudy", icon: "03d" }]
          },
          {
            dt: Date.now() + 259200000, // 3 days from now
            temp: { day: 14 },
            weather: [{ main: "Rain", description: "Light Rain", icon: "10d" }]
          }
        ]
      };

      updateWeatherUI(mockWeatherData);
    }

    function updateWeatherUI(data) {
      // Update current weather
      document.querySelector(".weather-temp").textContent = `${data.current.temp}°C`;
      document.querySelector(".weather-desc").textContent = data.current.weather[0].description;
      document.querySelector(".weather-extra").textContent = `Humidity: ${data.current.humidity}% | Wind: ${data.current.wind_speed} km/h`;

      // Update weather icon
      const weatherIconElement = document.querySelector(".weather-icon i");
      updateWeatherIcon(weatherIconElement, data.current.weather[0].main);

      // Update forecast
      const forecastDays = document.querySelectorAll(".forecast-day");
      data.daily.forEach((day, index) => {
        if (index < forecastDays.length) {
          const dayElement = forecastDays[index];
          const date = new Date(day.dt);
          const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });

          dayElement.querySelector("h4").textContent = index === 0 ? "Tomorrow" : dayName;
          dayElement.querySelector(".forecast-temp").textContent = `${day.temp.day}°C`;
          dayElement.querySelector(".forecast-desc").textContent = day.weather[0].description;

          // Update forecast icon
          const iconElement = dayElement.querySelector(".forecast-icon i");
          updateWeatherIcon(iconElement, day.weather[0].main);
        }
      });
    }

    function updateWeatherIcon(iconElement, weatherType) {
      // Remove all existing weather classes
      iconElement.className = "";

      // Add the appropriate weather icon class
      switch (weatherType) {
        case "Clear":
          iconElement.classList.add("fas", "fa-sun");
          break;
        case "Clouds":
          iconElement.classList.add("fas", "fa-cloud");
          break;
        case "Rain":
        case "Drizzle":
          iconElement.classList.add("fas", "fa-cloud-rain");
          break;
        case "Thunderstorm":
          iconElement.classList.add("fas", "fa-bolt");
          break;
        case "Snow":
          iconElement.classList.add("fas", "fa-snowflake");
          break;
        case "Mist":
        case "Fog":
        case "Haze":
          iconElement.classList.add("fas", "fa-smog");
          break;
        default:
          iconElement.classList.add("fas", "fa-cloud-sun");
      }
    }
  </script>

  <!-- Audio Guide Script -->
  <script src="audio-player.js"></script>

  <script>
    // Initialize the audio guide with chapters
    const ootyChapters = [
      { title: "Introduction", startTime: 0 },
      { title: "Historical Background", startTime: 60 },
      { title: "Natural Attractions", startTime: 120 },
      { title: "Colonial Heritage", startTime: 180 },
      { title: "Tea Plantations", startTime: 240 },
      { title: "Travel Tips", startTime: 300 }
    ];

    initAudioGuide('ooty', ootyChapters);
  </script>

  <!-- Language Toggle Script -->
  <script src="language-toggle.js"></script>
</body>
</html>
