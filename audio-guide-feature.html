<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Multilingual Audio Guide Feature - Heritage Explorer</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
  <style>
    :root {
      --primary-color: #e63946;
      --secondary-color: #457b9d;
      --accent-color: #1d3557;
      --light-color: #f1faee;
      --dark-color: #1d3557;
      --gray-100: #f8f9fa;
      --gray-200: #e9ecef;
      --gray-300: #dee2e6;
      --gray-400: #ced4da;
      --gray-500: #adb5bd;
      --gray-600: #6c757d;
      --gray-700: #495057;
      --gray-800: #343a40;
      --gray-900: #212529;
      --border-radius: 0.5rem;
      --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
      --transition: all 0.3s ease-in-out;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: var(--gray-800);
      background-color: var(--gray-100);
    }

    header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      text-align: center;
    }

    main {
      max-width: 1200px;
      margin: 2rem auto;
      padding: 0 1rem;
    }

    .feature-section {
      background-color: white;
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      padding: 2rem;
      margin-bottom: 2rem;
    }

    .feature-header {
      display: flex;
      align-items: center;
      margin-bottom: 1.5rem;
      border-bottom: 2px solid var(--primary-color);
      padding-bottom: 1rem;
    }

    .feature-icon {
      font-size: 2.5rem;
      color: var(--primary-color);
      margin-right: 1rem;
    }

    .feature-title {
      margin: 0;
      color: var(--primary-color);
    }

    .feature-description {
      margin-bottom: 2rem;
    }

    .feature-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 2rem;
    }

    .feature-card {
      background-color: var(--gray-100);
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
      transition: var(--transition);
    }

    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.2);
    }

    .feature-card-image {
      height: 200px;
      overflow: hidden;
    }

    .feature-card-image img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: var(--transition);
    }

    .feature-card:hover .feature-card-image img {
      transform: scale(1.05);
    }

    .feature-card-content {
      padding: 1.5rem;
    }

    .feature-card-title {
      color: var(--accent-color);
      margin-bottom: 0.5rem;
    }

    .feature-card-text {
      color: var(--gray-700);
      margin-bottom: 1rem;
    }

    .feature-card-link {
      display: inline-block;
      color: var(--primary-color);
      text-decoration: none;
      font-weight: 500;
      transition: var(--transition);
    }

    .feature-card-link:hover {
      color: var(--accent-color);
    }

    .feature-demo {
      margin-top: 2rem;
      padding: 1.5rem;
      background-color: var(--gray-100);
      border-radius: var(--border-radius);
      border-left: 4px solid var(--primary-color);
    }

    .demo-title {
      color: var(--accent-color);
      margin-bottom: 1rem;
    }

    .demo-steps {
      list-style-position: inside;
      margin-bottom: 1.5rem;
    }

    .demo-steps li {
      margin-bottom: 0.5rem;
    }

    .demo-image {
      max-width: 100%;
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      margin: 1rem 0;
    }

    .language-badge {
      display: inline-block;
      padding: 0.25rem 0.5rem;
      background-color: var(--secondary-color);
      color: white;
      border-radius: 0.25rem;
      font-size: 0.8rem;
      margin-right: 0.5rem;
    }

    .cta-button {
      display: inline-block;
      background-color: var(--primary-color);
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: var(--border-radius);
      text-decoration: none;
      font-weight: 500;
      transition: var(--transition);
      margin-top: 1rem;
    }

    .cta-button:hover {
      background-color: var(--accent-color);
    }

    footer {
      background-color: var(--primary-color);
      color: white;
      text-align: center;
      padding: 1rem;
      margin-top: 2rem;
    }

    .chapter-list {
      margin-top: 1rem;
      padding-left: 1.5rem;
    }

    .chapter-list li {
      margin-bottom: 0.5rem;
    }

    .feature-benefits {
      margin-top: 2rem;
      background-color: var(--light-color);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .benefits-title {
      color: var(--accent-color);
      margin-bottom: 1rem;
    }

    .benefits-list {
      list-style-type: none;
      padding: 0;
    }

    .benefits-list li {
      margin-bottom: 0.75rem;
      padding-left: 1.5rem;
      position: relative;
    }

    .benefits-list li:before {
      content: "✓";
      color: var(--primary-color);
      position: absolute;
      left: 0;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <header>
    <h1>Heritage Explorer</h1>
    <p>Discover Tamil Nadu's rich heritage and culture</p>
  </header>

  <main>
    <div class="feature-section">
      <div class="feature-header">
        <div class="feature-icon">
          <i class="fas fa-headphones"></i>
        </div>
        <h2 class="feature-title">Multilingual Audio Guide Feature</h2>
      </div>

      <div class="feature-description">
        <p>Enhance your heritage exploration experience with our new multilingual audio guides. Listen to detailed narrations about Tamil Nadu's magnificent heritage sites in both English and Tamil. Our audio guides provide comprehensive information about the history, architecture, cultural significance, and interesting facts about each site.</p>
      </div>

      <div class="feature-grid">
        <div class="feature-card">
          <div class="feature-card-image">
            <img src="https://dynamic-media-cdn.tripadvisor.com/media/photo-o/17/6a/e5/a7/thanjavur-brihadeeshwara.jpg?w=1200&h=1200&s=1" alt="Brihadeeswarar Temple">
          </div>
          <div class="feature-card-content">
            <h3 class="feature-card-title">Brihadeeswarar Temple Audio Guide</h3>
            <div>
              <span class="language-badge">English</span>
              <span class="language-badge">தமிழ்</span>
            </div>
            <p class="feature-card-text">Explore the magnificent UNESCO World Heritage Site with our detailed audio guide covering the temple's history, architecture, sculptures, and religious significance.</p>
            <ul class="chapter-list">
              <li>Introduction</li>
              <li>Historical Background</li>
              <li>Architecture</li>
              <li>Sculptures & Carvings</li>
              <li>Religious Significance</li>
              <li>Conservation Efforts</li>
            </ul>
            <a href="brihadeeswarar-temple.html" class="feature-card-link">Visit Temple Page <i class="fas fa-arrow-right"></i></a>
          </div>
        </div>

        <div class="feature-card">
          <div class="feature-card-image">
            <img src="https://dynamic-media-cdn.tripadvisor.com/media/photo-o/0d/de/f0/eb/shore-temple.jpg?w=1200&h=-1&s=1" alt="Shore Temple">
          </div>
          <div class="feature-card-content">
            <h3 class="feature-card-title">Shore Temple Audio Guide</h3>
            <div>
              <span class="language-badge">English</span>
              <span class="language-badge">தமிழ்</span>
            </div>
            <p class="feature-card-text">Listen to the fascinating story of this ancient seaside temple, its unique architecture, and its cultural importance as a UNESCO World Heritage Site.</p>
            <ul class="chapter-list">
              <li>Introduction</li>
              <li>Historical Background</li>
              <li>Architecture</li>
              <li>Sculptures & Carvings</li>
              <li>Religious Significance</li>
              <li>Conservation Efforts</li>
            </ul>
            <a href="shore-temple.html" class="feature-card-link">Visit Temple Page <i class="fas fa-arrow-right"></i></a>
          </div>
        </div>

        <div class="feature-card">
          <div class="feature-card-image">
            <img src="https://dynamic-media-cdn.tripadvisor.com/media/photo-o/0c/cf/5d/06/ooty-lake.jpg?w=1200&h=-1&s=1" alt="Ooty Hill Station">
          </div>
          <div class="feature-card-content">
            <h3 class="feature-card-title">Ooty Hill Station Audio Guide</h3>
            <div>
              <span class="language-badge">English</span>
              <span class="language-badge">தமிழ்</span>
            </div>
            <p class="feature-card-text">Discover the Queen of Hill Stations through our comprehensive audio guide covering its colonial history, natural attractions, and cultural significance.</p>
            <ul class="chapter-list">
              <li>Introduction</li>
              <li>Historical Background</li>
              <li>Natural Attractions</li>
              <li>Colonial Heritage</li>
              <li>Tea Plantations</li>
              <li>Travel Tips</li>
            </ul>
            <a href="ooty.html" class="feature-card-link">Visit Hill Station Page <i class="fas fa-arrow-right"></i></a>
          </div>
        </div>
      </div>

      <div class="feature-demo">
        <h3 class="demo-title">How to Use the Audio Guide</h3>
        <ol class="demo-steps">
          <li>Visit any heritage site page that features the audio guide (look for the headphones icon).</li>
          <li>Scroll down to find the audio player below the site description.</li>
          <li>Select your preferred language (English or Tamil) from the dropdown menu.</li>
          <li>Press the play button to start listening to the audio guide.</li>
          <li>Use the chapter selector to jump to specific sections of interest.</li>
          <li>Adjust volume and playback speed as needed.</li>
          <li>Download the audio guide for offline listening if desired.</li>
        </ol>
        <p>For a full demonstration of the audio guides, visit our <a href="audio-guides/audio-guide-demo.html">Audio Guide Demo Page</a>.</p>
        <a href="audio-guides/audio-guide-demo.html" class="cta-button">Try Audio Guide Demo</a>
      </div>

      <div class="feature-benefits">
        <h3 class="benefits-title">Benefits of Our Multilingual Audio Guides</h3>
        <ul class="benefits-list">
          <li>Professionally narrated content in both English and Tamil</li>
          <li>Comprehensive information organized into easy-to-navigate chapters</li>
          <li>Freedom to explore at your own pace</li>
          <li>Accessibility for visually impaired visitors</li>
          <li>Deeper understanding of cultural and historical context</li>
          <li>Downloadable for offline use during your visit</li>
          <li>Seamless language switching with our language toggle feature</li>
          <li>Adjustable playback speed for your listening preference</li>
        </ul>
      </div>
    </div>
  </main>

  <footer>
    <p>&copy; 2025 Heritage Explorer | Explore Tamil Nadu</p>
  </footer>
</body>
</html>
