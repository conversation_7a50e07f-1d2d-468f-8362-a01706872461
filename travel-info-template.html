<!-- Travel Information Section Template -->
<!-- This template can be customized for each heritage site -->

<!-- CSS Styles for Travel Information Section -->
<style>
  /* Travel Information Section */
  .travel-info {
    margin-bottom: 2rem;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
  }
  
  .travel-info-header {
    background-color: var(--primary-color);
    color: white;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .travel-info-header h2 {
    margin: 0;
    font-size: 1.5rem;
  }
  
  .travel-info-tabs {
    display: flex;
    background-color: var(--gray-200);
  }
  
  .travel-tab {
    padding: 1rem;
    cursor: pointer;
    flex: 1;
    text-align: center;
    font-weight: 500;
    transition: var(--transition);
    border-bottom: 3px solid transparent;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .travel-tab i {
    margin-right: 0.5rem;
  }
  
  .travel-tab.active {
    background-color: white;
    border-bottom-color: var(--accent-color);
  }
  
  .travel-tab:hover:not(.active) {
    background-color: var(--gray-300);
  }
  
  .travel-content {
    padding: 1.5rem;
  }
  
  .travel-panel {
    display: none;
  }
  
  .travel-panel.active {
    display: block;
  }
  
  /* Weather Panel */
  .weather-info {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    align-items: center;
  }
  
  .weather-current {
    display: flex;
    align-items: center;
    background-color: var(--gray-100);
    padding: 1rem;
    border-radius: var(--border-radius);
    flex: 1;
    min-width: 250px;
  }
  
  .weather-icon {
    font-size: 3rem;
    margin-right: 1rem;
    color: var(--secondary-color);
  }
  
  .weather-details h3 {
    margin: 0;
    color: var(--primary-color);
  }
  
  .weather-temp {
    font-size: 2rem;
    font-weight: bold;
  }
  
  .weather-desc {
    color: var(--gray-700);
  }
  
  .weather-forecast {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    flex: 2;
    min-width: 300px;
  }
  
  .forecast-day {
    background-color: var(--gray-100);
    padding: 1rem;
    border-radius: var(--border-radius);
    text-align: center;
    flex: 1;
    min-width: 100px;
  }
  
  .forecast-day h4 {
    margin: 0 0 0.5rem 0;
    color: var(--primary-color);
  }
  
  .forecast-icon {
    font-size: 1.5rem;
    margin: 0.5rem 0;
    color: var(--secondary-color);
  }
  
  /* Transportation Panel */
  .transport-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
  }
  
  .transport-option {
    background-color: var(--gray-100);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    display: flex;
    flex-direction: column;
  }
  
  .transport-option h3 {
    display: flex;
    align-items: center;
    color: var(--primary-color);
    margin-bottom: 1rem;
  }
  
  .transport-option h3 i {
    margin-right: 0.5rem;
    font-size: 1.5rem;
  }
  
  .transport-option p {
    margin-bottom: 0.5rem;
  }
  
  .transport-option .distance {
    font-weight: bold;
    color: var(--gray-700);
  }
  
  /* Hours & Tickets Panel */
  .hours-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
  }
  
  .opening-hours, .ticket-info {
    flex: 1;
    min-width: 250px;
    background-color: var(--gray-100);
    padding: 1.5rem;
    border-radius: var(--border-radius);
  }
  
  .opening-hours h3, .ticket-info h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
  }
  
  .opening-hours h3 i, .ticket-info h3 i {
    margin-right: 0.5rem;
  }
  
  .hours-list {
    list-style: none;
  }
  
  .hours-list li {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--gray-300);
  }
  
  .hours-list li:last-child {
    border-bottom: none;
  }
  
  .day {
    font-weight: 500;
  }
  
  .ticket-type {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--gray-300);
  }
  
  .ticket-type:last-child {
    border-bottom: none;
  }
  
  .ticket-price {
    font-weight: bold;
  }
  
  /* Amenities Panel */
  .amenities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
  }
  
  .amenity-card {
    background-color: var(--gray-100);
    padding: 1.5rem;
    border-radius: var(--border-radius);
  }
  
  .amenity-card h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
  }
  
  .amenity-card h3 i {
    margin-right: 0.5rem;
    font-size: 1.5rem;
  }
  
  .amenity-card p {
    margin-bottom: 0.5rem;
  }
  
  .amenity-card .distance {
    font-weight: bold;
    color: var(--gray-700);
  }
  
  .amenity-card .rating {
    color: var(--warning-color);
    margin-bottom: 0.5rem;
  }
</style>

<!-- HTML Structure for Travel Information Section -->
<div class="travel-info">
  <div class="travel-info-header">
    <h2>Practical Travel Information</h2>
    <div class="last-updated">Last updated: <span id="lastUpdated">Today</span></div>
  </div>
  
  <div class="travel-info-tabs">
    <div class="travel-tab active" data-tab="weather">
      <i class="fas fa-cloud-sun"></i> Weather
    </div>
    <div class="travel-tab" data-tab="transport">
      <i class="fas fa-bus"></i> Transportation
    </div>
    <div class="travel-tab" data-tab="hours">
      <i class="far fa-clock"></i> Hours & Tickets
    </div>
    <div class="travel-tab" data-tab="amenities">
      <i class="fas fa-concierge-bell"></i> Nearby Amenities
    </div>
  </div>
  
  <div class="travel-content">
    <!-- Weather Panel -->
    <div class="travel-panel active" id="weatherPanel">
      <div class="weather-info">
        <div class="weather-current">
          <div class="weather-icon">
            <i class="fas fa-sun"></i>
          </div>
          <div class="weather-details">
            <h3>Current Weather</h3>
            <div class="weather-temp">32°C</div>
            <div class="weather-desc">Sunny</div>
            <div class="weather-extra">Humidity: 65% | Wind: 12 km/h</div>
          </div>
        </div>
        
        <div class="weather-forecast">
          <div class="forecast-day">
            <h4>Tomorrow</h4>
            <div class="forecast-icon"><i class="fas fa-cloud-sun"></i></div>
            <div class="forecast-temp">30°C</div>
            <div class="forecast-desc">Partly Cloudy</div>
          </div>
          <div class="forecast-day">
            <h4>Wed</h4>
            <div class="forecast-icon"><i class="fas fa-cloud"></i></div>
            <div class="forecast-temp">29°C</div>
            <div class="forecast-desc">Cloudy</div>
          </div>
          <div class="forecast-day">
            <h4>Thu</h4>
            <div class="forecast-icon"><i class="fas fa-cloud-sun-rain"></i></div>
            <div class="forecast-temp">28°C</div>
            <div class="forecast-desc">Light Rain</div>
          </div>
        </div>
      </div>
      
      <div class="weather-tips">
        <h3>Weather Tips</h3>
        <p>Weather tips specific to this location will go here. Include information about the best seasons to visit, what to wear, and any weather-related precautions.</p>
      </div>
    </div>
    
    <!-- Transportation Panel -->
    <div class="travel-panel" id="transportPanel">
      <div class="transport-options">
        <div class="transport-option">
          <h3><i class="fas fa-plane"></i> By Air</h3>
          <p>Information about the nearest airport.</p>
          <p class="distance">Distance from the site</p>
          <p>Available flights and connections.</p>
          <p>Transportation options from the airport.</p>
        </div>
        
        <div class="transport-option">
          <h3><i class="fas fa-train"></i> By Train</h3>
          <p>Information about the nearest railway station.</p>
          <p class="distance">Distance from the site</p>
          <p>Available train services.</p>
          <p>Transportation options from the station.</p>
        </div>
        
        <div class="transport-option">
          <h3><i class="fas fa-bus"></i> By Bus</h3>
          <p>Information about bus services.</p>
          <p class="distance">Distance from the site</p>
          <p>Available bus routes.</p>
          <p>Bus frequency and timings.</p>
        </div>
        
        <div class="transport-option">
          <h3><i class="fas fa-car"></i> By Car</h3>
          <p>Driving directions from major cities.</p>
          <p>Road conditions and approximate travel times.</p>
          <p>Parking information.</p>
        </div>
      </div>
      
      <div class="local-transport">
        <h3>Local Transportation</h3>
        <p>Information about local transportation options like auto-rickshaws, taxis, and app-based services.</p>
      </div>
    </div>
    
    <!-- Hours & Tickets Panel -->
    <div class="travel-panel" id="hoursPanel">
      <div class="hours-container">
        <div class="opening-hours">
          <h3><i class="far fa-clock"></i> Opening Hours</h3>
          <ul class="hours-list">
            <li><span class="day">Monday - Friday:</span> <span class="time">Opening hours</span></li>
            <li><span class="day">Saturday - Sunday:</span> <span class="time">Weekend hours</span></li>
            <li><span class="day">Holidays:</span> <span class="time">Holiday hours</span></li>
          </ul>
          <p class="hours-note">Additional notes about opening hours, closures, or special timings.</p>
        </div>
        
        <div class="ticket-info">
          <h3><i class="fas fa-ticket-alt"></i> Ticket Information</h3>
          <div class="ticket-type">
            <span>Indian Nationals:</span>
            <span class="ticket-price">Price</span>
          </div>
          <div class="ticket-type">
            <span>Foreign Nationals:</span>
            <span class="ticket-price">Price</span>
          </div>
          <div class="ticket-type">
            <span>Camera Fee:</span>
            <span class="ticket-price">Price</span>
          </div>
          <p class="ticket-note">Additional notes about tickets, discounts, or payment methods.</p>
        </div>
      </div>
      
      <div class="best-time">
        <h3>Best Time to Visit</h3>
        <p>Information about the best time of day and year to visit the site.</p>
      </div>
    </div>
    
    <!-- Amenities Panel -->
    <div class="travel-panel" id="amenitiesPanel">
      <div class="amenities-grid">
        <div class="amenity-card">
          <h3><i class="fas fa-hotel"></i> Accommodation</h3>
          <p><strong>Hotel Name</strong></p>
          <p class="distance">Distance from site</p>
          <p class="rating">Rating</p>
          <p>Brief description of the accommodation.</p>
          <p>Price range information.</p>
        </div>
        
        <div class="amenity-card">
          <h3><i class="fas fa-utensils"></i> Restaurants</h3>
          <p><strong>Restaurant Name</strong></p>
          <p class="distance">Distance from site</p>
          <p class="rating">Rating</p>
          <p>Brief description of the restaurant and cuisine.</p>
          <p>Price range information.</p>
        </div>
        
        <div class="amenity-card">
          <h3><i class="fas fa-shopping-bag"></i> Shopping</h3>
          <p><strong>Shop Name</strong></p>
          <p class="distance">Distance from site</p>
          <p>Brief description of shopping options.</p>
          <p>What to buy as souvenirs.</p>
        </div>
        
        <div class="amenity-card">
          <h3><i class="fas fa-first-aid"></i> Medical Facilities</h3>
          <p><strong>Medical Facility Name</strong></p>
          <p class="distance">Distance from site</p>
          <p>Brief description of medical services.</p>
          <p>Emergency contact information.</p>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- JavaScript for Travel Information Section -->
<script>
  // Initialize the travel info tabs
  document.addEventListener("DOMContentLoaded", function() {
    // Set the last updated date
    const today = new Date();
    document.getElementById("lastUpdated").textContent = today.toLocaleDateString();
    
    // Tab functionality
    const tabs = document.querySelectorAll(".travel-tab");
    tabs.forEach(tab => {
      tab.addEventListener("click", function() {
        // Remove active class from all tabs
        tabs.forEach(t => t.classList.remove("active"));
        
        // Add active class to clicked tab
        this.classList.add("active");
        
        // Hide all panels
        const panels = document.querySelectorAll(".travel-panel");
        panels.forEach(panel => panel.classList.remove("active"));
        
        // Show the corresponding panel
        const tabId = this.getAttribute("data-tab");
        document.getElementById(tabId + "Panel").classList.add("active");
      });
    });
  });
  
  // Weather API Integration (Mock)
  function fetchWeatherData(lat, lon) {
    // Mock weather data
    const mockWeatherData = {
      current: {
        temp: 32,
        humidity: 65,
        wind_speed: 12,
        weather: [{ main: "Clear", description: "Sunny", icon: "01d" }]
      },
      daily: [
        {
          dt: Date.now() + 86400000, // Tomorrow
          temp: { day: 30 },
          weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
        },
        {
          dt: Date.now() + *********, // Day after tomorrow
          temp: { day: 29 },
          weather: [{ main: "Clouds", description: "Cloudy", icon: "03d" }]
        },
        {
          dt: Date.now() + 259200000, // 3 days from now
          temp: { day: 28 },
          weather: [{ main: "Rain", description: "Light Rain", icon: "10d" }]
        }
      ]
    };
    
    updateWeatherUI(mockWeatherData);
  }
  
  function updateWeatherUI(data) {
    // Update current weather
    document.querySelector(".weather-temp").textContent = `${data.current.temp}°C`;
    document.querySelector(".weather-desc").textContent = data.current.weather[0].description;
    document.querySelector(".weather-extra").textContent = `Humidity: ${data.current.humidity}% | Wind: ${data.current.wind_speed} km/h`;
    
    // Update weather icon
    const weatherIconElement = document.querySelector(".weather-icon i");
    updateWeatherIcon(weatherIconElement, data.current.weather[0].main);
    
    // Update forecast
    const forecastDays = document.querySelectorAll(".forecast-day");
    data.daily.forEach((day, index) => {
      if (index < forecastDays.length) {
        const dayElement = forecastDays[index];
        const date = new Date(day.dt);
        const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });
        
        dayElement.querySelector("h4").textContent = index === 0 ? "Tomorrow" : dayName;
        dayElement.querySelector(".forecast-temp").textContent = `${day.temp.day}°C`;
        dayElement.querySelector(".forecast-desc").textContent = day.weather[0].description;
        
        // Update forecast icon
        const iconElement = dayElement.querySelector(".forecast-icon i");
        updateWeatherIcon(iconElement, day.weather[0].main);
      }
    });
  }
  
  function updateWeatherIcon(iconElement, weatherType) {
    // Remove all existing weather classes
    iconElement.className = "";
    
    // Add the appropriate weather icon class
    switch (weatherType) {
      case "Clear":
        iconElement.classList.add("fas", "fa-sun");
        break;
      case "Clouds":
        iconElement.classList.add("fas", "fa-cloud");
        break;
      case "Rain":
      case "Drizzle":
        iconElement.classList.add("fas", "fa-cloud-rain");
        break;
      case "Thunderstorm":
        iconElement.classList.add("fas", "fa-bolt");
        break;
      case "Snow":
        iconElement.classList.add("fas", "fa-snowflake");
        break;
      case "Mist":
      case "Fog":
      case "Haze":
        iconElement.classList.add("fas", "fa-smog");
        break;
      default:
        iconElement.classList.add("fas", "fa-cloud-sun");
    }
  }
</script>
