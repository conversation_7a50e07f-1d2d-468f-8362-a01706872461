<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Kodaikanal - Heritage Explorer</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
  <style>
    :root {
      --primary-color: #1a237e;
      --secondary-color: #457b9d;
      --accent-color: #e63946;
      --light-color: #f1faee;
      --dark-color: #1d3557;
      --success-color: #2e7d32;
      --warning-color: #ff9800;
      --danger-color: #c62828;
      --gray-100: #f8f9fa;
      --gray-200: #e9ecef;
      --gray-300: #dee2e6;
      --gray-400: #ced4da;
      --gray-500: #adb5bd;
      --gray-600: #6c757d;
      --gray-700: #495057;
      --gray-800: #343a40;
      --gray-900: #212529;
      --border-radius: 0.5rem;
      --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
      --transition: all 0.3s ease-in-out;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: var(--gray-800);
      background-color: var(--gray-100);
    }

    header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      text-align: center;
    }

    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      background-color: white;
      box-shadow: var(--box-shadow);
    }

    nav a {
      color: var(--gray-700);
      text-decoration: none;
      margin: 0 1rem;
      font-weight: 500;
      transition: var(--transition);
    }

    nav a:hover {
      color: var(--primary-color);
    }

    main {
      max-width: 1200px;
      margin: 2rem auto;
      padding: 0 1rem;
    }

    .site-details {
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    @media (min-width: 768px) {
      .site-details {
        grid-template-columns: 1fr 1fr;
      }
    }

    .site-image {
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
    }

    .site-image img {
      width: 100%;
      height: auto;
      display: block;
    }

    .site-info h1 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .site-info p {
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }

    .site-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .meta-item {
      display: flex;
      align-items: center;
      background-color: var(--gray-200);
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
    }

    .meta-item i {
      margin-right: 0.5rem;
      color: var(--secondary-color);
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      margin-top: 1.5rem;
    }

    .action-buttons button {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      background-color: var(--secondary-color);
      color: white;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
      display: flex;
      align-items: center;
    }

    .action-buttons button i {
      margin-right: 0.5rem;
    }

    .action-buttons button:hover {
      background-color: var(--primary-color);
    }

    .site-description {
      margin-bottom: 2rem;
    }

    .site-description h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .map-container {
      height: 400px;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      margin-bottom: 2rem;
    }

    .nearby-attractions {
      margin-bottom: 2rem;
    }

    .nearby-attractions h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .attractions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .attraction-card {
      background-color: white;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
    }

    .attraction-card:hover {
      transform: translateY(-5px);
    }

    .attraction-card img {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }

    .attraction-card-content {
      padding: 1rem;
    }

    .attraction-card-content h3 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    footer {
      background-color: var(--primary-color);
      color: white;
      text-align: center;
      padding: 1rem;
      margin-top: 2rem;
    }

    #backToTop {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      width: 3rem;
      height: 3rem;
      background-color: var(--primary-color);
      color: white;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
      display: none;
    }

    #backToTop:hover {
      background-color: var(--secondary-color);
    }

    /* Travel Information Section */
    .travel-info {
      margin-bottom: 2rem;
      background-color: white;
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      overflow: hidden;
    }

    .travel-info-header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .travel-info-header h2 {
      margin: 0;
      font-size: 1.5rem;
    }

    .travel-info-tabs {
      display: flex;
      background-color: var(--gray-200);
    }

    .travel-tab {
      padding: 1rem;
      cursor: pointer;
      flex: 1;
      text-align: center;
      font-weight: 500;
      transition: var(--transition);
      border-bottom: 3px solid transparent;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .travel-tab i {
      margin-right: 0.5rem;
    }

    .travel-tab.active {
      background-color: white;
      border-bottom-color: var(--accent-color);
    }

    .travel-tab:hover:not(.active) {
      background-color: var(--gray-300);
    }

    .travel-content {
      padding: 1.5rem;
    }

    .travel-panel {
      display: none;
    }

    .travel-panel.active {
      display: block;
    }

    /* Weather Panel */
    .weather-info {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      align-items: center;
    }

    .weather-current {
      display: flex;
      align-items: center;
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      flex: 1;
      min-width: 250px;
    }

    .weather-icon {
      font-size: 3rem;
      margin-right: 1rem;
      color: var(--secondary-color);
    }

    .weather-details h3 {
      margin: 0;
      color: var(--primary-color);
    }

    .weather-temp {
      font-size: 2rem;
      font-weight: bold;
    }

    .weather-desc {
      color: var(--gray-700);
    }

    .weather-forecast {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      flex: 2;
      min-width: 300px;
    }

    .forecast-day {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      text-align: center;
      flex: 1;
      min-width: 100px;
    }

    .forecast-day h4 {
      margin: 0 0 0.5rem 0;
      color: var(--primary-color);
    }

    .forecast-icon {
      font-size: 1.5rem;
      margin: 0.5rem 0;
      color: var(--secondary-color);
    }

    /* Transportation Panel */
    .transport-options {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .transport-option {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
      display: flex;
      flex-direction: column;
    }

    .transport-option h3 {
      display: flex;
      align-items: center;
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .transport-option h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .transport-option p {
      margin-bottom: 0.5rem;
    }

    .transport-option .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    /* Hours & Tickets Panel */
    .hours-container {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .opening-hours, .ticket-info {
      flex: 1;
      min-width: 250px;
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .opening-hours h3, .ticket-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .opening-hours h3 i, .ticket-info h3 i {
      margin-right: 0.5rem;
    }

    .hours-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .hours-list li {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .hours-list li:last-child {
      border-bottom: none;
    }

    .day {
      font-weight: 500;
    }

    .ticket-type {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .ticket-type:last-child {
      border-bottom: none;
    }

    .ticket-price {
      font-weight: bold;
    }

    /* Amenities Panel */
    .amenities-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .amenity-card {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .amenity-card h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .amenity-card h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .amenity-card p {
      margin-bottom: 0.5rem;
    }

    .amenity-card .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    .amenity-card .rating {
      color: var(--warning-color);
      margin-bottom: 0.5rem;
    }

    .weather-tips h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .local-transport h3, .best-time h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>Heritage Explorer</h1>
    <p>Discover Tamil Nadu's rich heritage and culture</p>
  </header>

  <nav>
    <div>
      <a href="website.html"><i class="fas fa-home"></i> Home</a>
      <a href="#"><i class="fas fa-map-marked-alt"></i> Explore</a>
      <a href="#"><i class="fas fa-info-circle"></i> About</a>
    </div>
    <div>
      <a href="#" id="toggleLang">தமிழ்</a>
    </div>
  </nav>

  <main>
    <div class="site-details">
      <div class="site-image">
        <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQrcOZb8u7CS79cqnxN87Pic9wFmyRXeVB_fA&s" alt="Kodaikanal">
      </div>
      <div class="site-info">
        <h1>Kodaikanal</h1>
        <div class="site-meta">
          <div class="meta-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>Dindigul, Tamil Nadu</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-mountain"></i>
            <span>Hill Station</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-ruler-vertical"></i>
            <span>2,133 meters above sea level</span>
          </div>
        </div>
        <p>Kodaikanal, known as the "Princess of Hill Stations," is a beautiful hill retreat in the Dindigul district of Tamil Nadu. Famous for its star-shaped lake, lush forests, and misty landscapes, it offers a perfect escape from the summer heat.</p>
        <div class="action-buttons">
          <button id="playAudioButton"><i class="fas fa-volume-up"></i> Listen</button>
          <button id="favoriteButton"><i class="far fa-heart"></i> Favorite</button>
          <button id="shareButton"><i class="fas fa-share-alt"></i> Share</button>
        </div>
      </div>
    </div>

    <div class="site-description">
      <h2>About Kodaikanal</h2>
      <p>Kodaikanal, often referred to as the "Gift of the Forest" in Tamil, is a hill station situated in the Palani Hills of the Western Ghats. Located at an altitude of 2,133 meters above sea level, it enjoys a pleasant climate throughout the year, with temperatures rarely exceeding 20°C in summer or dropping below 8°C in winter.</p>
      <p>The hill station was established in 1845 as a refuge from the high temperatures and tropical diseases of the plains. It was primarily developed by American missionaries and British bureaucrats who were seeking a place similar to the climate of their homeland. The town still retains much of its colonial charm with old stone buildings, churches, and well-planned roads.</p>
      <p>The centerpiece of Kodaikanal is its star-shaped artificial lake, created in 1863. Spread over 60 acres, the lake is surrounded by a 5 km walking path (Bryant Park Road) that offers beautiful views and is a popular spot for boating and cycling. The town is also known for its rich biodiversity, with numerous endemic plant species found in the surrounding shola forests.</p>
      <p>Kodaikanal offers various attractions for nature lovers and adventure enthusiasts, including trekking trails, viewpoints offering panoramic vistas, waterfalls, and botanical gardens. The town is also famous for its homemade chocolates, aromatic oils, and the Kurinji flower that blooms once every 12 years, covering the hills in a carpet of blue.</p>
    </div>

    <div class="map-container">
      <!-- Map will be embedded here -->
      <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d31476.62311012707!2d77.47351687910156!3d10.2381!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3b0766939f7d9f2f%3A0xc222f3d5e2fcd4b!2sKodaikanal%2C%20Tamil%20Nadu!5e0!3m2!1sen!2sin!4v1652345678901!5m2!1sen!2sin" width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy" title="Map of Kodaikanal"></iframe>
    </div>

    <!-- Travel Information Section -->
    <div class="travel-info">
      <div class="travel-info-header">
        <h2>Practical Travel Information</h2>
        <div class="last-updated">Last updated: <span id="lastUpdated">Today</span></div>
      </div>

      <div class="travel-info-tabs">
        <div class="travel-tab active" data-tab="weather">
          <i class="fas fa-cloud-sun"></i> Weather
        </div>
        <div class="travel-tab" data-tab="transport">
          <i class="fas fa-bus"></i> Transportation
        </div>
        <div class="travel-tab" data-tab="hours">
          <i class="far fa-clock"></i> Hours & Tickets
        </div>
        <div class="travel-tab" data-tab="amenities">
          <i class="fas fa-concierge-bell"></i> Nearby Amenities
        </div>
      </div>

      <div class="travel-content">
        <!-- Weather Panel -->
        <div class="travel-panel active" id="weatherPanel">
          <div class="weather-info">
            <div class="weather-current">
              <div class="weather-icon">
                <i class="fas fa-cloud"></i>
              </div>
              <div class="weather-details">
                <h3>Current Weather</h3>
                <div class="weather-temp">18°C</div>
                <div class="weather-desc">Partly Cloudy</div>
                <div class="weather-extra">Humidity: 75% | Wind: 15 km/h</div>
              </div>
            </div>

            <div class="weather-forecast">
              <div class="forecast-day">
                <h4>Tomorrow</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun"></i></div>
                <div class="forecast-temp">19°C</div>
                <div class="forecast-desc">Mostly Sunny</div>
              </div>
              <div class="forecast-day">
                <h4>Wed</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-rain"></i></div>
                <div class="forecast-temp">16°C</div>
                <div class="forecast-desc">Light Rain</div>
              </div>
              <div class="forecast-day">
                <h4>Thu</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun"></i></div>
                <div class="forecast-temp">17°C</div>
                <div class="forecast-desc">Partly Cloudy</div>
              </div>
            </div>
          </div>

          <div class="weather-tips">
            <h3>Weather Tips</h3>
            <p>Kodaikanal has a cool and pleasant climate throughout the year. The best time to visit is from April to June when the weather is warm and dry. Monsoon season (July-September) brings heavy rainfall, making outdoor activities challenging. Winter (October-March) can be quite cold with temperatures dropping to 8°C or lower, especially at night. Always carry a light jacket or sweater even in summer as evenings can be cool. During monsoon, waterproof clothing and proper footwear are essential. The weather in hill stations can change rapidly, so it's advisable to check the forecast before planning outdoor activities.</p>
          </div>
        </div>

        <!-- Transportation Panel -->
        <div class="travel-panel" id="transportPanel">
          <div class="transport-options">
            <div class="transport-option">
              <h3><i class="fas fa-plane"></i> By Air</h3>
              <p>Madurai Airport is the nearest airport to Kodaikanal.</p>
              <p class="distance">120 km from Kodaikanal (approx. 3 hours by car)</p>
              <p>Regular flights from Chennai, Bangalore, and other major cities.</p>
              <p>Taxi services available from the airport to Kodaikanal (₹2,500-3,500).</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-train"></i> By Train</h3>
              <p>Kodai Road Railway Station is the nearest railhead.</p>
              <p class="distance">80 km from Kodaikanal (approx. 2 hours by car)</p>
              <p>Well-connected to Chennai, Madurai, Coimbatore, and other cities.</p>
              <p>Taxis and buses available from the station to Kodaikanal.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-bus"></i> By Bus</h3>
              <p>Regular state-run and private buses operate to Kodaikanal.</p>
              <p>Direct buses from Chennai (12 hours), Madurai (4 hours), Coimbatore (7 hours).</p>
              <p>Both AC and non-AC buses available with varying comfort levels.</p>
              <p>The bus stand is located in the town center, walking distance from most hotels.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-car"></i> By Car</h3>
              <p>Well-connected by roads from major cities in Tamil Nadu.</p>
              <p>From Chennai: NH-45 and NH-83 (520 km, approx. 10 hours)</p>
              <p>From Madurai: NH-44 and Kodaikanal Road (115 km, approx. 3 hours)</p>
              <p>The ghat road to Kodaikanal has 21 hairpin bends and requires careful driving.</p>
            </div>
          </div>

          <div class="local-transport">
            <h3>Local Transportation</h3>
            <p>Within Kodaikanal, auto-rickshaws are the primary mode of transportation. Most charge a fixed rate rather than by meter, so negotiate the fare before starting your journey. Taxis can be hired for sightseeing packages, typically ranging from ₹1,500-2,500 for a half-day tour. Bicycles are available for rent (₹100-200 per day) and are a great way to explore the lake area. For the more adventurous, walking is an excellent option as many attractions are within 2-3 km of the town center. Some hotels also offer shuttle services to popular attractions.</p>
          </div>
        </div>

        <!-- Hours & Tickets Panel -->
        <div class="travel-panel" id="hoursPanel">
          <div class="hours-container">
            <div class="opening-hours">
              <h3><i class="far fa-clock"></i> Opening Hours</h3>
              <ul class="hours-list">
                <li><span class="day">Kodaikanal Lake:</span> <span class="time">6:00 AM - 6:00 PM</span></li>
                <li><span class="day">Bryant Park:</span> <span class="time">9:00 AM - 6:00 PM</span></li>
                <li><span class="day">Pillar Rocks:</span> <span class="time">7:00 AM - 6:00 PM</span></li>
                <li><span class="day">Coaker's Walk:</span> <span class="time">7:00 AM - 7:00 PM</span></li>
                <li><span class="day">Silver Cascade Falls:</span> <span class="time">24 hours (best visited during daylight)</span></li>
              </ul>
              <p class="hours-note">Most natural attractions are open throughout the year, but some viewpoints may be closed during heavy fog or monsoon season for safety reasons. It's advisable to visit viewpoints in the morning before clouds and mist obstruct the views.</p>
            </div>

            <div class="ticket-info">
              <h3><i class="fas fa-ticket-alt"></i> Ticket Information</h3>
              <div class="ticket-type">
                <span>Bryant Park:</span>
                <span class="ticket-price">₹30 per person</span>
              </div>
              <div class="ticket-type">
                <span>Kodaikanal Lake (Boating):</span>
                <span class="ticket-price">₹50-₹150 depending on boat type</span>
              </div>
              <div class="ticket-type">
                <span>Coaker's Walk:</span>
                <span class="ticket-price">₹10 per person</span>
              </div>
              <div class="ticket-type">
                <span>Camera Fee (varies by location):</span>
                <span class="ticket-price">₹30-₹50</span>
              </div>
              <p class="ticket-note">Most natural viewpoints have a small entry fee or parking fee. Some attractions offer discounts for children and students with valid ID. Credit cards are accepted at major attractions, but it's advisable to carry cash for smaller sites.</p>
            </div>
          </div>

          <div class="best-time">
            <h3>Best Time to Visit</h3>
            <p>The best time to visit Kodaikanal is from April to June when the weather is pleasant and ideal for outdoor activities. The temperature ranges from 20°C to 35°C during this period. September to March is also a good time, though it can get quite cold in December and January. Monsoon season (July-August) brings heavy rainfall, making some outdoor activities difficult, but the landscape becomes lush and waterfalls are at their full glory. Weekends and public holidays see a significant increase in tourists, so weekdays are recommended for a more peaceful experience. Early mornings (6:00 AM - 9:00 AM) are ideal for visiting viewpoints to avoid mist and clouds that often form later in the day.</p>
          </div>
        </div>

        <!-- Amenities Panel -->
        <div class="travel-panel" id="amenitiesPanel">
          <div class="amenities-grid">
            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>The Carlton</strong></p>
              <p class="distance">1.5 km from town center</p>
              <p class="rating">★★★★★ (4.6/5)</p>
              <p>Luxury heritage hotel with colonial architecture and modern amenities.</p>
              <p>Price range: ₹8,000 - ₹15,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Sterling Kodai Lake</strong></p>
              <p class="distance">0.5 km from Kodaikanal Lake</p>
              <p class="rating">★★★★☆ (4.2/5)</p>
              <p>Mid-range resort with lake views and family-friendly amenities.</p>
              <p>Price range: ₹4,000 - ₹7,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Zostel Kodaikanal</strong></p>
              <p class="distance">2 km from town center</p>
              <p class="rating">★★★★☆ (4.3/5)</p>
              <p>Budget-friendly hostel with dormitories and private rooms.</p>
              <p>Price range: ₹600 - ₹2,500 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Altaf's Cafe</strong></p>
              <p class="distance">1 km from Kodaikanal Lake</p>
              <p class="rating">★★★★★ (4.5/5)</p>
              <p>Popular for its Middle Eastern cuisine and lake views.</p>
              <p>Price range: ₹500 - ₹800 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Cloud Street</strong></p>
              <p class="distance">0.8 km from town center</p>
              <p class="rating">★★★★☆ (4.3/5)</p>
              <p>Cozy cafe serving continental breakfast and wood-fired pizzas.</p>
              <p>Price range: ₹600 - ₹1,000 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-shopping-bag"></i> Shopping</h3>
              <p><strong>Kodaikanal Chocolate Factory</strong></p>
              <p class="distance">1.2 km from town center</p>
              <p>Famous for homemade chocolates in various flavors.</p>
              <p>Must-buy: Eucalyptus chocolate, fruit-flavored chocolates</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-first-aid"></i> Medical Facilities</h3>
              <p><strong>Van Allen Hospital</strong></p>
              <p class="distance">1 km from town center</p>
              <p>Well-equipped hospital with emergency services.</p>
              <p>Contact: +91 4542 241 324</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-info-circle"></i> Tourist Information</h3>
              <p><strong>Tamil Nadu Tourism Office</strong></p>
              <p class="distance">Near Kodaikanal Lake</p>
              <p>Provides maps, brochures, and guided tour information.</p>
              <p>Contact: +91 4542 241 675</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="nearby-attractions">
      <h2>Nearby Attractions</h2>
      <div class="attractions-grid">
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Kodaikanal_Lake_20190212182003.jpg" alt="Kodaikanal Lake">
          <div class="attraction-card-content">
            <h3>Kodaikanal Lake</h3>
            <p>A star-shaped artificial lake at the heart of the town, offering boating facilities and a scenic 5 km walking path around its perimeter.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Pillar_Rocks_20190212182004.jpg" alt="Pillar Rocks">
          <div class="attraction-card-content">
            <h3>Pillar Rocks</h3>
            <p>Three massive granite pillars standing 400 feet high, offering breathtaking views of the surrounding valleys.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Bryant_Park_20190212182005.jpg" alt="Bryant Park">
          <div class="attraction-card-content">
            <h3>Bryant Park</h3>
            <p>A beautifully maintained botanical garden with a wide variety of flowers, plants, and a glasshouse with rare plant species.</p>
          </div>
        </div>
      </div>
    </div>
  </main>

  <footer>
    <p>&copy; 2025 Heritage Explorer | Explore Tamil Nadu</p>
  </footer>

  <div id="backToTop">
    <i class="fas fa-arrow-up"></i>
  </div>

  <script>
    // Back to Top Button
    const backToTop = document.getElementById("backToTop");
    window.addEventListener("scroll", () => {
      if (window.scrollY > 300) {
        backToTop.style.display = "flex";
      } else {
        backToTop.style.display = "none";
      }
    });
    backToTop.addEventListener("click", () => {
      window.scrollTo({ top: 0, behavior: "smooth" });
    });

    // Audio Playback
    document.getElementById("playAudioButton").addEventListener("click", () => {
      const description = document.querySelector(".site-info p").textContent;
      const utterance = new SpeechSynthesisUtterance(description);
      utterance.lang = "en-US";
      speechSynthesis.speak(utterance);
    });

    // Favorite Button
    document.getElementById("favoriteButton").addEventListener("click", function() {
      const icon = this.querySelector("i");
      if (icon.classList.contains("far")) {
        icon.classList.remove("far");
        icon.classList.add("fas");
        alert("Added to favorites!");
      } else {
        icon.classList.remove("fas");
        icon.classList.add("far");
        alert("Removed from favorites!");
      }
    });

    // Share Button
    document.getElementById("shareButton").addEventListener("click", () => {
      const shareData = {
        title: "Kodaikanal - Heritage Explorer",
        text: "Check out this beautiful hill station in Tamil Nadu!",
        url: window.location.href
      };

      if (navigator.share) {
        navigator.share(shareData)
          .then(() => console.log("Shared successfully"))
          .catch((error) => console.error("Error sharing:", error));
      } else {
        navigator.clipboard.writeText(shareData.url)
          .then(() => alert("URL copied to clipboard"))
          .catch((err) => console.error("Could not copy text: ", err));
      }
    });

    // Initialize the travel info tabs
    document.addEventListener("DOMContentLoaded", function() {
      // Set the last updated date
      const today = new Date();
      document.getElementById("lastUpdated").textContent = today.toLocaleDateString();

      // Tab functionality
      const tabs = document.querySelectorAll(".travel-tab");
      tabs.forEach(tab => {
        tab.addEventListener("click", function() {
          // Remove active class from all tabs
          tabs.forEach(t => t.classList.remove("active"));

          // Add active class to clicked tab
          this.classList.add("active");

          // Hide all panels
          const panels = document.querySelectorAll(".travel-panel");
          panels.forEach(panel => panel.classList.remove("active"));

          // Show the corresponding panel
          const tabId = this.getAttribute("data-tab");
          document.getElementById(tabId + "Panel").classList.add("active");
        });
      });

      // Fetch weather data (mock)
      fetchWeatherData();
    });

    // Weather API Integration (Mock)
    function fetchWeatherData() {
      // Coordinates for Kodaikanal
      const lat = 10.2381;
      const lon = 77.4892;

      // Mock weather data
      const mockWeatherData = {
        current: {
          temp: 18,
          humidity: 75,
          wind_speed: 15,
          weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
        },
        daily: [
          {
            dt: Date.now() + 86400000, // Tomorrow
            temp: { day: 19 },
            weather: [{ main: "Clear", description: "Mostly Sunny", icon: "01d" }]
          },
          {
            dt: Date.now() + 172800000, // Day after tomorrow
            temp: { day: 16 },
            weather: [{ main: "Rain", description: "Light Rain", icon: "10d" }]
          },
          {
            dt: Date.now() + 259200000, // 3 days from now
            temp: { day: 17 },
            weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
          }
        ]
      };

      updateWeatherUI(mockWeatherData);
    }

    function updateWeatherUI(data) {
      // Update current weather
      document.querySelector(".weather-temp").textContent = `${data.current.temp}°C`;
      document.querySelector(".weather-desc").textContent = data.current.weather[0].description;
      document.querySelector(".weather-extra").textContent = `Humidity: ${data.current.humidity}% | Wind: ${data.current.wind_speed} km/h`;

      // Update weather icon
      const weatherIconElement = document.querySelector(".weather-icon i");
      updateWeatherIcon(weatherIconElement, data.current.weather[0].main);

      // Update forecast
      const forecastDays = document.querySelectorAll(".forecast-day");
      data.daily.forEach((day, index) => {
        if (index < forecastDays.length) {
          const dayElement = forecastDays[index];
          const date = new Date(day.dt);
          const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });

          dayElement.querySelector("h4").textContent = index === 0 ? "Tomorrow" : dayName;
          dayElement.querySelector(".forecast-temp").textContent = `${day.temp.day}°C`;
          dayElement.querySelector(".forecast-desc").textContent = day.weather[0].description;

          // Update forecast icon
          const iconElement = dayElement.querySelector(".forecast-icon i");
          updateWeatherIcon(iconElement, day.weather[0].main);
        }
      });
    }

    function updateWeatherIcon(iconElement, weatherType) {
      // Remove all existing weather classes
      iconElement.className = "";

      // Add the appropriate weather icon class
      switch (weatherType) {
        case "Clear":
          iconElement.classList.add("fas", "fa-sun");
          break;
        case "Clouds":
          iconElement.classList.add("fas", "fa-cloud");
          break;
        case "Rain":
        case "Drizzle":
          iconElement.classList.add("fas", "fa-cloud-rain");
          break;
        case "Thunderstorm":
          iconElement.classList.add("fas", "fa-bolt");
          break;
        case "Snow":
          iconElement.classList.add("fas", "fa-snowflake");
          break;
        case "Mist":
        case "Fog":
        case "Haze":
          iconElement.classList.add("fas", "fa-smog");
          break;
        default:
          iconElement.classList.add("fas", "fa-cloud-sun");
      }
    }
  </script>

  <!-- Language Toggle Script -->
  <script src="language-toggle.js"></script>
</body>
</html>
