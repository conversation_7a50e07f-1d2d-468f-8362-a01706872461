# Heritage Explorer - Authentication System Setup Guide

## Overview
This guide will help you set up the login and signup system for the Heritage Explorer website with MySQL database integration.

## Prerequisites
- XAMPP or WAMP server (includes Apache, MySQL, and PHP)
- Web browser
- Text editor (optional)

## Files Created
1. **config.php** - Database configuration and connection
2. **auth.php** - Backend authentication logic
3. **login.html** - Login page
4. **signup.html** - Signup page  
5. **auth.js** - Frontend JavaScript for authentication
6. **database.sql** - Database schema and sample data

## Setup Instructions

### Step 1: Install XAMPP/WAMP
1. Download and install XAMPP from https://www.apachefriends.org/
2. Start Apache and MySQL services from the XAMPP Control Panel

### Step 2: Database Setup
1. Open phpMyAdmin by going to `http://localhost/phpmyadmin`
2. Create a new database named `tourism_db`
3. Import the `database.sql` file or run the SQL commands manually:
   ```sql
   -- Run the contents of database.sql file
   ```

### Step 3: Configure Database Connection
The database configuration is already set in `config.php`:
- **Host**: localhost
- **Port**: 3306
- **Database**: tourism_db
- **Username**: root
- **Password**: Devmanoj@3010

### Step 4: File Placement
Place all files in your web server directory:
- For XAMPP: `C:\xampp\htdocs\heritage-explorer\`
- For WAMP: `C:\wamp64\www\heritage-explorer\`

### Step 5: Test the Setup
1. Open your web browser
2. Navigate to `http://localhost/heritage-explorer/website.html`
3. You should see Login and Sign Up buttons in the header
4. Test the signup process by creating a new account
5. Test the login process with your new account

## Features

### User Registration
- Full name, username, email, phone (optional)
- Date of birth and gender (optional)
- Password strength validation
- Real-time username/email availability checking
- Terms and conditions agreement

### User Login
- Login with username or email
- Password validation
- Remember me option
- Session management

### Security Features
- Password hashing using PHP's `password_hash()`
- CSRF token protection
- SQL injection prevention using prepared statements
- Session timeout management
- Input validation and sanitization

### Database Tables
1. **users** - User account information
2. **user_sessions** - Active user sessions
3. **user_favorites** - User's favorite heritage sites
4. **user_reviews** - User reviews for sites
5. **user_visit_history** - Track user visits
6. **user_preferences** - User settings and preferences
7. **heritage_sites** - Reference data for heritage sites

## Usage

### For Users
1. **Sign Up**: Click "Sign Up" button, fill the form, and create account
2. **Login**: Click "Login" button, enter credentials
3. **Logout**: Click the logout button when logged in
4. **Favorites**: Save favorite heritage sites (requires login)
5. **Reviews**: Write reviews for heritage sites (requires login)

### For Developers
1. **Check Login Status**: Use `checkUserSession()` function
2. **Get User Info**: Access user data from session
3. **Logout**: Call `logout()` function
4. **Add Features**: Extend the authentication system as needed

## API Endpoints

### POST /auth.php
- `action=signup` - Create new user account
- `action=login` - Authenticate user
- `action=logout` - End user session
- `action=check_session` - Verify current session

### GET /auth.php
- `action=check_username&username=...` - Check username availability
- `action=check_email&email=...` - Check email availability

## Troubleshooting

### Common Issues
1. **Database Connection Failed**
   - Check if MySQL service is running
   - Verify database credentials in config.php
   - Ensure tourism_db database exists

2. **Login/Signup Not Working**
   - Check browser console for JavaScript errors
   - Verify PHP error logs
   - Ensure all files are in correct directory

3. **Session Issues**
   - Clear browser cookies and cache
   - Check PHP session configuration
   - Verify session table in database

### Error Messages
- "Database connection failed" - MySQL service not running
- "Invalid credentials" - Wrong username/password
- "Username already exists" - Choose different username
- "Email already registered" - Use different email

## Security Considerations
1. Change default database password in production
2. Use HTTPS in production environment
3. Implement rate limiting for login attempts
4. Regular security updates and patches
5. Monitor for suspicious activities

## Future Enhancements
1. Email verification for new accounts
2. Password reset functionality
3. Two-factor authentication
4. Social media login integration
5. User profile management
6. Admin dashboard for user management

## Support
For issues or questions:
1. Check the troubleshooting section
2. Review PHP error logs
3. Check browser console for JavaScript errors
4. Verify database structure and data

## File Structure
```
heritage-explorer/
├── config.php              # Database configuration
├── auth.php                # Authentication backend
├── auth.js                 # Authentication frontend
├── login.html              # Login page
├── signup.html             # Signup page
├── website.html            # Main website (updated)
├── database.sql            # Database schema
└── README-Authentication.md # This guide
```

## Testing Accounts
After running the database setup, you can use this admin account:
- **Username**: admin
- **Email**: <EMAIL>
- **Password**: admin123

## License
This authentication system is part of the Heritage Explorer project and follows the same license terms.
