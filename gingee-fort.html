<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gingee Fort - Heritage Explorer</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
  <style>
    :root {
      --primary-color: #1a237e;
      --secondary-color: #457b9d;
      --accent-color: #e63946;
      --light-color: #f1faee;
      --dark-color: #1d3557;
      --success-color: #2e7d32;
      --warning-color: #ff9800;
      --danger-color: #c62828;
      --gray-100: #f8f9fa;
      --gray-200: #e9ecef;
      --gray-300: #dee2e6;
      --gray-400: #ced4da;
      --gray-500: #adb5bd;
      --gray-600: #6c757d;
      --gray-700: #495057;
      --gray-800: #343a40;
      --gray-900: #212529;
      --border-radius: 0.5rem;
      --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
      --transition: all 0.3s ease-in-out;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: var(--gray-800);
      background-color: var(--gray-100);
    }

    header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      text-align: center;
    }

    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      background-color: white;
      box-shadow: var(--box-shadow);
    }

    nav a {
      color: var(--gray-700);
      text-decoration: none;
      margin: 0 1rem;
      font-weight: 500;
      transition: var(--transition);
    }

    nav a:hover {
      color: var(--primary-color);
    }

    main {
      max-width: 1200px;
      margin: 2rem auto;
      padding: 0 1rem;
    }

    .site-details {
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    @media (min-width: 768px) {
      .site-details {
        grid-template-columns: 1fr 1fr;
      }
    }

    .site-image {
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
    }

    .site-image img {
      width: 100%;
      height: auto;
      display: block;
    }

    .site-info h1 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .site-info p {
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }

    .site-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .meta-item {
      display: flex;
      align-items: center;
      background-color: var(--gray-200);
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
    }

    .meta-item i {
      margin-right: 0.5rem;
      color: var(--secondary-color);
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      margin-top: 1.5rem;
    }

    .action-buttons button {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      background-color: var(--secondary-color);
      color: white;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
      display: flex;
      align-items: center;
    }

    .action-buttons button i {
      margin-right: 0.5rem;
    }

    .action-buttons button:hover {
      background-color: var(--primary-color);
    }

    .site-description {
      margin-bottom: 2rem;
    }

    .site-description h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .map-container {
      height: 400px;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      margin-bottom: 2rem;
    }

    .nearby-attractions {
      margin-bottom: 2rem;
    }

    .nearby-attractions h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .attractions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .attraction-card {
      background-color: white;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
    }

    .attraction-card:hover {
      transform: translateY(-5px);
    }

    .attraction-card img {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }

    .attraction-card-content {
      padding: 1rem;
    }

    .attraction-card-content h3 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    footer {
      background-color: var(--primary-color);
      color: white;
      text-align: center;
      padding: 1rem;
      margin-top: 2rem;
    }

    #backToTop {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      width: 3rem;
      height: 3rem;
      background-color: var(--primary-color);
      color: white;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
      display: none;
    }

    #backToTop:hover {
      background-color: var(--secondary-color);
    }

    /* Travel Information Section */
    .travel-info {
      margin-bottom: 2rem;
      background-color: white;
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      overflow: hidden;
    }

    .travel-info-header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .travel-info-header h2 {
      margin: 0;
      font-size: 1.5rem;
    }

    .travel-info-tabs {
      display: flex;
      background-color: var(--gray-200);
    }

    .travel-tab {
      padding: 1rem;
      cursor: pointer;
      flex: 1;
      text-align: center;
      font-weight: 500;
      transition: var(--transition);
      border-bottom: 3px solid transparent;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .travel-tab i {
      margin-right: 0.5rem;
    }

    .travel-tab.active {
      background-color: white;
      border-bottom-color: var(--accent-color);
    }

    .travel-tab:hover:not(.active) {
      background-color: var(--gray-300);
    }

    .travel-content {
      padding: 1.5rem;
    }

    .travel-panel {
      display: none;
    }

    .travel-panel.active {
      display: block;
    }

    /* Weather Panel */
    .weather-info {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      align-items: center;
    }

    .weather-current {
      display: flex;
      align-items: center;
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      flex: 1;
      min-width: 250px;
    }

    .weather-icon {
      font-size: 3rem;
      margin-right: 1rem;
      color: var(--secondary-color);
    }

    .weather-details h3 {
      margin: 0;
      color: var(--primary-color);
    }

    .weather-temp {
      font-size: 2rem;
      font-weight: bold;
    }

    .weather-desc {
      color: var(--gray-700);
    }

    .weather-forecast {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      flex: 2;
      min-width: 300px;
    }

    .forecast-day {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      text-align: center;
      flex: 1;
      min-width: 100px;
    }

    .forecast-day h4 {
      margin: 0 0 0.5rem 0;
      color: var(--primary-color);
    }

    .forecast-icon {
      font-size: 1.5rem;
      margin: 0.5rem 0;
      color: var(--secondary-color);
    }

    /* Transportation Panel */
    .transport-options {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .transport-option {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
      display: flex;
      flex-direction: column;
    }

    .transport-option h3 {
      display: flex;
      align-items: center;
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .transport-option h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .transport-option p {
      margin-bottom: 0.5rem;
    }

    .transport-option .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    /* Hours & Tickets Panel */
    .hours-container {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .opening-hours, .ticket-info {
      flex: 1;
      min-width: 250px;
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .opening-hours h3, .ticket-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .opening-hours h3 i, .ticket-info h3 i {
      margin-right: 0.5rem;
    }

    .hours-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .hours-list li {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .hours-list li:last-child {
      border-bottom: none;
    }

    .day {
      font-weight: 500;
    }

    .ticket-type {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .ticket-type:last-child {
      border-bottom: none;
    }

    .ticket-price {
      font-weight: bold;
    }

    /* Amenities Panel */
    .amenities-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .amenity-card {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .amenity-card h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .amenity-card h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .amenity-card p {
      margin-bottom: 0.5rem;
    }

    .amenity-card .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    .amenity-card .rating {
      color: var(--warning-color);
      margin-bottom: 0.5rem;
    }

    .weather-tips h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .local-transport h3, .best-time h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>Heritage Explorer</h1>
    <p>Discover Tamil Nadu's rich heritage and culture</p>
  </header>

  <nav>
    <div>
      <a href="website.html"><i class="fas fa-home"></i> Home</a>
      <a href="#"><i class="fas fa-map-marked-alt"></i> Explore</a>
      <a href="#"><i class="fas fa-info-circle"></i> About</a>
    </div>
    <div>
      <a href="#" id="toggleLang">தமிழ்</a>
    </div>
  </nav>

  <main>
    <div class="site-details">
      <div class="site-image">
        <img src="https://thumbs.dreamstime.com/b/gingee-fort-11006155.jpg" alt="Gingee Fort">
      </div>
      <div class="site-info">
        <h1>Gingee Fort</h1>
        <div class="site-meta">
          <div class="meta-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>Villupuram, Tamil Nadu</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-monument"></i>
            <span>Monument</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-calendar-alt"></i>
            <span>Built in 9th century CE</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-mountain"></i>
            <span>Three Hill Fortress</span>
          </div>
        </div>
        <p>Gingee Fort, known as the "Troy of the East," is a magnificent 9th-century fortress spanning three hills in Villupuram district. This impregnable fort complex features impressive defensive structures, temples, granaries, and water systems, showcasing the military architecture and engineering skills of various dynasties that ruled the region.</p>
        <div class="action-buttons">
          <button id="playAudioButton"><i class="fas fa-volume-up"></i> Listen</button>
          <button id="favoriteButton"><i class="far fa-heart"></i> Favorite</button>
          <button id="shareButton"><i class="fas fa-share-alt"></i> Share</button>
        </div>
      </div>
    </div>

    <div class="site-description">
      <h2>About Gingee Fort</h2>
      <p>Gingee Fort, also known as Senji Fort or Chenji Fort, is a remarkable fortification located in the Villupuram district of Tamil Nadu. Often referred to as the "Troy of the East" by the British, this imposing fortress is spread across three hills: Rajagiri, Krishnagiri, and Chandrayandurg, forming a triangular layout. The fort's origins date back to the 9th century, with significant expansions and modifications made by various dynasties over the centuries, including the Cholas, Vijayanagara Empire, Marathas, Bijapur Sultanate, Mughals, French, and British.</p>
      <p>The fort's strategic importance stems from its unique design and natural defenses. The three hills are connected by walls and fortifications, creating an almost impregnable fortress. The main citadel is situated on Rajagiri hill, which rises to a height of about 800 feet and is accessible only through a narrow, winding path protected by multiple gateways and defensive structures. The fort complex includes numerous buildings such as temples, granaries, stables, ammunition stores, and living quarters, showcasing the military architecture and engineering skills of its builders.</p>
      <p>One of the most impressive features of Gingee Fort is its water management system. The fort has several ponds, wells, and underground water storage facilities that ensured a continuous water supply during sieges. The Kalyana Mahal (marriage hall) with its distinctive pyramid-shaped tower is a notable structure within the fort complex. Other significant structures include the Ranganatha Temple, Venkataramana Temple, Sadatulla Khan's Tomb, and the Elephant Tank. The fort also houses a museum that displays artifacts and provides information about its rich history.</p>
      <p>Despite its formidable defenses, Gingee Fort changed hands multiple times throughout history. It was captured by the Marathas under Shivaji in 1677, who strengthened its fortifications. Later, it was controlled by the Mughals, the French, and finally the British. Today, the fort stands as a protected monument under the Archaeological Survey of India and attracts history enthusiasts, trekkers, and tourists who come to explore its extensive ruins and enjoy the panoramic views from its hilltops. The fort's historical significance, architectural grandeur, and natural setting make it one of the most impressive fortifications in South India.</p>
    </div>

    <div class="map-container">
      <!-- Map will be embedded here -->
      <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3919.0123456789!2d79.40!3d12.253!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3a52c5e8e2b3b6a7%3A0x1c9c4c5c8e5e5e5e!2sGingee%20Fort!5e0!3m2!1sen!2sin!4v1652345678901!5m2!1sen!2sin" width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy" title="Map of Gingee Fort"></iframe>
    </div>

    <!-- Travel Information Section -->
    <div class="travel-info">
      <div class="travel-info-header">
        <h2>Practical Travel Information</h2>
        <div class="last-updated">Last updated: <span id="lastUpdated">Today</span></div>
      </div>

      <div class="travel-info-tabs">
        <div class="travel-tab active" data-tab="weather">
          <i class="fas fa-cloud-sun"></i> Weather
        </div>
        <div class="travel-tab" data-tab="transport">
          <i class="fas fa-bus"></i> Transportation
        </div>
        <div class="travel-tab" data-tab="hours">
          <i class="far fa-clock"></i> Hours & Tickets
        </div>
        <div class="travel-tab" data-tab="amenities">
          <i class="fas fa-concierge-bell"></i> Nearby Amenities
        </div>
      </div>

      <div class="travel-content">
        <!-- Weather Panel -->
        <div class="travel-panel active" id="weatherPanel">
          <div class="weather-info">
            <div class="weather-current">
              <div class="weather-icon">
                <i class="fas fa-sun"></i>
              </div>
              <div class="weather-details">
                <h3>Current Weather</h3>
                <div class="weather-temp">32°C</div>
                <div class="weather-desc">Sunny</div>
                <div class="weather-extra">Humidity: 65% | Wind: 8 km/h</div>
              </div>
            </div>

            <div class="weather-forecast">
              <div class="forecast-day">
                <h4>Tomorrow</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun"></i></div>
                <div class="forecast-temp">31°C</div>
                <div class="forecast-desc">Partly Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Wed</h4>
                <div class="forecast-icon"><i class="fas fa-cloud"></i></div>
                <div class="forecast-temp">30°C</div>
                <div class="forecast-desc">Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Thu</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun-rain"></i></div>
                <div class="forecast-temp">29°C</div>
                <div class="forecast-desc">Light Rain</div>
              </div>
            </div>
          </div>

          <div class="weather-tips">
            <h3>Weather Tips</h3>
            <p>Gingee experiences a tropical climate with temperatures ranging from 20°C to 38°C throughout the year. The best time to visit is from October to March when the weather is relatively cooler and pleasant. Summer months (April-June) can be extremely hot with temperatures often exceeding 35°C, making it challenging to explore the fort complex which requires significant walking and climbing.</p>
            <p>The monsoon season (July-September) brings moderate rainfall, which can make the stone pathways slippery and potentially dangerous for climbing the hills. If visiting during this period, wear shoes with good grip and carry rain protection. For any season, it's advisable to carry sufficient water, wear a hat or cap, apply sunscreen, and wear comfortable walking shoes as exploring the fort involves climbing steep paths and uneven terrain. Early morning (7:00 AM - 10:00 AM) is the best time to start your exploration to avoid the midday heat.</p>
          </div>
        </div>

        <!-- Transportation Panel -->
        <div class="travel-panel" id="transportPanel">
          <div class="transport-options">
            <div class="transport-option">
              <h3><i class="fas fa-plane"></i> By Air</h3>
              <p>Chennai International Airport is the nearest major airport.</p>
              <p class="distance">160 km from Gingee (approx. 3.5 hours by car)</p>
              <p>Pondicherry Airport is closer but has limited flights.</p>
              <p class="distance">70 km from Gingee (approx. 1.5 hours by car)</p>
              <p>Taxi services available from both airports (₹3,000-4,500 from Chennai).</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-train"></i> By Train</h3>
              <p>Gingee has its own railway station (Gingee/GYM).</p>
              <p class="distance">5 km from the fort (15 minutes by auto-rickshaw)</p>
              <p>Connected to Chennai, Villupuram, and Tiruvannamalai.</p>
              <p>Auto-rickshaws available outside the station (₹100-150 to the fort).</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-bus"></i> By Bus</h3>
              <p>Regular state-run buses operate to Gingee.</p>
              <p>Direct buses from Chennai (4 hours), Villupuram (1 hour), Tiruvannamalai (1.5 hours).</p>
              <p>The bus stand is located about 3 km from the fort entrance.</p>
              <p>Auto-rickshaws available from the bus stand to the fort (₹80-100).</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-car"></i> By Car</h3>
              <p>Well-connected by roads from major cities.</p>
              <p>From Chennai: NH-32 (160 km, approx. 3.5 hours)</p>
              <p>From Pondicherry: NH-32 (70 km, approx. 1.5 hours)</p>
              <p>Parking available near the fort entrance (₹30 for cars, ₹10 for two-wheelers).</p>
            </div>
          </div>

          <div class="local-transport">
            <h3>Local Transportation</h3>
            <p>Within Gingee town, auto-rickshaws are the primary mode of transportation. They typically charge a fixed rate rather than by meter, so negotiate the fare before starting your journey. For exploring the fort complex, which spans three hills, you'll need to walk as vehicles are not allowed inside. The main entrance leads to Rajagiri hill, and from there, you can access the other hills via connecting pathways. The complete exploration of all three hills can take 3-4 hours of walking and climbing. Some visitors choose to hire a local guide (₹300-500) who can provide historical information and show the best routes to navigate the complex fort structure.</p>
          </div>
        </div>

        <!-- Hours & Tickets Panel -->
        <div class="travel-panel" id="hoursPanel">
          <div class="hours-container">
            <div class="opening-hours">
              <h3><i class="far fa-clock"></i> Opening Hours</h3>
              <ul class="hours-list">
                <li><span class="day">All Days:</span> <span class="time">9:00 AM - 5:00 PM</span></li>
                <li><span class="day">Last Entry:</span> <span class="time">4:30 PM</span></li>
                <li><span class="day">Best Time to Visit:</span> <span class="time">Early Morning (9:00 AM - 11:00 AM)</span></li>
                <li><span class="day">Time Required:</span> <span class="time">3-4 hours (for complete exploration)</span></li>
                <li><span class="day">Closed:</span> <span class="time">No weekly closure</span></li>
              </ul>
              <p class="hours-note">The fort is open throughout the year, but visiting during the monsoon season (July-September) is not recommended due to slippery pathways. The fort complex is vast, spanning three hills, so plan to arrive early to have enough time to explore the main structures. Climbing to the top of Rajagiri hill takes approximately 30-45 minutes depending on your fitness level.</p>
            </div>

            <div class="ticket-info">
              <h3><i class="fas fa-ticket-alt"></i> Ticket Information</h3>
              <div class="ticket-type">
                <span>Indian Nationals:</span>
                <span class="ticket-price">₹25 per person</span>
              </div>
              <div class="ticket-type">
                <span>Foreign Nationals:</span>
                <span class="ticket-price">₹300 per person</span>
              </div>
              <div class="ticket-type">
                <span>Children (below 15 years):</span>
                <span class="ticket-price">Free</span>
              </div>
              <div class="ticket-type">
                <span>Camera Fee:</span>
                <span class="ticket-price">₹25 (still camera)</span>
              </div>
              <p class="ticket-note">Tickets can be purchased at the entrance gate. The ticket counter closes at 4:30 PM. Mobile phone photography is allowed without additional fees. The fort is maintained by the Archaeological Survey of India (ASI). Guides are available at the entrance (not official) and charge approximately ₹300-500 for a complete tour of the fort complex.</p>
            </div>
          </div>

          <div class="best-time">
            <h3>Best Time to Visit</h3>
            <p>The best time to visit Gingee Fort is from October to March when the weather is pleasant with temperatures ranging from 20°C to 30°C. This period offers comfortable conditions for exploring the fort complex which involves significant walking and climbing. Winter months (December-February) are particularly pleasant. Avoid visiting during the summer months (April-June) when temperatures can soar above 35°C, making the climb extremely exhausting. Early mornings are recommended for better photography and to avoid the midday heat. Weekdays are less crowded than weekends. The fort offers spectacular views during sunrise and sunset, but note that you should plan your descent before it gets dark as the pathways are not well-lit.</p>
          </div>
        </div>

        <!-- Amenities Panel -->
        <div class="travel-panel" id="amenitiesPanel">
          <div class="amenities-grid">
            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Tamil Nadu Tourism Hotel</strong></p>
              <p class="distance">3 km from fort</p>
              <p class="rating">★★★☆☆ (3.5/5)</p>
              <p>Government-run hotel with basic amenities.</p>
              <p>Price range: ₹1,000 - ₹1,500 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Hotel Gingee Palace</strong></p>
              <p class="distance">4 km from fort</p>
              <p class="rating">★★★☆☆ (3.7/5)</p>
              <p>Budget hotel with clean rooms and restaurant.</p>
              <p>Price range: ₹800 - ₹1,200 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Tiruvannamalai Hotels</strong></p>
              <p class="distance">35 km from fort</p>
              <p>Better accommodation options available in Tiruvannamalai.</p>
              <p>Many visitors prefer to stay there and make a day trip to Gingee.</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Anandha Bhavan</strong></p>
              <p class="distance">2 km from fort</p>
              <p class="rating">★★★★☆ (4.0/5)</p>
              <p>Vegetarian restaurant serving South Indian cuisine.</p>
              <p>Price range: ₹150 - ₹300 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Fort View Restaurant</strong></p>
              <p class="distance">1 km from fort</p>
              <p class="rating">★★★☆☆ (3.5/5)</p>
              <p>Small eatery serving local Tamil cuisine.</p>
              <p>Price range: ₹100 - ₹200 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-shopping-bag"></i> Shopping</h3>
              <p><strong>Local Market</strong></p>
              <p class="distance">3 km from fort</p>
              <p>Small shops selling local handicrafts and souvenirs.</p>
              <p>Limited shopping options in Gingee town.</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-first-aid"></i> Medical Facilities</h3>
              <p><strong>Government Hospital</strong></p>
              <p class="distance">4 km from fort</p>
              <p>Basic medical facilities for emergencies.</p>
              <p>For serious medical issues, Villupuram (30 km) has better facilities.</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-info-circle"></i> Tourist Information</h3>
              <p><strong>ASI Office</strong></p>
              <p class="distance">At fort entrance</p>
              <p>Limited information available at the ticket counter.</p>
              <p>No dedicated tourist information center in Gingee.</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="nearby-attractions">
      <h2>Nearby Attractions</h2>
      <div class="attractions-grid">
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Kalyana_Mahal_20190212182003.jpg" alt="Kalyana Mahal">
          <div class="attraction-card-content">
            <h3>Kalyana Mahal</h3>
            <p>A seven-story pyramid-shaped tower within the fort complex, believed to be a palace for royal women with a unique architectural style.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Venkataramana_Temple_20190212182004.jpg" alt="Venkataramana Temple">
          <div class="attraction-card-content">
            <h3>Venkataramana Temple</h3>
            <p>A beautiful temple dedicated to Lord Vishnu, featuring intricate carvings, pillared halls, and a sacred tank within the fort complex.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Anantasayanam_Lake_20190212182005.jpg" alt="Anantasayanam Lake">
          <div class="attraction-card-content">
            <h3>Anantasayanam Lake</h3>
            <p>A serene lake near the fort, offering boating facilities and a peaceful environment with views of the surrounding hills.</p>
          </div>
        </div>
      </div>
    </div>
  </main>

  <footer>
    <p>&copy; 2025 Heritage Explorer | Explore Tamil Nadu</p>
  </footer>

  <div id="backToTop">
    <i class="fas fa-arrow-up"></i>
  </div>

  <script>
    // Back to Top Button
    const backToTop = document.getElementById("backToTop");
    window.addEventListener("scroll", () => {
      if (window.scrollY > 300) {
        backToTop.style.display = "flex";
      } else {
        backToTop.style.display = "none";
      }
    });
    backToTop.addEventListener("click", () => {
      window.scrollTo({ top: 0, behavior: "smooth" });
    });

    // Audio Playback
    document.getElementById("playAudioButton").addEventListener("click", () => {
      const description = document.querySelector(".site-info p").textContent;
      const utterance = new SpeechSynthesisUtterance(description);
      utterance.lang = "en-US";
      speechSynthesis.speak(utterance);
    });

    // Favorite Button
    document.getElementById("favoriteButton").addEventListener("click", function() {
      const icon = this.querySelector("i");
      if (icon.classList.contains("far")) {
        icon.classList.remove("far");
        icon.classList.add("fas");
        alert("Added to favorites!");
      } else {
        icon.classList.remove("fas");
        icon.classList.add("far");
        alert("Removed from favorites!");
      }
    });

    // Share Button
    document.getElementById("shareButton").addEventListener("click", () => {
      const shareData = {
        title: "Gingee Fort - Heritage Explorer",
        text: "Check out this magnificent fortress in Tamil Nadu!",
        url: window.location.href
      };

      if (navigator.share) {
        navigator.share(shareData)
          .then(() => console.log("Shared successfully"))
          .catch((error) => console.error("Error sharing:", error));
      } else {
        navigator.clipboard.writeText(shareData.url)
          .then(() => alert("URL copied to clipboard"))
          .catch((err) => console.error("Could not copy text: ", err));
      }
    });

    // Initialize the travel info tabs
    document.addEventListener("DOMContentLoaded", function() {
      // Set the last updated date
      const today = new Date();
      document.getElementById("lastUpdated").textContent = today.toLocaleDateString();

      // Tab functionality
      const tabs = document.querySelectorAll(".travel-tab");
      tabs.forEach(tab => {
        tab.addEventListener("click", function() {
          // Remove active class from all tabs
          tabs.forEach(t => t.classList.remove("active"));

          // Add active class to clicked tab
          this.classList.add("active");

          // Hide all panels
          const panels = document.querySelectorAll(".travel-panel");
          panels.forEach(panel => panel.classList.remove("active"));

          // Show the corresponding panel
          const tabId = this.getAttribute("data-tab");
          document.getElementById(tabId + "Panel").classList.add("active");
        });
      });

      // Fetch weather data (mock)
      fetchWeatherData();
    });

    // Weather API Integration (Mock)
    function fetchWeatherData() {
      // Coordinates for Gingee
      const lat = 12.2530;
      const lon = 79.4000;

      // Mock weather data
      const mockWeatherData = {
        current: {
          temp: 32,
          humidity: 65,
          wind_speed: 8,
          weather: [{ main: "Clear", description: "Sunny", icon: "01d" }]
        },
        daily: [
          {
            dt: Date.now() + 86400000, // Tomorrow
            temp: { day: 31 },
            weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
          },
          {
            dt: Date.now() + 172800000, // Day after tomorrow
            temp: { day: 30 },
            weather: [{ main: "Clouds", description: "Cloudy", icon: "03d" }]
          },
          {
            dt: Date.now() + 259200000, // 3 days from now
            temp: { day: 29 },
            weather: [{ main: "Rain", description: "Light Rain", icon: "10d" }]
          }
        ]
      };

      updateWeatherUI(mockWeatherData);
    }

    function updateWeatherUI(data) {
      // Update current weather
      document.querySelector(".weather-temp").textContent = `${data.current.temp}°C`;
      document.querySelector(".weather-desc").textContent = data.current.weather[0].description;
      document.querySelector(".weather-extra").textContent = `Humidity: ${data.current.humidity}% | Wind: ${data.current.wind_speed} km/h`;

      // Update weather icon
      const weatherIconElement = document.querySelector(".weather-icon i");
      updateWeatherIcon(weatherIconElement, data.current.weather[0].main);

      // Update forecast
      const forecastDays = document.querySelectorAll(".forecast-day");
      data.daily.forEach((day, index) => {
        if (index < forecastDays.length) {
          const dayElement = forecastDays[index];
          const date = new Date(day.dt);
          const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });

          dayElement.querySelector("h4").textContent = index === 0 ? "Tomorrow" : dayName;
          dayElement.querySelector(".forecast-temp").textContent = `${day.temp.day}°C`;
          dayElement.querySelector(".forecast-desc").textContent = day.weather[0].description;

          // Update forecast icon
          const iconElement = dayElement.querySelector(".forecast-icon i");
          updateWeatherIcon(iconElement, day.weather[0].main);
        }
      });
    }

    function updateWeatherIcon(iconElement, weatherType) {
      // Remove all existing weather classes
      iconElement.className = "";

      // Add the appropriate weather icon class
      switch (weatherType) {
        case "Clear":
          iconElement.classList.add("fas", "fa-sun");
          break;
        case "Clouds":
          iconElement.classList.add("fas", "fa-cloud");
          break;
        case "Rain":
        case "Drizzle":
          iconElement.classList.add("fas", "fa-cloud-rain");
          break;
        case "Thunderstorm":
          iconElement.classList.add("fas", "fa-bolt");
          break;
        case "Snow":
          iconElement.classList.add("fas", "fa-snowflake");
          break;
        case "Mist":
        case "Fog":
        case "Haze":
          iconElement.classList.add("fas", "fa-smog");
          break;
        default:
          iconElement.classList.add("fas", "fa-cloud-sun");
      }
    }
  </script>

  <!-- Language Toggle Script -->
  <script src="language-toggle.js"></script>
</body>
</html>
