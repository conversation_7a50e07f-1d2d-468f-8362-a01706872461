#!/usr/bin/env node

/**
 * Heritage Explorer - Development Server
 * A simple Node.js server for development purposes
 * Note: This is for frontend development only - PHP features won't work
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = process.env.PORT || 3000;
const HOST = process.env.HOST || 'localhost';

// MIME types
const mimeTypes = {
    '.html': 'text/html',
    '.css': 'text/css',
    '.js': 'application/javascript',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon',
    '.txt': 'text/plain',
    '.pdf': 'application/pdf'
};

// Get MIME type
function getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return mimeTypes[ext] || 'application/octet-stream';
}

// Mock PHP responses for development
function mockPhpResponse(pathname, query, method, body) {
    const response = {
        'Content-Type': 'application/json'
    };
    
    // Mock auth.php responses
    if (pathname === '/auth.php') {
        if (query.action === 'check_session') {
            return {
                headers: response,
                body: JSON.stringify({
                    success: false,
                    logged_in: false,
                    message: 'Development mode - PHP not available'
                })
            };
        }
        
        if (query.action === 'check_username') {
            return {
                headers: response,
                body: JSON.stringify({
                    available: true,
                    message: 'Username available (dev mode)'
                })
            };
        }
        
        if (query.action === 'check_email') {
            return {
                headers: response,
                body: JSON.stringify({
                    available: true,
                    message: 'Email available (dev mode)'
                })
            };
        }
        
        if (method === 'POST') {
            return {
                headers: response,
                body: JSON.stringify({
                    success: false,
                    message: 'Development mode - Use XAMPP for full functionality'
                })
            };
        }
    }
    
    // Mock other PHP files
    if (pathname.endsWith('.php')) {
        return {
            headers: { 'Content-Type': 'text/html' },
            body: `
<!DOCTYPE html>
<html>
<head>
    <title>Development Mode</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 40px; background: #f0f0f0; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚧 Development Mode</h1>
        <div class="warning">
            <strong>PHP File Requested:</strong> ${pathname}<br>
            <strong>Note:</strong> This is a development server. PHP files cannot be executed.
        </div>
        <p>For full functionality including authentication and database features, please use XAMPP:</p>
        <ol>
            <li>Install XAMPP from <a href="https://www.apachefriends.org/">apachefriends.org</a></li>
            <li>Copy files to <code>C:\\xampp\\htdocs\\heritage-explorer\\</code></li>
            <li>Start Apache and MySQL services</li>
            <li>Access via <code>http://localhost/heritage-explorer/</code></li>
        </ol>
        <a href="/" class="btn">← Back to Website</a>
        <a href="website.html" class="btn">Main Site</a>
    </div>
</body>
</html>`
        };
    }
    
    return null;
}

// Create server
const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url, true);
    let pathname = parsedUrl.pathname;
    const query = parsedUrl.query;
    
    // Default to index.html or website.html
    if (pathname === '/') {
        if (fs.existsSync(path.join(__dirname, 'website.html'))) {
            pathname = '/website.html';
        } else if (fs.existsSync(path.join(__dirname, 'index.html'))) {
            pathname = '/index.html';
        }
    }
    
    // Handle POST requests
    let body = '';
    if (req.method === 'POST') {
        req.on('data', chunk => {
            body += chunk.toString();
        });
        
        req.on('end', () => {
            // Check for PHP mock response
            const mockResponse = mockPhpResponse(pathname, query, req.method, body);
            if (mockResponse) {
                Object.keys(mockResponse.headers).forEach(key => {
                    res.setHeader(key, mockResponse.headers[key]);
                });
                res.writeHead(200);
                res.end(mockResponse.body);
                return;
            }
        });
        return;
    }
    
    // Check for PHP mock response (GET requests)
    const mockResponse = mockPhpResponse(pathname, query, req.method);
    if (mockResponse) {
        Object.keys(mockResponse.headers).forEach(key => {
            res.setHeader(key, mockResponse.headers[key]);
        });
        res.writeHead(200);
        res.end(mockResponse.body);
        return;
    }
    
    // Serve static files
    const filePath = path.join(__dirname, pathname);
    
    fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
            // File not found
            res.writeHead(404, { 'Content-Type': 'text/html' });
            res.end(`
<!DOCTYPE html>
<html>
<head>
    <title>404 - Not Found</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 40px; text-align: center; background: #f0f0f0; }
        .container { max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>404 - File Not Found</h1>
        <p>The requested file <code>${pathname}</code> was not found.</p>
        <a href="/" class="btn">← Back to Home</a>
    </div>
</body>
</html>`);
            return;
        }
        
        fs.stat(filePath, (err, stats) => {
            if (err) {
                res.writeHead(500, { 'Content-Type': 'text/plain' });
                res.end('Internal Server Error');
                return;
            }
            
            if (stats.isDirectory()) {
                // Try to serve index.html from directory
                const indexPath = path.join(filePath, 'index.html');
                fs.access(indexPath, fs.constants.F_OK, (err) => {
                    if (err) {
                        res.writeHead(403, { 'Content-Type': 'text/plain' });
                        res.end('Directory listing not allowed');
                    } else {
                        fs.readFile(indexPath, (err, data) => {
                            if (err) {
                                res.writeHead(500, { 'Content-Type': 'text/plain' });
                                res.end('Internal Server Error');
                            } else {
                                res.writeHead(200, { 'Content-Type': 'text/html' });
                                res.end(data);
                            }
                        });
                    }
                });
            } else {
                // Serve file
                const mimeType = getMimeType(filePath);
                res.writeHead(200, { 'Content-Type': mimeType });
                
                const readStream = fs.createReadStream(filePath);
                readStream.pipe(res);
                
                readStream.on('error', (err) => {
                    res.writeHead(500, { 'Content-Type': 'text/plain' });
                    res.end('Internal Server Error');
                });
            }
        });
    });
});

// Start server
server.listen(PORT, HOST, () => {
    console.log('\n==========================================');
    console.log('   Heritage Explorer - Development Server');
    console.log('==========================================\n');
    console.log(`🚀 Server running at: http://${HOST}:${PORT}/`);
    console.log(`📁 Serving files from: ${__dirname}`);
    console.log('\n📋 Available URLs:');
    console.log(`   Main Site: http://${HOST}:${PORT}/website.html`);
    console.log(`   Login:     http://${HOST}:${PORT}/login.html`);
    console.log(`   Signup:    http://${HOST}:${PORT}/signup.html`);
    console.log('\n⚠️  Note: This is a development server.');
    console.log('   PHP features (authentication, database) will not work.');
    console.log('   For full functionality, use XAMPP with Apache and MySQL.\n');
    console.log('Press Ctrl+C to stop the server');
});

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n\n🛑 Server shutting down...');
    server.close(() => {
        console.log('✅ Server stopped successfully');
        process.exit(0);
    });
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
    console.error('❌ Uncaught Exception:', err);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
