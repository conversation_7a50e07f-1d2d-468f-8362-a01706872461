<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Heritage Explorer - Setup Check</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #fff;
        }
        
        .status-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #f39c12;
        }
        
        .status-item.success {
            border-left-color: #27ae60;
        }
        
        .status-item.error {
            border-left-color: #e74c3c;
        }
        
        .status-item.warning {
            border-left-color: #f39c12;
        }
        
        .icon {
            font-size: 20px;
            margin-right: 10px;
        }
        
        .button {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            font-weight: bold;
            transition: background 0.3s;
        }
        
        .button:hover {
            background: #2980b9;
        }
        
        .button.success {
            background: #27ae60;
        }
        
        .button.success:hover {
            background: #229954;
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .code {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
        
        .step {
            margin: 15px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
        }
        
        .current-url {
            background: rgba(255, 255, 255, 0.2);
            padding: 10px;
            border-radius: 6px;
            font-family: monospace;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏛️ Heritage Explorer - Setup Diagnostic</h1>
        
        <div class="status-item error">
            <span class="icon">❌</span>
            <strong>Current Issue Detected:</strong> You're accessing the site through a static file server instead of a PHP-enabled web server.
        </div>
        
        <div class="current-url">
            <strong>Current URL:</strong> <span id="currentUrl"></span>
        </div>
        
        <div class="instructions">
            <h3>🔧 How to Fix This Issue:</h3>
            
            <div class="step">
                <h4>Step 1: Install XAMPP (if not already installed)</h4>
                <p>Download and install XAMPP from: <a href="https://www.apachefriends.org/" target="_blank" style="color: #3498db;">https://www.apachefriends.org/</a></p>
            </div>
            
            <div class="step">
                <h4>Step 2: Start XAMPP Services</h4>
                <p>1. Open XAMPP Control Panel</p>
                <p>2. Start <strong>Apache</strong> service</p>
                <p>3. Start <strong>MySQL</strong> service</p>
                <p>4. Both should show "Running" status</p>
            </div>
            
            <div class="step">
                <h4>Step 3: Move Your Files</h4>
                <p>Copy ALL your project files to:</p>
                <div class="code">C:\xampp\htdocs\heritage-explorer\</div>
                <p>Make sure these files are in that folder:</p>
                <ul>
                    <li>website.html</li>
                    <li>login.html</li>
                    <li>signup.html</li>
                    <li>auth.php</li>
                    <li>config.php</li>
                    <li>auth.js</li>
                    <li>setup-database.php</li>
                    <li>All other project files</li>
                </ul>
            </div>
            
            <div class="step">
                <h4>Step 4: Access Through Localhost</h4>
                <p>Open your browser and go to:</p>
                <div class="code">http://localhost/heritage-explorer/setup-database.php</div>
                <p>This will automatically set up your database.</p>
            </div>
            
            <div class="step">
                <h4>Step 5: Test Your Website</h4>
                <p>After database setup, access your website at:</p>
                <div class="code">http://localhost/heritage-explorer/website.html</div>
            </div>
        </div>
        
        <div class="status-item warning">
            <span class="icon">⚠️</span>
            <strong>Important:</strong> You MUST use <code>http://localhost/</code> not <code>http://127.0.0.1:5500/</code> for PHP to work.
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <h3>Quick Links (use these AFTER moving files to XAMPP):</h3>
            <a href="http://localhost/heritage-explorer/setup-database.php" class="button">Setup Database</a>
            <a href="http://localhost/heritage-explorer/test-connection.php" class="button">Test Connection</a>
            <a href="http://localhost/heritage-explorer/website.html" class="button success">Go to Website</a>
        </div>
        
        <div class="instructions">
            <h3>🚨 Why This Error Occurs:</h3>
            <p><strong>Static File Server (127.0.0.1:5500):</strong> Can only serve HTML, CSS, and JavaScript files. Cannot execute PHP code.</p>
            <p><strong>XAMPP/Apache Server (localhost):</strong> Can execute PHP code and connect to databases.</p>
            <p><strong>Your authentication system needs PHP</strong> to work with the database, so you must use XAMPP.</p>
        </div>
        
        <div class="status-item success" style="margin-top: 30px;">
            <span class="icon">✅</span>
            <strong>After Setup:</strong> Your login/signup system will work perfectly, and you'll be able to create user accounts and manage the database.
        </div>
    </div>

    <script>
        // Display current URL
        document.getElementById('currentUrl').textContent = window.location.href;
        
        // Check if we're on the right server
        if (window.location.hostname === 'localhost' && !window.location.port) {
            // We're on localhost, check if PHP is working
            fetch('auth.php?action=check_session')
                .then(response => {
                    if (response.ok) {
                        // PHP is working, redirect to main site
                        window.location.href = 'website.html';
                    }
                })
                .catch(error => {
                    console.log('PHP not working yet, showing setup instructions');
                });
        }
        
        // Auto-refresh every 10 seconds to check if user has fixed the setup
        setTimeout(() => {
            if (window.location.hostname === 'localhost') {
                window.location.reload();
            }
        }, 10000);
    </script>
</body>
</html>
