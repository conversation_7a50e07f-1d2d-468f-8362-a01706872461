<?php
// Database setup script for Heritage Explorer
echo "<h2>Heritage Explorer - Database Setup</h2>";

// Database configuration
$host = 'localhost';
$port = '3306';
$dbname = 'tourism_db';
$username = 'root';
$password = '<PERSON><PERSON>oj@3010';

try {
    // Connect to MySQL server
    echo "Connecting to MySQL server...<br>";
    $dsn = "mysql:host=$host;port=$port;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Connected to MySQL server<br><br>";
    
    // Create database
    echo "Creating database '$dbname'...<br>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $dbname CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Database created/verified<br><br>";
    
    // Connect to the specific database
    echo "Connecting to database '$dbname'...<br>";
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Connected to database<br><br>";
    
    // Create tables
    echo "Creating tables...<br>";
    
    // Users table
    $sql = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        phone VARCHAR(15),
        date_of_birth DATE,
        gender ENUM('male', 'female', 'other'),
        profile_picture VARCHAR(255),
        is_active BOOLEAN DEFAULT TRUE,
        email_verified BOOLEAN DEFAULT FALSE,
        email_verification_token VARCHAR(255),
        password_reset_token VARCHAR(255),
        password_reset_expires TIMESTAMP NULL,
        last_login TIMESTAMP NULL,
        login_attempts INT DEFAULT 0,
        locked_until TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_username (username),
        INDEX idx_email (email)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "✅ Users table created<br>";
    
    // User sessions table
    $sql = "CREATE TABLE IF NOT EXISTS user_sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        session_token VARCHAR(255) UNIQUE NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_session_token (session_token),
        INDEX idx_user_id (user_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "✅ User sessions table created<br>";
    
    // User favorites table
    $sql = "CREATE TABLE IF NOT EXISTS user_favorites (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        site_name VARCHAR(100) NOT NULL,
        site_category VARCHAR(50),
        site_location VARCHAR(100),
        site_image_url VARCHAR(255),
        notes TEXT,
        added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_site (user_id, site_name),
        INDEX idx_user_id (user_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "✅ User favorites table created<br>";
    
    // User reviews table
    $sql = "CREATE TABLE IF NOT EXISTS user_reviews (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        site_name VARCHAR(100) NOT NULL,
        rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
        review_text TEXT,
        visit_date DATE,
        is_verified BOOLEAN DEFAULT FALSE,
        helpful_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_id (user_id),
        INDEX idx_site_name (site_name)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "✅ User reviews table created<br>";
    
    // Heritage sites table
    $sql = "CREATE TABLE IF NOT EXISTS heritage_sites (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        category ENUM('Temple', 'Beach', 'Hill Station', 'Monument', 'Museum', 'Palace', 'Fort', 'Church') NOT NULL,
        location VARCHAR(100) NOT NULL,
        district VARCHAR(50),
        state VARCHAR(50) DEFAULT 'Tamil Nadu',
        latitude DECIMAL(10, 8),
        longitude DECIMAL(11, 8),
        description TEXT,
        historical_significance TEXT,
        best_time_to_visit VARCHAR(100),
        entry_fee VARCHAR(50),
        opening_hours VARCHAR(100),
        contact_info VARCHAR(255),
        official_website VARCHAR(255),
        image_url VARCHAR(255),
        unesco_site BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_name (name),
        INDEX idx_category (category),
        INDEX idx_location (location)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    $pdo->exec($sql);
    echo "✅ Heritage sites table created<br><br>";
    
    // Insert sample data
    echo "Inserting sample data...<br>";
    
    // Check if admin user exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
    $stmt->execute();
    $adminExists = $stmt->fetchColumn() > 0;
    
    if (!$adminExists) {
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (username, email, password, full_name, is_active, email_verified) VALUES 
                ('admin', '<EMAIL>', ?, 'Heritage Explorer Admin', TRUE, TRUE)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$adminPassword]);
        echo "✅ Admin user created (username: admin, password: admin123)<br>";
    } else {
        echo "ℹ️ Admin user already exists<br>";
    }
    
    // Check if heritage sites exist
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM heritage_sites");
    $stmt->execute();
    $sitesCount = $stmt->fetchColumn();
    
    if ($sitesCount == 0) {
        $sites = [
            ['Brihadeeswarar Temple', 'Temple', 'Thanjavur', 'Thanjavur', 10.7870, 79.1378, 'A magnificent Chola temple and UNESCO World Heritage Site', 1],
            ['Shore Temple', 'Temple', 'Mahabalipuram', 'Kanchipuram', 12.6162, 80.2730, 'Ancient temple complex by the sea', 1],
            ['Meenakshi Amman Temple', 'Temple', 'Madurai', 'Madurai', 9.9195, 78.1193, 'Famous temple dedicated to Goddess Meenakshi', 0],
            ['Ooty Hill Station', 'Hill Station', 'Ooty', 'Nilgiris', 11.4064, 76.6932, 'Queen of Hill Stations in Tamil Nadu', 0],
            ['Marina Beach', 'Beach', 'Chennai', 'Chennai', 13.0827, 80.2707, 'One of the longest beaches in the world', 0],
            ['Kodaikanal', 'Hill Station', 'Kodaikanal', 'Dindigul', 10.2381, 77.4892, 'Princess of Hill Stations', 0],
            ['Thanjavur Palace', 'Palace', 'Thanjavur', 'Thanjavur', 10.7905, 79.1380, 'Royal palace of the Marathas', 0],
            ['Government Museum Chennai', 'Museum', 'Chennai', 'Chennai', 13.0732, 80.2609, 'One of the oldest museums in India', 0]
        ];
        
        $sql = "INSERT INTO heritage_sites (name, category, location, district, latitude, longitude, description, unesco_site) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $pdo->prepare($sql);
        
        foreach ($sites as $site) {
            $stmt->execute($site);
        }
        echo "✅ Sample heritage sites added<br>";
    } else {
        echo "ℹ️ Heritage sites already exist ($sitesCount sites)<br>";
    }
    
    echo "<br><h3>🎉 Database setup completed successfully!</h3>";
    echo "<p><strong>Test your setup:</strong></p>";
    echo "<ul>";
    echo "<li><a href='test-connection.php'>Test Database Connection</a></li>";
    echo "<li><a href='website.html'>Go to Heritage Explorer Website</a></li>";
    echo "<li><a href='login.html'>Test Login Page</a></li>";
    echo "<li><a href='signup.html'>Test Signup Page</a></li>";
    echo "</ul>";
    
    echo "<p><strong>Admin Login:</strong></p>";
    echo "<ul>";
    echo "<li>Username: admin</li>";
    echo "<li>Password: admin123</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<h3>❌ Setup failed!</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p>Please check your MySQL connection and try again.</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background-color: #f8f9fa;
}

h2 {
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

h3 {
    color: #27ae60;
    margin-top: 30px;
}

ul {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

li {
    margin: 10px 0;
    line-height: 1.6;
}

a {
    color: #3498db;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}
</style>
