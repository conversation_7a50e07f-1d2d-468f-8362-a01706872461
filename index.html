<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Heritage Explorer - Setup</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            max-width: 600px;
        }
        
        h1 {
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .status {
            font-size: 1.2em;
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid #fff;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .button {
            background: #3498db;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: background 0.3s;
        }
        
        .button:hover {
            background: #2980b9;
        }
        
        .button.success {
            background: #27ae60;
        }
        
        .button.success:hover {
            background: #229954;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏛️ Heritage Explorer</h1>
        <div class="status" id="status">
            <div class="spinner"></div>
            Checking server configuration...
        </div>
        <div id="actions" style="display: none;">
            <a href="website.html" class="button success">Go to Website</a>
            <a href="setup-database.php" class="button">Setup Database</a>
            <a href="check-setup.html" class="button">Setup Guide</a>
        </div>
    </div>

    <script>
        async function checkServerSetup() {
            const statusDiv = document.getElementById('status');
            const actionsDiv = document.getElementById('actions');
            
            // Check if we're on localhost
            if (window.location.hostname !== 'localhost') {
                statusDiv.innerHTML = `
                    <h3>❌ Wrong Server Detected</h3>
                    <p>You're accessing from: <strong>${window.location.origin}</strong></p>
                    <p>You need to access through: <strong>http://localhost/heritage-explorer/</strong></p>
                    <p>Please follow the setup instructions.</p>
                `;
                actionsDiv.innerHTML = `
                    <a href="check-setup.html" class="button">📋 Setup Instructions</a>
                    <a href="setup-xampp.bat" class="button" download>💾 Download Setup Helper</a>
                `;
                actionsDiv.style.display = 'block';
                return;
            }
            
            // Check if PHP is working
            try {
                const response = await fetch('auth.php?action=check_session');
                
                if (response.ok) {
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        // PHP is working, check database
                        try {
                            const dbResponse = await fetch('test-connection.php');
                            if (dbResponse.ok) {
                                statusDiv.innerHTML = `
                                    <h3>✅ Server Setup Complete!</h3>
                                    <p>PHP and database are working correctly.</p>
                                    <p>You can now use the Heritage Explorer website.</p>
                                `;
                                actionsDiv.innerHTML = `
                                    <a href="website.html" class="button success">🏛️ Go to Website</a>
                                    <a href="login.html" class="button">🔐 Login</a>
                                    <a href="signup.html" class="button">📝 Sign Up</a>
                                `;
                            } else {
                                statusDiv.innerHTML = `
                                    <h3>⚠️ Database Setup Needed</h3>
                                    <p>PHP is working, but database needs setup.</p>
                                `;
                                actionsDiv.innerHTML = `
                                    <a href="setup-database.php" class="button">🗄️ Setup Database</a>
                                    <a href="test-connection.php" class="button">🔍 Test Connection</a>
                                `;
                            }
                        } catch (error) {
                            statusDiv.innerHTML = `
                                <h3>⚠️ Database Setup Needed</h3>
                                <p>PHP is working, but database needs setup.</p>
                            `;
                            actionsDiv.innerHTML = `
                                <a href="setup-database.php" class="button">🗄️ Setup Database</a>
                                <a href="test-connection.php" class="button">🔍 Test Connection</a>
                            `;
                        }
                        actionsDiv.style.display = 'block';
                        return;
                    }
                }
                
                // PHP not working
                statusDiv.innerHTML = `
                    <h3>❌ PHP Not Working</h3>
                    <p>Apache server is not running or PHP is not configured.</p>
                    <p>Please start XAMPP services.</p>
                `;
                actionsDiv.innerHTML = `
                    <a href="check-setup.html" class="button">📋 Setup Instructions</a>
                    <a href="setup-xampp.bat" class="button" download>💾 Download Setup Helper</a>
                `;
                actionsDiv.style.display = 'block';
                
            } catch (error) {
                statusDiv.innerHTML = `
                    <h3>❌ Server Not Running</h3>
                    <p>Cannot connect to PHP server.</p>
                    <p>Please install and start XAMPP.</p>
                `;
                actionsDiv.innerHTML = `
                    <a href="check-setup.html" class="button">📋 Setup Instructions</a>
                    <a href="setup-xampp.bat" class="button" download>💾 Download Setup Helper</a>
                `;
                actionsDiv.style.display = 'block';
            }
        }
        
        // Run check after page loads
        setTimeout(checkServerSetup, 1000);
    </script>
</body>
</html>
