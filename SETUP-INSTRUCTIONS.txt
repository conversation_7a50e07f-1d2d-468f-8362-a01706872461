HERITAGE EXPLORER - QUICK SETUP GUIDE
=====================================

CURRENT ISSUE: You're accessing through http://127.0.0.1:5500 (static file server)
SOLUTION: Use XAMPP with PHP support

STEP-BY-STEP INSTRUCTIONS:
=========================

1. INSTALL XAMPP:
   - Download from: https://www.apachefriends.org/
   - Install to default location: C:\xampp\
   - Run the installer as Administrator

2. START XAMPP SERVICES:
   - Open XAMPP Control Panel
   - Click "Start" for Apache (should show green "Running")
   - Click "Start" for MySQL (should show green "Running")
   - Both services must be running!

3. CREATE PROJECT DIRECTORY:
   - Navigate to: C:\xampp\htdocs\
   - Create new folder: heritage-explorer
   - Full path should be: C:\xampp\htdocs\heritage-explorer\

4. COPY ALL PROJECT FILES:
   Copy these files to C:\xampp\htdocs\heritage-explorer\:
   
   ✓ website.html
   ✓ login.html
   ✓ signup.html
   ✓ auth.php
   ✓ config.php
   ✓ auth.js
   ✓ setup-database.php
   ✓ test-connection.php
   ✓ database.sql
   ✓ index.html
   ✓ check-setup.html
   ✓ heritage-chatbot.js
   ✓ language-toggle.js
   ✓ All CSS files
   ✓ All image files
   ✓ All other project files

5. SETUP DATABASE:
   - Open browser
   - Go to: http://localhost/heritage-explorer/setup-database.php
   - This will automatically create the database and tables
   - Wait for "Setup completed successfully!" message

6. TEST THE WEBSITE:
   - Go to: http://localhost/heritage-explorer/website.html
   - You should see the website WITHOUT any error messages
   - Login/Signup buttons should work properly

7. VERIFY EVERYTHING WORKS:
   - Test database: http://localhost/heritage-explorer/test-connection.php
   - Test login: http://localhost/heritage-explorer/login.html
   - Test signup: http://localhost/heritage-explorer/signup.html

IMPORTANT NOTES:
===============

❌ WRONG URLs (will show errors):
   - http://127.0.0.1:5500/website.html
   - http://localhost:5500/website.html
   - Any URL with port 5500, 3000, etc.

✅ CORRECT URLs (will work properly):
   - http://localhost/heritage-explorer/website.html
   - http://localhost/heritage-explorer/login.html
   - http://localhost/heritage-explorer/signup.html

TROUBLESHOOTING:
===============

Problem: "XAMPP not found"
Solution: Install XAMPP from apachefriends.org

Problem: "Apache won't start"
Solution: 
- Close Skype (uses port 80)
- Stop IIS if running
- Run XAMPP as Administrator

Problem: "MySQL won't start"
Solution:
- Stop any existing MySQL services
- Run XAMPP as Administrator

Problem: "Database connection failed"
Solution:
- Make sure MySQL is running in XAMPP
- Run setup-database.php first

Problem: "Still seeing error overlay"
Solution:
- Make sure you're using http://localhost/ not 127.0.0.1:5500
- Clear browser cache
- Make sure all files are copied to XAMPP directory

QUICK CHECK:
===========

1. XAMPP Control Panel shows:
   ✓ Apache: Running (green)
   ✓ MySQL: Running (green)

2. Files are in: C:\xampp\htdocs\heritage-explorer\

3. Accessing via: http://localhost/heritage-explorer/

4. Database setup completed successfully

If all above are ✓, your website should work perfectly!

ADMIN LOGIN (after database setup):
==================================
Username: admin
Password: admin123
Email: <EMAIL>

SUPPORT:
========
If you still have issues:
1. Check XAMPP error logs
2. Open browser Developer Tools (F12) and check Console
3. Make sure you followed all steps exactly
4. Try restarting XAMPP services
