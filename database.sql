-- Heritage Explorer Database Setup
-- Database: tourism_db
-- This script creates the database and all necessary tables

-- Create database
CREATE DATABASE IF NOT EXISTS tourism_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the database
USE tourism_db;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(15),
    date_of_birth DATE,
    gender ENUM('male', 'female', 'other'),
    profile_picture VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    password_reset_token VARCHAR(255),
    password_reset_expires TIMES<PERSON><PERSON> NULL,
    last_login TIMES<PERSON>MP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIM<PERSON><PERSON><PERSON> NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_email_verification_token (email_verification_token),
    INDEX idx_password_reset_token (password_reset_token)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create user sessions table
CREATE TABLE IF NOT EXISTS user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_session_token (session_token),
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create user favorites table
CREATE TABLE IF NOT EXISTS user_favorites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    site_name VARCHAR(100) NOT NULL,
    site_category VARCHAR(50),
    site_location VARCHAR(100),
    site_image_url VARCHAR(255),
    notes TEXT,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_site (user_id, site_name),
    INDEX idx_user_id (user_id),
    INDEX idx_site_category (site_category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create user reviews table
CREATE TABLE IF NOT EXISTS user_reviews (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    site_name VARCHAR(100) NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    visit_date DATE,
    is_verified BOOLEAN DEFAULT FALSE,
    helpful_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_site_name (site_name),
    INDEX idx_rating (rating),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create user visit history table
CREATE TABLE IF NOT EXISTS user_visit_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    site_name VARCHAR(100) NOT NULL,
    visit_date DATE NOT NULL,
    duration_minutes INT,
    notes TEXT,
    photos_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_site_name (site_name),
    INDEX idx_visit_date (visit_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create user preferences table
CREATE TABLE IF NOT EXISTS user_preferences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    preferred_language ENUM('en', 'ta') DEFAULT 'en',
    notification_email BOOLEAN DEFAULT TRUE,
    notification_sms BOOLEAN DEFAULT FALSE,
    preferred_categories JSON,
    travel_radius_km INT DEFAULT 50,
    accessibility_needs JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_preferences (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create heritage sites table (for reference)
CREATE TABLE IF NOT EXISTS heritage_sites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    category ENUM('Temple', 'Beach', 'Hill Station', 'Monument', 'Museum', 'Palace', 'Fort', 'Church') NOT NULL,
    location VARCHAR(100) NOT NULL,
    district VARCHAR(50),
    state VARCHAR(50) DEFAULT 'Tamil Nadu',
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    description TEXT,
    historical_significance TEXT,
    best_time_to_visit VARCHAR(100),
    entry_fee VARCHAR(50),
    opening_hours VARCHAR(100),
    contact_info VARCHAR(255),
    official_website VARCHAR(255),
    image_url VARCHAR(255),
    unesco_site BOOLEAN DEFAULT FALSE,
    accessibility_features JSON,
    nearby_attractions JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_category (category),
    INDEX idx_location (location),
    INDEX idx_district (district),
    INDEX idx_coordinates (latitude, longitude)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample heritage sites data
INSERT INTO heritage_sites (name, category, location, district, latitude, longitude, description, unesco_site) VALUES
('Brihadeeswarar Temple', 'Temple', 'Thanjavur', 'Thanjavur', 10.7870, 79.1378, 'A magnificent Chola temple and UNESCO World Heritage Site', TRUE),
('Shore Temple', 'Temple', 'Mahabalipuram', 'Kanchipuram', 12.6162, 80.2730, 'Ancient temple complex by the sea', TRUE),
('Meenakshi Amman Temple', 'Temple', 'Madurai', 'Madurai', 9.9195, 78.1193, 'Famous temple dedicated to Goddess Meenakshi', FALSE),
('Ooty Hill Station', 'Hill Station', 'Ooty', 'Nilgiris', 11.4064, 76.6932, 'Queen of Hill Stations in Tamil Nadu', FALSE),
('Marina Beach', 'Beach', 'Chennai', 'Chennai', 13.0827, 80.2707, 'One of the longest beaches in the world', FALSE),
('Kodaikanal', 'Hill Station', 'Kodaikanal', 'Dindigul', 10.2381, 77.4892, 'Princess of Hill Stations', FALSE),
('Thanjavur Palace', 'Palace', 'Thanjavur', 'Thanjavur', 10.7905, 79.1380, 'Royal palace of the Marathas', FALSE),
('Government Museum Chennai', 'Museum', 'Chennai', 'Chennai', 13.0732, 80.2609, 'One of the oldest museums in India', FALSE);

-- Create admin user (password: admin123)
INSERT INTO users (username, email, password, full_name, is_active, email_verified) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Heritage Explorer Admin', TRUE, TRUE);

-- Create indexes for better performance
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_users_created_at ON users(created_at);
CREATE INDEX idx_sessions_expires ON user_sessions(expires_at);
CREATE INDEX idx_favorites_added_at ON user_favorites(added_at);
CREATE INDEX idx_reviews_helpful ON user_reviews(helpful_count);

-- Create views for common queries
CREATE VIEW active_users AS
SELECT id, username, email, full_name, last_login, created_at
FROM users 
WHERE is_active = TRUE;

CREATE VIEW popular_sites AS
SELECT 
    site_name,
    COUNT(*) as favorite_count,
    AVG(rating) as average_rating
FROM user_favorites uf
LEFT JOIN user_reviews ur ON uf.site_name = ur.site_name
GROUP BY site_name
ORDER BY favorite_count DESC;

-- Create stored procedures for common operations
DELIMITER //

CREATE PROCEDURE GetUserFavorites(IN user_id INT)
BEGIN
    SELECT 
        uf.*,
        AVG(ur.rating) as average_rating,
        COUNT(ur.id) as review_count
    FROM user_favorites uf
    LEFT JOIN user_reviews ur ON uf.site_name = ur.site_name
    WHERE uf.user_id = user_id
    GROUP BY uf.id
    ORDER BY uf.added_at DESC;
END //

CREATE PROCEDURE GetSiteStatistics(IN site_name VARCHAR(100))
BEGIN
    SELECT 
        COUNT(DISTINCT uf.user_id) as favorite_count,
        AVG(ur.rating) as average_rating,
        COUNT(DISTINCT ur.user_id) as review_count,
        COUNT(DISTINCT vh.user_id) as visitor_count
    FROM heritage_sites hs
    LEFT JOIN user_favorites uf ON hs.name = uf.site_name
    LEFT JOIN user_reviews ur ON hs.name = ur.site_name
    LEFT JOIN user_visit_history vh ON hs.name = vh.site_name
    WHERE hs.name = site_name;
END //

DELIMITER ;

-- Grant permissions (adjust as needed for your setup)
-- GRANT ALL PRIVILEGES ON tourism_db.* TO 'heritage_user'@'localhost' IDENTIFIED BY 'heritage_password';
-- FLUSH PRIVILEGES;

-- Show table structure
SHOW TABLES;

-- Display table information
SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    DATA_LENGTH,
    INDEX_LENGTH
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'tourism_db'
ORDER BY TABLE_NAME;
