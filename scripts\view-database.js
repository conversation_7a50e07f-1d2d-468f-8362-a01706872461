const mongoose = require('mongoose');
const User = require('../models/User');
require('dotenv').config();

async function viewDatabase() {
  try {
    console.log('🔄 Connecting to MongoDB...');
    
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('✅ Connected to MongoDB successfully');
    console.log(`📊 Database: ${process.env.DB_NAME}`);
    console.log('=' .repeat(50));

    // Get database statistics
    const db = mongoose.connection.db;
    const stats = await db.stats();
    
    console.log('\n📈 DATABASE STATISTICS:');
    console.log(`   Collections: ${stats.collections}`);
    console.log(`   Data Size: ${(stats.dataSize / 1024).toFixed(2)} KB`);
    console.log(`   Storage Size: ${(stats.storageSize / 1024).toFixed(2)} KB`);
    console.log(`   Indexes: ${stats.indexes}`);

    // List all collections
    console.log('\n📁 COLLECTIONS:');
    const collections = await db.listCollections().toArray();
    collections.forEach(collection => {
      console.log(`   - ${collection.name}`);
    });

    // View Users collection
    console.log('\n👥 USERS COLLECTION:');
    const userCount = await User.countDocuments();
    console.log(`   Total Users: ${userCount}`);
    
    if (userCount > 0) {
      console.log('\n   📋 User List:');
      const users = await User.find({}, {
        username: 1,
        email: 1,
        firstName: 1,
        lastName: 1,
        isActive: 1,
        lastLogin: 1,
        createdAt: 1,
        'favorites.length': { $size: '$favorites' },
        'visitedSites.length': { $size: '$visitedSites' }
      }).sort({ createdAt: -1 });

      users.forEach((user, index) => {
        console.log(`\n   ${index + 1}. ${user.username} (${user.email})`);
        console.log(`      Name: ${user.firstName} ${user.lastName}`);
        console.log(`      Status: ${user.isActive ? 'Active' : 'Inactive'}`);
        console.log(`      Created: ${user.createdAt ? user.createdAt.toLocaleDateString() : 'N/A'}`);
        console.log(`      Last Login: ${user.lastLogin ? user.lastLogin.toLocaleDateString() : 'Never'}`);
        console.log(`      Favorites: ${user.favorites ? user.favorites.length : 0}`);
        console.log(`      Visited Sites: ${user.visitedSites ? user.visitedSites.length : 0}`);
      });

      // Show detailed view of first user (if exists)
      if (users.length > 0) {
        console.log('\n   🔍 DETAILED VIEW - First User:');
        const detailedUser = await User.findById(users[0]._id);
        console.log(`      Username: ${detailedUser.username}`);
        console.log(`      Email: ${detailedUser.email}`);
        console.log(`      Full Name: ${detailedUser.firstName} ${detailedUser.lastName}`);
        console.log(`      Preferences: ${JSON.stringify(detailedUser.preferences, null, 8)}`);
        
        if (detailedUser.favorites && detailedUser.favorites.length > 0) {
          console.log(`      Favorites:`);
          detailedUser.favorites.forEach((fav, i) => {
            console.log(`        ${i + 1}. ${fav.siteName} (${fav.category})`);
          });
        }

        if (detailedUser.visitedSites && detailedUser.visitedSites.length > 0) {
          console.log(`      Visited Sites:`);
          detailedUser.visitedSites.forEach((visit, i) => {
            console.log(`        ${i + 1}. ${visit.siteName} - Rating: ${visit.rating || 'N/A'}`);
          });
        }
      }
    }

    // View Heritage Sites collection
    console.log('\n🏛️  HERITAGE SITES COLLECTION:');
    const sitesCollection = db.collection('heritage_sites');
    const sitesCount = await sitesCollection.countDocuments();
    console.log(`   Total Heritage Sites: ${sitesCount}`);
    
    if (sitesCount > 0) {
      console.log('\n   📋 Sites List:');
      const sites = await sitesCollection.find({}, {
        name: 1,
        location: 1,
        category: 1,
        createdAt: 1
      }).limit(10).toArray();

      sites.forEach((site, index) => {
        console.log(`   ${index + 1}. ${site.name} - ${site.location} (${site.category})`);
      });

      if (sitesCount > 10) {
        console.log(`   ... and ${sitesCount - 10} more sites`);
      }
    }

    // Show recent activity
    console.log('\n📊 RECENT ACTIVITY:');
    const recentUsers = await User.find({})
      .sort({ lastLogin: -1 })
      .limit(5)
      .select('username lastLogin');
    
    console.log('   Recent Logins:');
    recentUsers.forEach((user, index) => {
      const lastLogin = user.lastLogin ? user.lastLogin.toLocaleString() : 'Never';
      console.log(`   ${index + 1}. ${user.username} - ${lastLogin}`);
    });

    console.log('\n' + '=' .repeat(50));
    console.log('✅ Database view completed successfully!');

  } catch (error) {
    console.error('❌ Error viewing database:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

// Run the database viewer
viewDatabase();
