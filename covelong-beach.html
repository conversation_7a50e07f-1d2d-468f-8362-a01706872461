<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Covelong Beach - Heritage Explorer</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
  <style>
    :root {
      --primary-color: #1a237e;
      --secondary-color: #457b9d;
      --accent-color: #e63946;
      --light-color: #f1faee;
      --dark-color: #1d3557;
      --success-color: #2e7d32;
      --warning-color: #ff9800;
      --danger-color: #c62828;
      --gray-100: #f8f9fa;
      --gray-200: #e9ecef;
      --gray-300: #dee2e6;
      --gray-400: #ced4da;
      --gray-500: #adb5bd;
      --gray-600: #6c757d;
      --gray-700: #495057;
      --gray-800: #343a40;
      --gray-900: #212529;
      --border-radius: 0.5rem;
      --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
      --transition: all 0.3s ease-in-out;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: var(--gray-800);
      background-color: var(--gray-100);
    }

    header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      text-align: center;
    }

    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      background-color: white;
      box-shadow: var(--box-shadow);
    }

    nav a {
      color: var(--gray-700);
      text-decoration: none;
      margin: 0 1rem;
      font-weight: 500;
      transition: var(--transition);
    }

    nav a:hover {
      color: var(--primary-color);
    }

    main {
      max-width: 1200px;
      margin: 2rem auto;
      padding: 0 1rem;
    }

    .site-details {
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    @media (min-width: 768px) {
      .site-details {
        grid-template-columns: 1fr 1fr;
      }
    }

    .site-image {
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
    }

    .site-image img {
      width: 100%;
      height: auto;
      display: block;
    }

    .site-info h1 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .site-info p {
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }

    .site-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .meta-item {
      display: flex;
      align-items: center;
      background-color: var(--gray-200);
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
    }

    .meta-item i {
      margin-right: 0.5rem;
      color: var(--secondary-color);
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      margin-top: 1.5rem;
    }

    .action-buttons button {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      background-color: var(--secondary-color);
      color: white;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
      display: flex;
      align-items: center;
    }

    .action-buttons button i {
      margin-right: 0.5rem;
    }

    .action-buttons button:hover {
      background-color: var(--primary-color);
    }

    .site-description {
      margin-bottom: 2rem;
    }

    .site-description h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .map-container {
      height: 400px;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      margin-bottom: 2rem;
    }

    .nearby-attractions {
      margin-bottom: 2rem;
    }

    .nearby-attractions h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .attractions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .attraction-card {
      background-color: white;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
    }

    .attraction-card:hover {
      transform: translateY(-5px);
    }

    .attraction-card img {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }

    .attraction-card-content {
      padding: 1rem;
    }

    .attraction-card-content h3 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    footer {
      background-color: var(--primary-color);
      color: white;
      text-align: center;
      padding: 1rem;
      margin-top: 2rem;
    }

    #backToTop {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      width: 3rem;
      height: 3rem;
      background-color: var(--primary-color);
      color: white;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
      display: none;
    }

    #backToTop:hover {
      background-color: var(--secondary-color);
    }

    /* Travel Information Section */
    .travel-info {
      margin-bottom: 2rem;
      background-color: white;
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      overflow: hidden;
    }

    .travel-info-header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .travel-info-header h2 {
      margin: 0;
      font-size: 1.5rem;
    }

    .travel-info-tabs {
      display: flex;
      background-color: var(--gray-200);
    }

    .travel-tab {
      padding: 1rem;
      cursor: pointer;
      flex: 1;
      text-align: center;
      font-weight: 500;
      transition: var(--transition);
      border-bottom: 3px solid transparent;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .travel-tab i {
      margin-right: 0.5rem;
    }

    .travel-tab.active {
      background-color: white;
      border-bottom-color: var(--accent-color);
    }

    .travel-tab:hover:not(.active) {
      background-color: var(--gray-300);
    }

    .travel-content {
      padding: 1.5rem;
    }

    .travel-panel {
      display: none;
    }

    .travel-panel.active {
      display: block;
    }

    /* Weather Panel */
    .weather-info {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      align-items: center;
    }

    .weather-current {
      display: flex;
      align-items: center;
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      flex: 1;
      min-width: 250px;
    }

    .weather-icon {
      font-size: 3rem;
      margin-right: 1rem;
      color: var(--secondary-color);
    }

    .weather-details h3 {
      margin: 0;
      color: var(--primary-color);
    }

    .weather-temp {
      font-size: 2rem;
      font-weight: bold;
    }

    .weather-desc {
      color: var(--gray-700);
    }

    .weather-forecast {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      flex: 2;
      min-width: 300px;
    }

    .forecast-day {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      text-align: center;
      flex: 1;
      min-width: 100px;
    }

    .forecast-day h4 {
      margin: 0 0 0.5rem 0;
      color: var(--primary-color);
    }

    .forecast-icon {
      font-size: 1.5rem;
      margin: 0.5rem 0;
      color: var(--secondary-color);
    }

    /* Transportation Panel */
    .transport-options {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .transport-option {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
      display: flex;
      flex-direction: column;
    }

    .transport-option h3 {
      display: flex;
      align-items: center;
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .transport-option h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .transport-option p {
      margin-bottom: 0.5rem;
    }

    .transport-option .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    /* Beach Activities Panel */
    .hours-container {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .beach-hours, .activities-info {
      flex: 1;
      min-width: 250px;
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .beach-hours h3, .activities-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .beach-hours h3 i, .activities-info h3 i {
      margin-right: 0.5rem;
    }

    .hours-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .hours-list li {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .hours-list li:last-child {
      border-bottom: none;
    }

    .day {
      font-weight: 500;
    }

    .activity-item {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .activity-item:last-child {
      border-bottom: none;
    }

    .activity-name {
      font-weight: 500;
    }

    .activity-price {
      font-weight: bold;
    }

    /* Amenities Panel */
    .amenities-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .amenity-card {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .amenity-card h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .amenity-card h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .amenity-card p {
      margin-bottom: 0.5rem;
    }

    .amenity-card .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    .amenity-card .rating {
      color: var(--warning-color);
      margin-bottom: 0.5rem;
    }

    .weather-tips h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .local-transport h3, .best-time h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .water-sports-info {
      margin-top: 1.5rem;
    }

    .water-sport {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      margin-bottom: 1rem;
    }

    .water-sport h4 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
    }

    .water-sport h4 i {
      margin-right: 0.5rem;
    }

    .sport-details {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 0.5rem;
    }

    .sport-detail {
      display: flex;
      align-items: center;
      font-size: 0.9rem;
      color: var(--gray-700);
    }

    .sport-detail i {
      margin-right: 0.25rem;
      font-size: 0.8rem;
    }

    .tide-info {
      margin-top: 1.5rem;
    }

    .tide-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .tide-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 1rem;
    }

    .tide-table th, .tide-table td {
      padding: 0.75rem;
      text-align: left;
      border-bottom: 1px solid var(--gray-300);
    }

    .tide-table th {
      background-color: var(--gray-200);
      font-weight: 600;
      color: var(--primary-color);
    }

    .tide-table tr:last-child td {
      border-bottom: none;
    }
  </style>
</head>
<body>
  <header>
    <h1>Heritage Explorer</h1>
    <p>Discover Tamil Nadu's rich heritage and culture</p>
  </header>

  <nav>
    <div>
      <a href="website.html"><i class="fas fa-home"></i> Home</a>
      <a href="#"><i class="fas fa-map-marked-alt"></i> Explore</a>
      <a href="#"><i class="fas fa-info-circle"></i> About</a>
    </div>
    <div>
      <a href="#" id="toggleLang">தமிழ்</a>
    </div>
  </nav>

  <main>
    <div class="site-details">
      <div class="site-image">
        <img src="https://media.istockphoto.com/id/1150059940/photo/kovalam-beach-sunset.jpg?s=612x612&w=0&k=20&c=H0yfOqRkOXYaFxajF8pcHVJ3ONckf7L5cTLTvabZGRU=" alt="Covelong Beach">
      </div>
      <div class="site-info">
        <h1>Covelong Beach</h1>
        <div class="site-meta">
          <div class="meta-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>Kanchipuram, Tamil Nadu</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-umbrella-beach"></i>
            <span>Beach</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-water"></i>
            <span>Water Sports Hub</span>
          </div>
        </div>
        <p>Covelong Beach, also known as Kovalam Beach, is a serene coastal destination near Chennai known for its water sports, fishing village, and the remains of a Dutch fort. It offers a perfect blend of adventure, relaxation, and historical exploration.</p>
        <div class="action-buttons">
          <button id="playAudioButton"><i class="fas fa-volume-up"></i> Listen</button>
          <button id="favoriteButton"><i class="far fa-heart"></i> Favorite</button>
          <button id="shareButton"><i class="fas fa-share-alt"></i> Share</button>
        </div>
      </div>
    </div>

    <div class="site-description">
      <h2>About Covelong Beach</h2>
      <p>Covelong Beach, also known as Kovalam Beach, is a picturesque coastal destination located about 40 kilometers south of Chennai in the Kanchipuram district of Tamil Nadu. The beach derives its name from Covelong, the anglicized version of Kovalam, which means "a grove of coconut trees" in Tamil, aptly describing the landscape of the area.</p>
      <p>The beach has a rich historical background. In the 18th century, it was the site of a port and fort built by the Nawab of Carnatic, which was later taken over by the French and subsequently by the British. The ruins of this fort can still be seen near the beach, adding a historical dimension to the natural beauty of the area.</p>
      <p>Covelong Beach is known for its golden sands and clear blue waters, making it an ideal spot for swimming and sunbathing. What sets it apart from other beaches in Tamil Nadu is its reputation as a hub for water sports. It is one of the few places in India where you can enjoy windsurfing, and it also offers opportunities for activities like kiteboarding, kayaking, and stand-up paddleboarding. The beach is home to the Covelong Point Social Surf School, which promotes surfing and other water sports in the region.</p>
      <p>The beach is also known for its fishing village, where you can observe the traditional lifestyle of local fishermen. The village is dotted with colorful fishing boats and nets, offering a glimpse into the coastal culture of Tamil Nadu. The beach is less crowded compared to other popular beaches in the region, making it a perfect getaway for those seeking tranquility and a connection with nature.</p>
    </div>

    <div class="map-container">
      <!-- Map will be embedded here -->
      <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3890.8123456789!2d80.24!3d12.787!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3a525b446a1a3325%3A0xf3aef7ec7c8e0f85!2sCovelong%20Beach!5e0!3m2!1sen!2sin!4v1652345678901!5m2!1sen!2sin" width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy" title="Map of Covelong Beach"></iframe>
    </div>

    <!-- Travel Information Section -->
    <div class="travel-info">
      <div class="travel-info-header">
        <h2>Practical Travel Information</h2>
        <div class="last-updated">Last updated: <span id="lastUpdated">Today</span></div>
      </div>

      <div class="travel-info-tabs">
        <div class="travel-tab active" data-tab="weather">
          <i class="fas fa-cloud-sun"></i> Weather
        </div>
        <div class="travel-tab" data-tab="transport">
          <i class="fas fa-bus"></i> Transportation
        </div>
        <div class="travel-tab" data-tab="hours">
          <i class="fas fa-water"></i> Beach Activities
        </div>
        <div class="travel-tab" data-tab="amenities">
          <i class="fas fa-concierge-bell"></i> Nearby Amenities
        </div>
      </div>

      <div class="travel-content">
        <!-- Weather Panel -->
        <div class="travel-panel active" id="weatherPanel">
          <div class="weather-info">
            <div class="weather-current">
              <div class="weather-icon">
                <i class="fas fa-sun"></i>
              </div>
              <div class="weather-details">
                <h3>Current Weather</h3>
                <div class="weather-temp">30°C</div>
                <div class="weather-desc">Sunny</div>
                <div class="weather-extra">Humidity: 78% | Wind: 15 km/h</div>
              </div>
            </div>

            <div class="weather-forecast">
              <div class="forecast-day">
                <h4>Tomorrow</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun"></i></div>
                <div class="forecast-temp">29°C</div>
                <div class="forecast-desc">Partly Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Wed</h4>
                <div class="forecast-icon"><i class="fas fa-cloud"></i></div>
                <div class="forecast-temp">28°C</div>
                <div class="forecast-desc">Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Thu</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun"></i></div>
                <div class="forecast-temp">30°C</div>
                <div class="forecast-desc">Partly Cloudy</div>
              </div>
            </div>
          </div>

          <div class="weather-tips">
            <h3>Weather Tips</h3>
            <p>Covelong Beach enjoys a tropical climate with warm temperatures throughout the year. The beach is located on the Coromandel Coast of Tamil Nadu, which experiences a hot and humid climate with temperatures ranging from 25°C to 35°C. The best time to visit is from November to February when the weather is relatively cooler and more pleasant for beach activities.</p>
            <p>The monsoon season (June to September) brings occasional heavy rainfall to the region, which can limit beach activities. However, the post-monsoon period (October-November) offers a beautiful landscape with lush greenery around the beach area. Summer months (March-May) can be extremely hot and humid, with temperatures often exceeding 35°C, making outdoor activities challenging during midday.</p>
            <p>For water sports enthusiasts, the wind conditions are most favorable from November to March, making it the ideal time for surfing, windsurfing, and kiteboarding. Early mornings (6:00 AM - 9:00 AM) and late afternoons (4:00 PM - 6:00 PM) are the best times to enjoy the beach, as the midday sun can be quite intense. Always carry sunscreen (SPF 50+), sunglasses, a hat, and plenty of water when visiting the beach. Light cotton clothing is recommended, and it's advisable to bring a light jacket or shawl for evenings as sea breezes can make it feel cooler.</p>
          </div>

          <div class="tide-info">
            <h3>Tide Information</h3>
            <p>Tide conditions significantly impact water sports and swimming safety at Covelong Beach. Below is the tide schedule for the current week:</p>
            <table class="tide-table">
              <thead>
                <tr>
                  <th>Day</th>
                  <th>High Tide</th>
                  <th>Low Tide</th>
                  <th>Best Time for Activities</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Today</td>
                  <td>6:45 AM, 7:12 PM</td>
                  <td>12:30 PM, 1:15 AM</td>
                  <td>8:00 AM - 11:00 AM</td>
                </tr>
                <tr>
                  <td>Tomorrow</td>
                  <td>7:30 AM, 7:55 PM</td>
                  <td>1:15 PM, 2:00 AM</td>
                  <td>9:00 AM - 12:00 PM</td>
                </tr>
                <tr>
                  <td>Wednesday</td>
                  <td>8:15 AM, 8:40 PM</td>
                  <td>2:00 PM, 2:45 AM</td>
                  <td>10:00 AM - 1:00 PM</td>
                </tr>
                <tr>
                  <td>Thursday</td>
                  <td>9:00 AM, 9:25 PM</td>
                  <td>2:45 PM, 3:30 AM</td>
                  <td>11:00 AM - 2:00 PM</td>
                </tr>
              </tbody>
            </table>
            <p>Note: For surfing and other water sports, the period between high and low tide (falling tide) often provides the best conditions. Always check with local surf schools or lifeguards for the most current tide information and safety advisories.</p>
          </div>
        </div>

        <!-- Transportation Panel -->
        <div class="travel-panel" id="transportPanel">
          <div class="transport-options">
            <div class="transport-option">
              <h3><i class="fas fa-plane"></i> By Air</h3>
              <p>Chennai International Airport is the nearest airport.</p>
              <p class="distance">40 km from Covelong Beach (approx. 1 hour by car)</p>
              <p>Regular flights from all major Indian cities and international destinations.</p>
              <p>Airport taxis available (₹800-1,200).</p>
              <p>Pre-paid taxi counters available at the airport.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-train"></i> By Train</h3>
              <p>Chennai Central and Chennai Egmore are the major railway stations.</p>
              <p class="distance">40-45 km from Covelong Beach (approx. 1.5 hours by car)</p>
              <p>Chengalpattu Junction is the nearest railway station.</p>
              <p class="distance">25 km from Covelong Beach (approx. 45 minutes by car)</p>
              <p>Auto-rickshaws and taxis available from stations.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-bus"></i> By Bus</h3>
              <p>Regular buses from Chennai to Kovalam/Covelong.</p>
              <p>Board buses to Mahabalipuram and get down at Kovalam junction.</p>
              <p>Frequency: Every 30 minutes from Chennai CMBT.</p>
              <p>Journey time: Approximately 1.5-2 hours.</p>
              <p>Fare: ₹50-70 for ordinary buses, ₹100-150 for deluxe buses.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-car"></i> By Car</h3>
              <p>Well-connected via East Coast Road (ECR) from Chennai.</p>
              <p>Distance from Chennai: 40 km (approx. 1 hour).</p>
              <p>Route: Take ECR from Chennai, drive past Muttukadu and Crocodile Bank.</p>
              <p>Parking available near the beach (₹30-50 for cars).</p>
              <p>Car rentals available in Chennai (₹1,500-2,500 per day).</p>
            </div>
          </div>

          <div class="local-transport">
            <h3>Local Transportation</h3>
            <p>Once you reach Covelong Beach, getting around is relatively easy. Auto-rickshaws are readily available for short distances within the area, with fares ranging from ₹30-100 depending on the distance. For more flexibility, consider renting a two-wheeler (₹300-500 per day) from rental shops in Kovalam village. This is a popular option among tourists as it allows you to explore nearby attractions at your own pace.</p>
            <p>If you're staying at one of the resorts along the beach, many offer shuttle services to nearby attractions or can arrange for taxis. For those interested in visiting multiple attractions along the East Coast Road, including Mahabalipuram (15 km south) or Muttukadu Boat House (10 km north), hiring a taxi for the day (₹2,000-3,000) is a convenient option. Some surf schools and resorts also offer bicycle rentals, which is a pleasant way to explore the coastal village, especially during cooler hours of the day.</p>
          </div>
        </div>

        <!-- Beach Activities Panel -->
        <div class="travel-panel" id="hoursPanel">
          <div class="hours-container">
            <div class="beach-hours">
              <h3><i class="far fa-clock"></i> Beach Hours & Safety</h3>
              <ul class="hours-list">
                <li><span class="day">Beach Access:</span> <span class="time">24 hours (unrestricted)</span></li>
                <li><span class="day">Lifeguard Hours:</span> <span class="time">6:00 AM - 6:00 PM</span></li>
                <li><span class="day">Best Swimming Hours:</span> <span class="time">7:00 AM - 10:00 AM, 4:00 PM - 6:00 PM</span></li>
                <li><span class="day">Surfing School Hours:</span> <span class="time">6:00 AM - 6:00 PM</span></li>
                <li><span class="day">Beach Patrol:</span> <span class="time">24 hours</span></li>
              </ul>
              <p class="hours-note">Swimming safety varies throughout the year. Red flags indicate dangerous conditions, while yellow flags suggest caution. Green flags indicate safe swimming conditions. Always follow lifeguard instructions and avoid swimming alone, especially during evening hours. The beach can get crowded on weekends and public holidays, so weekday visits are recommended for a more peaceful experience. Some areas of the beach are reserved for fishing activities, so be mindful of local fishermen and their boats.</p>
            </div>

            <div class="activities-info">
              <h3><i class="fas fa-water"></i> Water Sports & Prices</h3>
              <div class="activity-item">
                <span class="activity-name">Surfing Lesson (1 hour)</span>
                <span class="activity-price">₹1,000 - ₹1,500</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Surfboard Rental (per hour)</span>
                <span class="activity-price">₹300 - ₹500</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Kayaking (30 minutes)</span>
                <span class="activity-price">₹400 - ₹600</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Stand-up Paddleboarding (1 hour)</span>
                <span class="activity-price">₹600 - ₹800</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Windsurfing Lesson (1 hour)</span>
                <span class="activity-price">₹1,200 - ₹1,800</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Catamaran Ride (30 minutes)</span>
                <span class="activity-price">₹300 per person</span>
              </div>
              <p class="activity-note">Most water sports operators require advance booking, especially during peak season (November-February). Covelong Point Social Surf School offers package deals for multiple activities and multi-day lessons. All water sports activities include basic safety equipment, but it's advisable to bring your own sunscreen, water, and appropriate clothing. Some activities may be restricted based on weather and sea conditions.</p>
            </div>
          </div>

          <div class="water-sports-info">
            <h3>Popular Water Sports</h3>
            <p>Covelong Beach is renowned as one of the premier water sports destinations on India's east coast. Here are some of the most popular activities available:</p>

            <div class="water-sport">
              <h4><i class="fas fa-water"></i> Surfing</h4>
              <div class="sport-details">
                <span class="sport-detail"><i class="fas fa-signal"></i> Difficulty: Beginner to Intermediate</span>
                <span class="sport-detail"><i class="fas fa-calendar-alt"></i> Best Season: November to March</span>
                <span class="sport-detail"><i class="fas fa-clock"></i> Best Time: Early Morning (6:00 AM - 9:00 AM)</span>
              </div>
              <p>Covelong Beach offers consistent waves that are perfect for beginners and intermediate surfers. The Covelong Point Social Surf School, founded by local fisherman-turned-surfer Murthy Megavan, provides excellent instruction for all levels. The beach has a sandy bottom with occasional rocky patches, making it relatively safe for learners. Wave heights typically range from 2-4 feet, with larger swells during the northeast monsoon (October-December).</p>
            </div>

            <div class="water-sport">
              <h4><i class="fas fa-wind"></i> Windsurfing & Kiteboarding</h4>
              <div class="sport-details">
                <span class="sport-detail"><i class="fas fa-signal"></i> Difficulty: Intermediate to Advanced</span>
                <span class="sport-detail"><i class="fas fa-calendar-alt"></i> Best Season: January to March</span>
                <span class="sport-detail"><i class="fas fa-clock"></i> Best Time: Late Morning to Afternoon</span>
              </div>
              <p>The consistent sea breeze at Covelong Beach creates ideal conditions for windsurfing and kiteboarding, especially from January to March when wind speeds average 12-18 knots. Several operators offer equipment rentals and lessons, with Bay of Life being one of the most established. These activities require some prior experience or professional instruction, as the conditions can be challenging for beginners.</p>
            </div>

            <div class="water-sport">
              <h4><i class="fas fa-ship"></i> Stand-up Paddleboarding & Kayaking</h4>
              <div class="sport-details">
                <span class="sport-detail"><i class="fas fa-signal"></i> Difficulty: Beginner</span>
                <span class="sport-detail"><i class="fas fa-calendar-alt"></i> Best Season: Year-round (except monsoon)</span>
                <span class="sport-detail"><i class="fas fa-clock"></i> Best Time: Early Morning or Late Afternoon</span>
              </div>
              <p>For those seeking a more relaxed water experience, stand-up paddleboarding (SUP) and kayaking are excellent options. These activities are available year-round except during the monsoon season when sea conditions can be rough. Early mornings offer calm waters, perfect for exploring the coastline. Some operators also offer sunrise and sunset SUP tours, providing a unique perspective of the beach and fishing village.</p>
            </div>
          </div>
        </div>

        <!-- Amenities Panel -->
        <div class="travel-panel" id="amenitiesPanel">
          <div class="amenities-grid">
            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Taj Fisherman's Cove Resort & Spa</strong></p>
              <p class="distance">On Covelong Beach</p>
              <p class="rating">★★★★★ (4.6/5)</p>
              <p>Luxury beachfront resort with colonial architecture.</p>
              <p>Price range: ₹15,000 - ₹30,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Covelong Point Social Surf School</strong></p>
              <p class="distance">On Covelong Beach</p>
              <p class="rating">★★★★☆ (4.3/5)</p>
              <p>Surf school with basic accommodation for surf enthusiasts.</p>
              <p>Price range: ₹2,000 - ₹4,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Bay of Life Surf School & Camp</strong></p>
              <p class="distance">500m from beach</p>
              <p class="rating">★★★★☆ (4.2/5)</p>
              <p>Surf camp with dormitory and private rooms.</p>
              <p>Price range: ₹1,500 - ₹3,500 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Bayview Restaurant (Taj Fisherman's Cove)</strong></p>
              <p class="distance">On Covelong Beach</p>
              <p class="rating">★★★★★ (4.7/5)</p>
              <p>Fine dining with seafood specialties and beach views.</p>
              <p>Price range: ₹3,000 - ₹5,000 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Covelong Surf Cafe</strong></p>
              <p class="distance">At Covelong Point Surf School</p>
              <p class="rating">★★★★☆ (4.1/5)</p>
              <p>Casual cafe serving healthy options and fresh juices.</p>
              <p>Price range: ₹500 - ₹800 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Seashell Restaurant</strong></p>
              <p class="distance">1 km from beach</p>
              <p class="rating">★★★★☆ (4.0/5)</p>
              <p>Local seafood restaurant with authentic Tamil cuisine.</p>
              <p>Price range: ₹600 - ₹1,000 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-shopping-bag"></i> Shopping</h3>
              <p><strong>Kovalam Fish Market</strong></p>
              <p class="distance">1 km from beach</p>
              <p>Fresh seafood market open early mornings.</p>
              <p>Local handicrafts and shell items also available.</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-first-aid"></i> Medical Facilities</h3>
              <p><strong>Kovalam Primary Health Center</strong></p>
              <p class="distance">2 km from beach</p>
              <p>Basic medical facilities for minor emergencies.</p>
              <p>For serious medical issues, hospitals in Chennai (40 km) are recommended.</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-info-circle"></i> Tourist Information</h3>
              <p><strong>Tamil Nadu Tourism Office</strong></p>
              <p class="distance">At Mahabalipuram (15 km south)</p>
              <p>Provides information about local attractions and transportation.</p>
              <p>Contact: +91 44 2744 5333</p>
            </div>
          </div>

          <div class="best-time">
            <h3>Best Time to Visit</h3>
            <p>The ideal time to visit Covelong Beach is from November to February when the weather is pleasant with temperatures ranging from 20°C to 30°C. This period offers the best conditions for beach activities and water sports. The surfing season peaks from November to March when the waves are most consistent. If you're interested in learning to surf, this is the best time to visit as the surf schools are fully operational with regular classes and events.</p>
            <p>The summer months (March to June) can be extremely hot with temperatures often exceeding 35°C, making outdoor activities uncomfortable during midday. However, early mornings and evenings can still be enjoyable. The monsoon season (June to September) brings occasional heavy rainfall, and some water sports activities may be restricted due to rough sea conditions. However, this is also when the beach is least crowded, offering a more peaceful experience.</p>
            <p>Weekends and public holidays see a significant increase in visitors, especially from Chennai. If you prefer a quieter experience, plan your visit on weekdays. The annual Covelong Point Surf, Music & Yoga Festival, usually held in August, attracts surfers and tourists from around the world. While this is an exciting time to visit, accommodations should be booked well in advance as they fill up quickly.</p>
          </div>
        </div>
      </div>
    </div>

    <div class="nearby-attractions">
      <h2>Nearby Attractions</h2>
      <div class="attractions-grid">
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Covelong_Point_Surf_School_20190212182003.jpg" alt="Covelong Point Surf School">
          <div class="attraction-card-content">
            <h3>Covelong Point Surf School</h3>
            <p>A premier surfing school offering lessons for beginners and advanced surfers, along with equipment rentals and accommodation.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Tiger_Cave_20190212182004.jpg" alt="Tiger Cave">
          <div class="attraction-card-content">
            <h3>Tiger Cave</h3>
            <p>An ancient rock-cut temple with carvings of tiger heads, located near Mahabalipuram and offering insights into Pallava architecture.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/DakshinaChitra_20190212182005.jpg" alt="DakshinaChitra Museum">
          <div class="attraction-card-content">
            <h3>DakshinaChitra Museum</h3>
            <p>A living museum showcasing the art, architecture, lifestyles, and crafts of South India through reconstructed traditional houses.</p>
          </div>
        </div>
      </div>
    </div>
  </main>

  <footer>
    <p>&copy; 2025 Heritage Explorer | Explore Tamil Nadu</p>
  </footer>

  <div id="backToTop">
    <i class="fas fa-arrow-up"></i>
  </div>

  <script>
    // Back to Top Button
    const backToTop = document.getElementById("backToTop");
    window.addEventListener("scroll", () => {
      if (window.scrollY > 300) {
        backToTop.style.display = "flex";
      } else {
        backToTop.style.display = "none";
      }
    });
    backToTop.addEventListener("click", () => {
      window.scrollTo({ top: 0, behavior: "smooth" });
    });

    // Audio Playback
    document.getElementById("playAudioButton").addEventListener("click", () => {
      const description = document.querySelector(".site-info p").textContent;
      const utterance = new SpeechSynthesisUtterance(description);
      utterance.lang = "en-US";
      speechSynthesis.speak(utterance);
    });

    // Favorite Button
    document.getElementById("favoriteButton").addEventListener("click", function() {
      const icon = this.querySelector("i");
      if (icon.classList.contains("far")) {
        icon.classList.remove("far");
        icon.classList.add("fas");
        alert("Added to favorites!");
      } else {
        icon.classList.remove("fas");
        icon.classList.add("far");
        alert("Removed from favorites!");
      }
    });

    // Share Button
    document.getElementById("shareButton").addEventListener("click", () => {
      const shareData = {
        title: "Covelong Beach - Heritage Explorer",
        text: "Check out this beautiful beach in Tamil Nadu!",
        url: window.location.href
      };

      if (navigator.share) {
        navigator.share(shareData)
          .then(() => console.log("Shared successfully"))
          .catch((error) => console.error("Error sharing:", error));
      } else {
        navigator.clipboard.writeText(shareData.url)
          .then(() => alert("URL copied to clipboard"))
          .catch((err) => console.error("Could not copy text: ", err));
      }
    });

    // Initialize the travel info tabs
    document.addEventListener("DOMContentLoaded", function() {
      // Set the last updated date
      const today = new Date();
      document.getElementById("lastUpdated").textContent = today.toLocaleDateString();

      // Tab functionality
      const tabs = document.querySelectorAll(".travel-tab");
      tabs.forEach(tab => {
        tab.addEventListener("click", function() {
          // Remove active class from all tabs
          tabs.forEach(t => t.classList.remove("active"));

          // Add active class to clicked tab
          this.classList.add("active");

          // Hide all panels
          const panels = document.querySelectorAll(".travel-panel");
          panels.forEach(panel => panel.classList.remove("active"));

          // Show the corresponding panel
          const tabId = this.getAttribute("data-tab");
          document.getElementById(tabId + "Panel").classList.add("active");
        });
      });

      // Fetch weather data (mock)
      fetchWeatherData();
    });

    // Weather API Integration (Mock)
    function fetchWeatherData() {
      // Coordinates for Covelong Beach
      const lat = 12.7870;
      const lon = 80.2400;

      // Mock weather data
      const mockWeatherData = {
        current: {
          temp: 30,
          humidity: 78,
          wind_speed: 15,
          weather: [{ main: "Clear", description: "Sunny", icon: "01d" }]
        },
        daily: [
          {
            dt: Date.now() + 86400000, // Tomorrow
            temp: { day: 29 },
            weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
          },
          {
            dt: Date.now() + 172800000, // Day after tomorrow
            temp: { day: 28 },
            weather: [{ main: "Clouds", description: "Cloudy", icon: "03d" }]
          },
          {
            dt: Date.now() + 259200000, // 3 days from now
            temp: { day: 30 },
            weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
          }
        ]
      };

      updateWeatherUI(mockWeatherData);
    }

    function updateWeatherUI(data) {
      // Update current weather
      document.querySelector(".weather-temp").textContent = `${data.current.temp}°C`;
      document.querySelector(".weather-desc").textContent = data.current.weather[0].description;
      document.querySelector(".weather-extra").textContent = `Humidity: ${data.current.humidity}% | Wind: ${data.current.wind_speed} km/h`;

      // Update weather icon
      const weatherIconElement = document.querySelector(".weather-icon i");
      updateWeatherIcon(weatherIconElement, data.current.weather[0].main);

      // Update forecast
      const forecastDays = document.querySelectorAll(".forecast-day");
      data.daily.forEach((day, index) => {
        if (index < forecastDays.length) {
          const dayElement = forecastDays[index];
          const date = new Date(day.dt);
          const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });

          dayElement.querySelector("h4").textContent = index === 0 ? "Tomorrow" : dayName;
          dayElement.querySelector(".forecast-temp").textContent = `${day.temp.day}°C`;
          dayElement.querySelector(".forecast-desc").textContent = day.weather[0].description;

          // Update forecast icon
          const iconElement = dayElement.querySelector(".forecast-icon i");
          updateWeatherIcon(iconElement, day.weather[0].main);
        }
      });
    }

    function updateWeatherIcon(iconElement, weatherType) {
      // Remove all existing weather classes
      iconElement.className = "";

      // Add the appropriate weather icon class
      switch (weatherType) {
        case "Clear":
          iconElement.classList.add("fas", "fa-sun");
          break;
        case "Clouds":
          iconElement.classList.add("fas", "fa-cloud-sun");
          break;
        case "Rain":
        case "Drizzle":
          iconElement.classList.add("fas", "fa-cloud-rain");
          break;
        case "Thunderstorm":
          iconElement.classList.add("fas", "fa-bolt");
          break;
        case "Snow":
          iconElement.classList.add("fas", "fa-snowflake");
          break;
        case "Mist":
        case "Fog":
        case "Haze":
          iconElement.classList.add("fas", "fa-smog");
          break;
        default:
          iconElement.classList.add("fas", "fa-cloud-sun");
      }
    }
  </script>

  <!-- Language Toggle Script -->
  <script src="language-toggle.js"></script>
</body>
</html>
