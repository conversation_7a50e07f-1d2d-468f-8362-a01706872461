/**
 * Development Mode Indicator
 * Shows visual indicators when running in development mode
 */

// Add development mode indicator
function addDevModeIndicator() {
    // Only show if in development mode
    if (!window.isDevelopmentMode) return;
    
    // Add CSS for development mode
    const devCSS = document.createElement('link');
    devCSS.rel = 'stylesheet';
    devCSS.href = 'dev-mode.css';
    document.head.appendChild(devCSS);
    
    // Add development mode banner
    const banner = document.createElement('div');
    banner.className = 'dev-mode-banner';
    banner.innerHTML = `
        🚀 Development Mode - Authentication features are mocked. Use XAMPP for full functionality.
        <button class="close-btn" onclick="this.parentElement.remove(); document.body.classList.remove('dev-mode')">&times;</button>
    `;
    document.body.insertBefore(banner, document.body.firstChild);
    document.body.classList.add('dev-mode');
    
    // Add development mode indicator
    const indicator = document.createElement('div');
    indicator.className = 'dev-indicator';
    indicator.innerHTML = '🚀 DEV MODE';
    indicator.title = 'Development server - PHP features are mocked';
    document.body.appendChild(indicator);
    
    // Auto-hide banner after 10 seconds
    setTimeout(() => {
        if (banner.parentElement) {
            banner.style.animation = 'slideUp 0.5s ease-out forwards';
            setTimeout(() => {
                banner.remove();
                document.body.classList.remove('dev-mode');
            }, 500);
        }
    }, 10000);
}

// Add slideUp animation
const style = document.createElement('style');
style.textContent = `
    @keyframes slideUp {
        from {
            transform: translateY(0);
            opacity: 1;
        }
        to {
            transform: translateY(-100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', addDevModeIndicator);
} else {
    addDevModeIndicator();
}
