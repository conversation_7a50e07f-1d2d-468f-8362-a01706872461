<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DakshinaChitra Museum - Heritage Explorer</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
  <style>
    :root {
      --primary-color: #1a237e;
      --secondary-color: #457b9d;
      --accent-color: #e63946;
      --light-color: #f1faee;
      --dark-color: #1d3557;
      --success-color: #2e7d32;
      --warning-color: #ff9800;
      --danger-color: #c62828;
      --gray-100: #f8f9fa;
      --gray-200: #e9ecef;
      --gray-300: #dee2e6;
      --gray-400: #ced4da;
      --gray-500: #adb5bd;
      --gray-600: #6c757d;
      --gray-700: #495057;
      --gray-800: #343a40;
      --gray-900: #212529;
      --border-radius: 0.5rem;
      --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
      --transition: all 0.3s ease-in-out;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: var(--gray-800);
      background-color: var(--gray-100);
    }

    header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      text-align: center;
    }

    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      background-color: white;
      box-shadow: var(--box-shadow);
    }

    nav a {
      color: var(--gray-700);
      text-decoration: none;
      margin: 0 1rem;
      font-weight: 500;
      transition: var(--transition);
    }

    nav a:hover {
      color: var(--primary-color);
    }

    main {
      max-width: 1200px;
      margin: 2rem auto;
      padding: 0 1rem;
    }

    .site-details {
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    @media (min-width: 768px) {
      .site-details {
        grid-template-columns: 1fr 1fr;
      }
    }

    .site-image {
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
    }

    .site-image img {
      width: 100%;
      height: auto;
      display: block;
    }

    .site-info h1 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .site-info p {
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }

    .site-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .meta-item {
      display: flex;
      align-items: center;
      background-color: var(--gray-200);
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
    }

    .meta-item i {
      margin-right: 0.5rem;
      color: var(--secondary-color);
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      margin-top: 1.5rem;
    }

    .action-buttons button {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      background-color: var(--secondary-color);
      color: white;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
      display: flex;
      align-items: center;
    }

    .action-buttons button i {
      margin-right: 0.5rem;
    }

    .action-buttons button:hover {
      background-color: var(--primary-color);
    }

    .site-description {
      margin-bottom: 2rem;
    }

    .site-description h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .map-container {
      height: 400px;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      margin-bottom: 2rem;
    }

    .nearby-attractions {
      margin-bottom: 2rem;
    }

    .nearby-attractions h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .attractions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .attraction-card {
      background-color: white;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
    }

    .attraction-card:hover {
      transform: translateY(-5px);
    }

    .attraction-card img {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }

    .attraction-card-content {
      padding: 1rem;
    }

    .attraction-card-content h3 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    footer {
      background-color: var(--primary-color);
      color: white;
      text-align: center;
      padding: 1rem;
      margin-top: 2rem;
    }

    #backToTop {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      width: 3rem;
      height: 3rem;
      background-color: var(--primary-color);
      color: white;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
      display: none;
    }

    #backToTop:hover {
      background-color: var(--secondary-color);
    }
  </style>
</head>
<body>
  <header>
    <h1>Heritage Explorer</h1>
    <p>Discover Tamil Nadu's rich heritage and culture</p>
  </header>

  <nav>
    <div>
      <a href="website.html"><i class="fas fa-home"></i> Home</a>
      <a href="#"><i class="fas fa-map-marked-alt"></i> Explore</a>
      <a href="#"><i class="fas fa-info-circle"></i> About</a>
    </div>
    <div>
      <a href="#" id="toggleLang">தமிழ்</a>
    </div>
  </nav>

  <main>
    <div class="site-details">
      <div class="site-image">
        <img src="https://www.shutterstock.com/image-photo/dakshinachitra-chennai-tamilnadu-india-december-600nw-1300201657.jpg" alt="DakshinaChitra Museum">
      </div>
      <div class="site-info">
        <h1>DakshinaChitra Museum</h1>
        <div class="site-meta">
          <div class="meta-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>Chennai, Tamil Nadu</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-university"></i>
            <span>Museum</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-calendar-alt"></i>
            <span>Established in 1996</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-home"></i>
            <span>Living Heritage Museum</span>
          </div>
        </div>
        <p>DakshinaChitra Museum is a living heritage museum showcasing the art, architecture, lifestyles, and crafts of South India. Located near Chennai, this unique cultural center features reconstructed traditional houses from Tamil Nadu, Kerala, Karnataka, and Andhra Pradesh, along with artisan demonstrations, performances, and workshops.</p>
        <div class="action-buttons">
          <button id="playAudioButton"><i class="fas fa-volume-up"></i> Listen</button>
          <button id="favoriteButton"><i class="far fa-heart"></i> Favorite</button>
          <button id="shareButton"><i class="fas fa-share-alt"></i> Share</button>
        </div>
      </div>
    </div>

    <div class="site-description">
      <h2>About DakshinaChitra Museum</h2>
      <p>DakshinaChitra Museum is a unique living heritage museum located in Muttukadu, about 25 kilometers south of Chennai on the East Coast Road. Established in 1996 by the Madras Craft Foundation, the museum is dedicated to preserving and promoting the rich cultural heritage of South India. The name "DakshinaChitra" translates to "Art of the South," reflecting its focus on the diverse cultural traditions of Tamil Nadu, Kerala, Karnataka, and Andhra Pradesh.</p>
      <p>What sets DakshinaChitra apart from conventional museums is its approach to showcasing heritage. Instead of merely displaying artifacts behind glass cases, the museum has recreated traditional homes and living spaces from different regions of South India. These structures were carefully dismantled from their original locations, transported to the museum site, and meticulously reassembled to create an authentic experience. Each house is furnished with period furniture, household items, and tools that provide visitors with a glimpse into the daily lives of people from different communities and time periods.</p>
      <p>The museum complex spans 10 acres and features over 18 historical houses, including a Tamil Nadu merchant's house, a Kerala Syrian Christian house, a Karnataka potter's house, and an Andhra Pradesh weaver's house. Visitors can explore these structures, interact with artisans demonstrating traditional crafts such as pottery, weaving, bronze casting, and puppet making, and even participate in workshops to learn these skills. The museum also hosts regular cultural performances, including folk dances, music recitals, and theatrical presentations that bring the heritage of South India to life.</p>
      <p>Beyond its role as a tourist attraction, DakshinaChitra serves as an important educational and research center. It houses a vast collection of artifacts, photographs, and documents related to South Indian culture and history. The museum conducts educational programs for students, organizes cultural festivals celebrating regional traditions, and supports research on various aspects of South Indian heritage. Through these activities, DakshinaChitra plays a vital role in preserving and transmitting cultural knowledge to future generations while providing visitors with an immersive and engaging experience of South India's rich cultural tapestry.</p>
    </div>

    <div class="map-container">
      <!-- Map will be embedded here -->
      <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3919.0123456789!2d80.22!3d12.826!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3a525b79de7f381f%3A0xc7fb7ee1e5386e73!2sDakshinaChitra%20Museum!5e0!3m2!1sen!2sin!4v1652345678901!5m2!1sen!2sin" width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
    </div>

    <div class="nearby-attractions">
      <h2>Nearby Attractions</h2>
      <div class="attractions-grid">
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Crocodile_Bank_20190212182003.jpg" alt="Madras Crocodile Bank">
          <div class="attraction-card-content">
            <h3>Madras Crocodile Bank</h3>
            <p>A reptile zoo and herpetology research station housing various species of crocodiles, alligators, snakes, and turtles, with educational programs and conservation efforts.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/MGM_Dizzee_World_20190212182004.jpg" alt="MGM Dizzee World">
          <div class="attraction-card-content">
            <h3>MGM Dizzee World</h3>
            <p>An amusement park offering various rides, water attractions, and entertainment options for visitors of all ages, located along the East Coast Road.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Muttukadu_Boat_House_20190212182005.jpg" alt="Muttukadu Boat House">
          <div class="attraction-card-content">
            <h3>Muttukadu Boat House</h3>
            <p>A scenic backwater area offering boating facilities, water sports, and beautiful views of the surrounding landscape and mangrove forests.</p>
          </div>
        </div>
      </div>
    </div>
  </main>

  <footer>
    <p>&copy; 2025 Heritage Explorer | Explore Tamil Nadu</p>
  </footer>

  <div id="backToTop">
    <i class="fas fa-arrow-up"></i>
  </div>

  <script>
    // Back to Top Button
    const backToTop = document.getElementById("backToTop");
    window.addEventListener("scroll", () => {
      if (window.scrollY > 300) {
        backToTop.style.display = "flex";
      } else {
        backToTop.style.display = "none";
      }
    });
    backToTop.addEventListener("click", () => {
      window.scrollTo({ top: 0, behavior: "smooth" });
    });

    // Audio Playback
    document.getElementById("playAudioButton").addEventListener("click", () => {
      const description = document.querySelector(".site-info p").textContent;
      const utterance = new SpeechSynthesisUtterance(description);
      utterance.lang = "en-US";
      speechSynthesis.speak(utterance);
    });

    // Favorite Button
    document.getElementById("favoriteButton").addEventListener("click", function() {
      const icon = this.querySelector("i");
      if (icon.classList.contains("far")) {
        icon.classList.remove("far");
        icon.classList.add("fas");
        alert("Added to favorites!");
      } else {
        icon.classList.remove("fas");
        icon.classList.add("far");
        alert("Removed from favorites!");
      }
    });

    // Share Button
    document.getElementById("shareButton").addEventListener("click", () => {
      const shareData = {
        title: "DakshinaChitra Museum - Heritage Explorer",
        text: "Check out this living heritage museum in Tamil Nadu!",
        url: window.location.href
      };

      if (navigator.share) {
        navigator.share(shareData)
          .then(() => console.log("Shared successfully"))
          .catch((error) => console.error("Error sharing:", error));
      } else {
        navigator.clipboard.writeText(shareData.url)
          .then(() => alert("URL copied to clipboard"))
          .catch((err) => console.error("Could not copy text: ", err));
      }
    });
  </script>

  <!-- Language Toggle Script -->
  <script src="language-toggle.js"></script>
</body>
</html>
