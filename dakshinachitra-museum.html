<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DakshinaChitra Museum - Heritage Explorer</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
  <style>
    :root {
      --primary-color: #1a237e;
      --secondary-color: #457b9d;
      --accent-color: #e63946;
      --light-color: #f1faee;
      --dark-color: #1d3557;
      --success-color: #2e7d32;
      --warning-color: #ff9800;
      --danger-color: #c62828;
      --gray-100: #f8f9fa;
      --gray-200: #e9ecef;
      --gray-300: #dee2e6;
      --gray-400: #ced4da;
      --gray-500: #adb5bd;
      --gray-600: #6c757d;
      --gray-700: #495057;
      --gray-800: #343a40;
      --gray-900: #212529;
      --border-radius: 0.5rem;
      --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
      --transition: all 0.3s ease-in-out;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: var(--gray-800);
      background-color: var(--gray-100);
    }

    header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      text-align: center;
    }

    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      background-color: white;
      box-shadow: var(--box-shadow);
    }

    nav a {
      color: var(--gray-700);
      text-decoration: none;
      margin: 0 1rem;
      font-weight: 500;
      transition: var(--transition);
    }

    nav a:hover {
      color: var(--primary-color);
    }

    main {
      max-width: 1200px;
      margin: 2rem auto;
      padding: 0 1rem;
    }

    .site-details {
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    @media (min-width: 768px) {
      .site-details {
        grid-template-columns: 1fr 1fr;
      }
    }

    .site-image {
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
    }

    .site-image img {
      width: 100%;
      height: auto;
      display: block;
    }

    .site-info h1 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .site-info p {
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }

    .site-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .meta-item {
      display: flex;
      align-items: center;
      background-color: var(--gray-200);
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
    }

    .meta-item i {
      margin-right: 0.5rem;
      color: var(--secondary-color);
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      margin-top: 1.5rem;
    }

    .action-buttons button {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      background-color: var(--secondary-color);
      color: white;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
      display: flex;
      align-items: center;
    }

    .action-buttons button i {
      margin-right: 0.5rem;
    }

    .action-buttons button:hover {
      background-color: var(--primary-color);
    }

    .site-description {
      margin-bottom: 2rem;
    }

    .site-description h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .map-container {
      height: 400px;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      margin-bottom: 2rem;
    }

    .nearby-attractions {
      margin-bottom: 2rem;
    }

    .nearby-attractions h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .attractions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .attraction-card {
      background-color: white;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
    }

    .attraction-card:hover {
      transform: translateY(-5px);
    }

    .attraction-card img {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }

    .attraction-card-content {
      padding: 1rem;
    }

    .attraction-card-content h3 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    footer {
      background-color: var(--primary-color);
      color: white;
      text-align: center;
      padding: 1rem;
      margin-top: 2rem;
    }

    #backToTop {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      width: 3rem;
      height: 3rem;
      background-color: var(--primary-color);
      color: white;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
      display: none;
    }

    #backToTop:hover {
      background-color: var(--secondary-color);
    }

    /* Travel Information Section */
    .travel-info {
      margin-bottom: 2rem;
      background-color: white;
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      overflow: hidden;
    }

    .travel-info-header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .travel-info-header h2 {
      margin: 0;
      font-size: 1.5rem;
    }

    .travel-info-tabs {
      display: flex;
      background-color: var(--gray-200);
    }

    .travel-tab {
      padding: 1rem;
      cursor: pointer;
      flex: 1;
      text-align: center;
      font-weight: 500;
      transition: var(--transition);
      border-bottom: 3px solid transparent;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .travel-tab i {
      margin-right: 0.5rem;
    }

    .travel-tab.active {
      background-color: white;
      border-bottom-color: var(--accent-color);
    }

    .travel-tab:hover:not(.active) {
      background-color: var(--gray-300);
    }

    .travel-content {
      padding: 1.5rem;
    }

    .travel-panel {
      display: none;
    }

    .travel-panel.active {
      display: block;
    }

    /* Weather Panel */
    .weather-info {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      align-items: center;
    }

    .weather-current {
      display: flex;
      align-items: center;
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      flex: 1;
      min-width: 250px;
    }

    .weather-icon {
      font-size: 3rem;
      margin-right: 1rem;
      color: var(--secondary-color);
    }

    .weather-details h3 {
      margin: 0;
      color: var(--primary-color);
    }

    .weather-temp {
      font-size: 2rem;
      font-weight: bold;
    }

    .weather-desc {
      color: var(--gray-700);
    }

    .weather-forecast {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      flex: 2;
      min-width: 300px;
    }

    .forecast-day {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      text-align: center;
      flex: 1;
      min-width: 100px;
    }

    .forecast-day h4 {
      margin: 0 0 0.5rem 0;
      color: var(--primary-color);
    }

    .forecast-icon {
      font-size: 1.5rem;
      margin: 0.5rem 0;
      color: var(--secondary-color);
    }

    /* Transportation Panel */
    .transport-options {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .transport-option {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
      display: flex;
      flex-direction: column;
    }

    .transport-option h3 {
      display: flex;
      align-items: center;
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .transport-option h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .transport-option p {
      margin-bottom: 0.5rem;
    }

    .transport-option .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    /* Hours & Tickets Panel */
    .hours-container {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .opening-hours, .ticket-info {
      flex: 1;
      min-width: 250px;
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .opening-hours h3, .ticket-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .opening-hours h3 i, .ticket-info h3 i {
      margin-right: 0.5rem;
    }

    .hours-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .hours-list li {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .hours-list li:last-child {
      border-bottom: none;
    }

    .day {
      font-weight: 500;
    }

    .ticket-type {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .ticket-type:last-child {
      border-bottom: none;
    }

    .ticket-price {
      font-weight: bold;
    }

    /* Amenities Panel */
    .amenities-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .amenity-card {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .amenity-card h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .amenity-card h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .amenity-card p {
      margin-bottom: 0.5rem;
    }

    .amenity-card .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    .amenity-card .rating {
      color: var(--warning-color);
      margin-bottom: 0.5rem;
    }

    .weather-tips h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .local-transport h3, .best-time h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>Heritage Explorer</h1>
    <p>Discover Tamil Nadu's rich heritage and culture</p>
  </header>

  <nav>
    <div>
      <a href="website.html"><i class="fas fa-home"></i> Home</a>
      <a href="#"><i class="fas fa-map-marked-alt"></i> Explore</a>
      <a href="#"><i class="fas fa-info-circle"></i> About</a>
    </div>
    <div>
      <a href="#" id="toggleLang">தமிழ்</a>
    </div>
  </nav>

  <main>
    <div class="site-details">
      <div class="site-image">
        <img src="https://www.shutterstock.com/image-photo/dakshinachitra-chennai-tamilnadu-india-december-600nw-1300201657.jpg" alt="DakshinaChitra Museum">
      </div>
      <div class="site-info">
        <h1>DakshinaChitra Museum</h1>
        <div class="site-meta">
          <div class="meta-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>Chennai, Tamil Nadu</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-university"></i>
            <span>Museum</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-calendar-alt"></i>
            <span>Established in 1996</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-home"></i>
            <span>Living Heritage Museum</span>
          </div>
        </div>
        <p>DakshinaChitra Museum is a living heritage museum showcasing the art, architecture, lifestyles, and crafts of South India. Located near Chennai, this unique cultural center features reconstructed traditional houses from Tamil Nadu, Kerala, Karnataka, and Andhra Pradesh, along with artisan demonstrations, performances, and workshops.</p>
        <div class="action-buttons">
          <button id="playAudioButton"><i class="fas fa-volume-up"></i> Listen</button>
          <button id="favoriteButton"><i class="far fa-heart"></i> Favorite</button>
          <button id="shareButton"><i class="fas fa-share-alt"></i> Share</button>
        </div>
      </div>
    </div>

    <div class="site-description">
      <h2>About DakshinaChitra Museum</h2>
      <p>DakshinaChitra Museum is a unique living heritage museum located in Muttukadu, about 25 kilometers south of Chennai on the East Coast Road. Established in 1996 by the Madras Craft Foundation, the museum is dedicated to preserving and promoting the rich cultural heritage of South India. The name "DakshinaChitra" translates to "Art of the South," reflecting its focus on the diverse cultural traditions of Tamil Nadu, Kerala, Karnataka, and Andhra Pradesh.</p>
      <p>What sets DakshinaChitra apart from conventional museums is its approach to showcasing heritage. Instead of merely displaying artifacts behind glass cases, the museum has recreated traditional homes and living spaces from different regions of South India. These structures were carefully dismantled from their original locations, transported to the museum site, and meticulously reassembled to create an authentic experience. Each house is furnished with period furniture, household items, and tools that provide visitors with a glimpse into the daily lives of people from different communities and time periods.</p>
      <p>The museum complex spans 10 acres and features over 18 historical houses, including a Tamil Nadu merchant's house, a Kerala Syrian Christian house, a Karnataka potter's house, and an Andhra Pradesh weaver's house. Visitors can explore these structures, interact with artisans demonstrating traditional crafts such as pottery, weaving, bronze casting, and puppet making, and even participate in workshops to learn these skills. The museum also hosts regular cultural performances, including folk dances, music recitals, and theatrical presentations that bring the heritage of South India to life.</p>
      <p>Beyond its role as a tourist attraction, DakshinaChitra serves as an important educational and research center. It houses a vast collection of artifacts, photographs, and documents related to South Indian culture and history. The museum conducts educational programs for students, organizes cultural festivals celebrating regional traditions, and supports research on various aspects of South Indian heritage. Through these activities, DakshinaChitra plays a vital role in preserving and transmitting cultural knowledge to future generations while providing visitors with an immersive and engaging experience of South India's rich cultural tapestry.</p>
    </div>

    <div class="map-container">
      <!-- Map will be embedded here -->
      <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3919.0123456789!2d80.22!3d12.826!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3a525b79de7f381f%3A0xc7fb7ee1e5386e73!2sDakshinaChitra%20Museum!5e0!3m2!1sen!2sin!4v1652345678901!5m2!1sen!2sin" width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
    </div>

    <!-- Travel Information Section -->
    <div class="travel-info">
      <div class="travel-info-header">
        <h2>Practical Travel Information</h2>
        <div class="last-updated">Last updated: <span id="lastUpdated">Today</span></div>
      </div>

      <div class="travel-info-tabs">
        <div class="travel-tab active" data-tab="weather">
          <i class="fas fa-cloud-sun"></i> Weather
        </div>
        <div class="travel-tab" data-tab="transport">
          <i class="fas fa-bus"></i> Transportation
        </div>
        <div class="travel-tab" data-tab="hours">
          <i class="far fa-clock"></i> Hours & Tickets
        </div>
        <div class="travel-tab" data-tab="amenities">
          <i class="fas fa-concierge-bell"></i> Nearby Amenities
        </div>
      </div>

      <div class="travel-content">
        <!-- Weather Panel -->
        <div class="travel-panel active" id="weatherPanel">
          <div class="weather-info">
            <div class="weather-current">
              <div class="weather-icon">
                <i class="fas fa-sun"></i>
              </div>
              <div class="weather-details">
                <h3>Current Weather</h3>
                <div class="weather-temp">32°C</div>
                <div class="weather-desc">Sunny</div>
                <div class="weather-extra">Humidity: 78% | Wind: 12 km/h</div>
              </div>
            </div>

            <div class="weather-forecast">
              <div class="forecast-day">
                <h4>Tomorrow</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun"></i></div>
                <div class="forecast-temp">31°C</div>
                <div class="forecast-desc">Partly Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Wed</h4>
                <div class="forecast-icon"><i class="fas fa-cloud"></i></div>
                <div class="forecast-temp">30°C</div>
                <div class="forecast-desc">Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Thu</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun-rain"></i></div>
                <div class="forecast-temp">29°C</div>
                <div class="forecast-desc">Light Rain</div>
              </div>
            </div>
          </div>

          <div class="weather-tips">
            <h3>Weather Tips</h3>
            <p>Chennai and the East Coast Road area have a tropical climate with hot and humid conditions throughout most of the year. The best time to visit DakshinaChitra Museum is from November to February when the weather is relatively cooler with temperatures ranging from 20°C to 30°C. Summer (March-June) can be extremely hot with temperatures often exceeding 35°C.</p>
            <p>The museum is mostly open-air with some indoor exhibits, so it's advisable to carry water, wear light cotton clothing, and use sun protection. An umbrella can be useful both for sun protection and during unexpected rain showers. The museum complex has several shaded areas and rest spots where visitors can take breaks. Early morning visits (9:30 AM - 11:30 AM) are recommended to avoid the midday heat, especially during summer months.</p>
          </div>
        </div>

        <!-- Transportation Panel -->
        <div class="travel-panel" id="transportPanel">
          <div class="transport-options">
            <div class="transport-option">
              <h3><i class="fas fa-plane"></i> By Air</h3>
              <p>Chennai International Airport is well-connected to major cities.</p>
              <p class="distance">40 km from the museum (approx. 1-1.5 hours by car)</p>
              <p>Regular flights from all major Indian cities and international destinations.</p>
              <p>Pre-paid taxis and app-based cabs available from the airport (₹800-1,200).</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-train"></i> By Train</h3>
              <p>Chennai Central and Chennai Egmore are the main railway stations.</p>
              <p class="distance">30 km from the museum (approx. 1 hour by car)</p>
              <p>Well-connected to major cities across India.</p>
              <p>Taxis and app-based cabs available outside the stations (₹600-800).</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-bus"></i> By Bus</h3>
              <p>MTC (Metropolitan Transport Corporation) buses operate to Muttukadu.</p>
              <p>Route 109 from Adyar to Mamallapuram passes by the museum.</p>
              <p>State transport buses also ply on the East Coast Road.</p>
              <p>The bus stop is about 200 meters from the museum entrance.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-car"></i> By Car</h3>
              <p>Located on the scenic East Coast Road (ECR).</p>
              <p class="distance">25 km from Chennai city center (approx. 45-60 minutes)</p>
              <p>Well-maintained road with clear signage.</p>
              <p>Ample parking space available at the museum.</p>
            </div>
          </div>

          <div class="local-transport">
            <h3>Local Transportation</h3>
            <p>The East Coast Road is a popular tourist route with several attractions. If you're planning to visit multiple sites along the ECR (like Crocodile Bank, Muttukadu Boat House, and Mamallapuram), hiring a taxi for the day is the most convenient option (₹2,500-3,500). App-based cabs like Ola and Uber operate in the area but might have limited availability for return journeys. The museum is not easily accessible by local public transport, so having a dedicated vehicle is recommended. Some hotels in Chennai also offer day tours that include DakshinaChitra in their itinerary.</p>
          </div>
        </div>

        <!-- Hours & Tickets Panel -->
        <div class="travel-panel" id="hoursPanel">
          <div class="hours-container">
            <div class="opening-hours">
              <h3><i class="far fa-clock"></i> Opening Hours</h3>
              <ul class="hours-list">
                <li><span class="day">Tuesday - Sunday:</span> <span class="time">10:00 AM - 6:00 PM</span></li>
                <li><span class="day">Monday:</span> <span class="time">Closed</span></li>
                <li><span class="day">Public Holidays:</span> <span class="time">Open (except specified holidays)</span></li>
                <li><span class="day">Last Entry:</span> <span class="time">5:00 PM</span></li>
                <li><span class="day">Craft Demonstrations:</span> <span class="time">11:00 AM - 4:00 PM</span></li>
              </ul>
              <p class="hours-note">The museum remains closed on certain national holidays. Cultural performances are scheduled on weekends and holidays, typically at 11:00 AM and 3:00 PM. Photography is allowed in most areas, but there may be restrictions in certain galleries. A complete tour of the museum takes approximately 3-4 hours.</p>
            </div>

            <div class="ticket-info">
              <h3><i class="fas fa-ticket-alt"></i> Ticket Information</h3>
              <div class="ticket-type">
                <span>Indian Nationals:</span>
                <span class="ticket-price">₹150 per person</span>
              </div>
              <div class="ticket-type">
                <span>Foreign Nationals:</span>
                <span class="ticket-price">₹350 per person</span>
              </div>
              <div class="ticket-type">
                <span>Students with ID:</span>
                <span class="ticket-price">₹75 per person</span>
              </div>
              <div class="ticket-type">
                <span>Camera Fee:</span>
                <span class="ticket-price">₹100 (professional cameras)</span>
              </div>
              <p class="ticket-note">Children below 5 years enter free. Group discounts are available for parties of 20 or more. Guided tours are available in English, Tamil, and Hindi (₹200 per group). Tickets can be purchased at the entrance or online through the museum's official website. The ticket includes access to all exhibits, craft demonstrations, and scheduled performances.</p>
            </div>
          </div>

          <div class="best-time">
            <h3>Best Time to Visit</h3>
            <p>The best time to visit is on weekdays (Tuesday-Friday) when the museum is less crowded. Mornings (10:00 AM - 12:00 PM) are ideal for a peaceful experience and to avoid the afternoon heat. The museum gets busier on weekends and during school holidays. Special cultural festivals are organized during Pongal (January), Navratri (September-October), and the annual Crafts Bazaar (December), which are excellent times to visit for those interested in cultural performances and craft exhibitions. November to February is the ideal season to visit as the weather is pleasant and comfortable for exploring the open-air museum.</p>
          </div>
        </div>

        <!-- Amenities Panel -->
        <div class="travel-panel" id="amenitiesPanel">
          <div class="amenities-grid">
            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Ideal Beach Resort</strong></p>
              <p class="distance">5 km from museum</p>
              <p class="rating">★★★★☆ (4.2/5)</p>
              <p>Beachfront resort with comfortable rooms and good amenities.</p>
              <p>Price range: ₹5,000 - ₹8,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Radisson Blu Resort Temple Bay</strong></p>
              <p class="distance">15 km from museum (in Mamallapuram)</p>
              <p class="rating">★★★★★ (4.5/5)</p>
              <p>Luxury resort with private beach access and excellent facilities.</p>
              <p>Price range: ₹12,000 - ₹20,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>ECR Beach Resorts</strong></p>
              <p class="distance">3 km from museum</p>
              <p class="rating">★★★☆☆ (3.8/5)</p>
              <p>Mid-range accommodation with sea views and basic amenities.</p>
              <p>Price range: ₹3,000 - ₹5,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>DakshinaChitra Café</strong></p>
              <p class="distance">Inside the museum</p>
              <p class="rating">★★★★☆ (4.0/5)</p>
              <p>Serves authentic South Indian cuisine in a traditional setting.</p>
              <p>Price range: ₹400 - ₹600 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Wharf</strong></p>
              <p class="distance">7 km from museum</p>
              <p class="rating">★★★★☆ (4.3/5)</p>
              <p>Seafood restaurant with beautiful views of the Bay of Bengal.</p>
              <p>Price range: ₹1,200 - ₹1,800 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-shopping-bag"></i> Shopping</h3>
              <p><strong>Craft Shop at DakshinaChitra</strong></p>
              <p class="distance">Inside the museum</p>
              <p>Sells handcrafted items, textiles, and souvenirs from South India.</p>
              <p>Must-buy: Handloom textiles, terracotta items, and traditional toys</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-first-aid"></i> Medical Facilities</h3>
              <p><strong>First Aid Center</strong></p>
              <p class="distance">Inside the museum</p>
              <p>Basic medical assistance for minor injuries and discomfort.</p>
              <p>For emergencies, Apollo Hospitals (20 km) is the nearest major hospital.</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-info-circle"></i> Visitor Facilities</h3>
              <p><strong>Museum Amenities</strong></p>
              <p>Clean restrooms, drinking water stations, and rest areas available.</p>
              <p>Wheelchair accessibility for most areas (some traditional houses have steps).</p>
              <p>Locker facilities available for storing valuables and bags.</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="nearby-attractions">
      <h2>Nearby Attractions</h2>
      <div class="attractions-grid">
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Crocodile_Bank_20190212182003.jpg" alt="Madras Crocodile Bank">
          <div class="attraction-card-content">
            <h3>Madras Crocodile Bank</h3>
            <p>A reptile zoo and herpetology research station housing various species of crocodiles, alligators, snakes, and turtles, with educational programs and conservation efforts.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/MGM_Dizzee_World_20190212182004.jpg" alt="MGM Dizzee World">
          <div class="attraction-card-content">
            <h3>MGM Dizzee World</h3>
            <p>An amusement park offering various rides, water attractions, and entertainment options for visitors of all ages, located along the East Coast Road.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Muttukadu_Boat_House_20190212182005.jpg" alt="Muttukadu Boat House">
          <div class="attraction-card-content">
            <h3>Muttukadu Boat House</h3>
            <p>A scenic backwater area offering boating facilities, water sports, and beautiful views of the surrounding landscape and mangrove forests.</p>
          </div>
        </div>
      </div>
    </div>
  </main>

  <footer>
    <p>&copy; 2025 Heritage Explorer | Explore Tamil Nadu</p>
  </footer>

  <div id="backToTop">
    <i class="fas fa-arrow-up"></i>
  </div>

  <script>
    // Back to Top Button
    const backToTop = document.getElementById("backToTop");
    window.addEventListener("scroll", () => {
      if (window.scrollY > 300) {
        backToTop.style.display = "flex";
      } else {
        backToTop.style.display = "none";
      }
    });
    backToTop.addEventListener("click", () => {
      window.scrollTo({ top: 0, behavior: "smooth" });
    });

    // Audio Playback
    document.getElementById("playAudioButton").addEventListener("click", () => {
      const description = document.querySelector(".site-info p").textContent;
      const utterance = new SpeechSynthesisUtterance(description);
      utterance.lang = "en-US";
      speechSynthesis.speak(utterance);
    });

    // Favorite Button
    document.getElementById("favoriteButton").addEventListener("click", function() {
      const icon = this.querySelector("i");
      if (icon.classList.contains("far")) {
        icon.classList.remove("far");
        icon.classList.add("fas");
        alert("Added to favorites!");
      } else {
        icon.classList.remove("fas");
        icon.classList.add("far");
        alert("Removed from favorites!");
      }
    });

    // Share Button
    document.getElementById("shareButton").addEventListener("click", () => {
      const shareData = {
        title: "DakshinaChitra Museum - Heritage Explorer",
        text: "Check out this living heritage museum in Tamil Nadu!",
        url: window.location.href
      };

      if (navigator.share) {
        navigator.share(shareData)
          .then(() => console.log("Shared successfully"))
          .catch((error) => console.error("Error sharing:", error));
      } else {
        navigator.clipboard.writeText(shareData.url)
          .then(() => alert("URL copied to clipboard"))
          .catch((err) => console.error("Could not copy text: ", err));
      }
    });

    // Initialize the travel info tabs
    document.addEventListener("DOMContentLoaded", function() {
      // Set the last updated date
      const today = new Date();
      document.getElementById("lastUpdated").textContent = today.toLocaleDateString();

      // Tab functionality
      const tabs = document.querySelectorAll(".travel-tab");
      tabs.forEach(tab => {
        tab.addEventListener("click", function() {
          // Remove active class from all tabs
          tabs.forEach(t => t.classList.remove("active"));

          // Add active class to clicked tab
          this.classList.add("active");

          // Hide all panels
          const panels = document.querySelectorAll(".travel-panel");
          panels.forEach(panel => panel.classList.remove("active"));

          // Show the corresponding panel
          const tabId = this.getAttribute("data-tab");
          document.getElementById(tabId + "Panel").classList.add("active");
        });
      });

      // Fetch weather data (mock)
      fetchWeatherData();
    });

    // Weather API Integration (Mock)
    function fetchWeatherData() {
      // Coordinates for Chennai/East Coast Road
      const lat = 12.8260;
      const lon = 80.2200;

      // Mock weather data
      const mockWeatherData = {
        current: {
          temp: 32,
          humidity: 78,
          wind_speed: 12,
          weather: [{ main: "Clear", description: "Sunny", icon: "01d" }]
        },
        daily: [
          {
            dt: Date.now() + 86400000, // Tomorrow
            temp: { day: 31 },
            weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
          },
          {
            dt: Date.now() + 172800000, // Day after tomorrow
            temp: { day: 30 },
            weather: [{ main: "Clouds", description: "Cloudy", icon: "03d" }]
          },
          {
            dt: Date.now() + 259200000, // 3 days from now
            temp: { day: 29 },
            weather: [{ main: "Rain", description: "Light Rain", icon: "10d" }]
          }
        ]
      };

      updateWeatherUI(mockWeatherData);
    }

    function updateWeatherUI(data) {
      // Update current weather
      document.querySelector(".weather-temp").textContent = `${data.current.temp}°C`;
      document.querySelector(".weather-desc").textContent = data.current.weather[0].description;
      document.querySelector(".weather-extra").textContent = `Humidity: ${data.current.humidity}% | Wind: ${data.current.wind_speed} km/h`;

      // Update weather icon
      const weatherIconElement = document.querySelector(".weather-icon i");
      updateWeatherIcon(weatherIconElement, data.current.weather[0].main);

      // Update forecast
      const forecastDays = document.querySelectorAll(".forecast-day");
      data.daily.forEach((day, index) => {
        if (index < forecastDays.length) {
          const dayElement = forecastDays[index];
          const date = new Date(day.dt);
          const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });

          dayElement.querySelector("h4").textContent = index === 0 ? "Tomorrow" : dayName;
          dayElement.querySelector(".forecast-temp").textContent = `${day.temp.day}°C`;
          dayElement.querySelector(".forecast-desc").textContent = day.weather[0].description;

          // Update forecast icon
          const iconElement = dayElement.querySelector(".forecast-icon i");
          updateWeatherIcon(iconElement, day.weather[0].main);
        }
      });
    }

    function updateWeatherIcon(iconElement, weatherType) {
      // Remove all existing weather classes
      iconElement.className = "";

      // Add the appropriate weather icon class
      switch (weatherType) {
        case "Clear":
          iconElement.classList.add("fas", "fa-sun");
          break;
        case "Clouds":
          iconElement.classList.add("fas", "fa-cloud");
          break;
        case "Rain":
        case "Drizzle":
          iconElement.classList.add("fas", "fa-cloud-rain");
          break;
        case "Thunderstorm":
          iconElement.classList.add("fas", "fa-bolt");
          break;
        case "Snow":
          iconElement.classList.add("fas", "fa-snowflake");
          break;
        case "Mist":
        case "Fog":
        case "Haze":
          iconElement.classList.add("fas", "fa-smog");
          break;
        default:
          iconElement.classList.add("fas", "fa-cloud-sun");
      }
    }
  </script>

  <!-- Language Toggle Script -->
  <script src="language-toggle.js"></script>
</body>
</html>
