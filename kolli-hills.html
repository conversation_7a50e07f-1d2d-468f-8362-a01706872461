<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON>lli Hills - Heritage Explorer</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
  <style>
    :root {
      --primary-color: #1a237e;
      --secondary-color: #457b9d;
      --accent-color: #e63946;
      --light-color: #f1faee;
      --dark-color: #1d3557;
      --success-color: #2e7d32;
      --warning-color: #ff9800;
      --danger-color: #c62828;
      --gray-100: #f8f9fa;
      --gray-200: #e9ecef;
      --gray-300: #dee2e6;
      --gray-400: #ced4da;
      --gray-500: #adb5bd;
      --gray-600: #6c757d;
      --gray-700: #495057;
      --gray-800: #343a40;
      --gray-900: #212529;
      --border-radius: 0.5rem;
      --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
      --transition: all 0.3s ease-in-out;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: var(--gray-800);
      background-color: var(--gray-100);
    }

    header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      text-align: center;
    }

    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      background-color: white;
      box-shadow: var(--box-shadow);
    }

    nav a {
      color: var(--gray-700);
      text-decoration: none;
      margin: 0 1rem;
      font-weight: 500;
      transition: var(--transition);
    }

    nav a:hover {
      color: var(--primary-color);
    }

    main {
      max-width: 1200px;
      margin: 2rem auto;
      padding: 0 1rem;
    }

    .site-details {
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    @media (min-width: 768px) {
      .site-details {
        grid-template-columns: 1fr 1fr;
      }
    }

    .site-image {
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
    }

    .site-image img {
      width: 100%;
      height: auto;
      display: block;
    }

    .site-info h1 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .site-info p {
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }

    .site-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .meta-item {
      display: flex;
      align-items: center;
      background-color: var(--gray-200);
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
    }

    .meta-item i {
      margin-right: 0.5rem;
      color: var(--secondary-color);
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      margin-top: 1.5rem;
    }

    .action-buttons button {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      background-color: var(--secondary-color);
      color: white;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
      display: flex;
      align-items: center;
    }

    .action-buttons button i {
      margin-right: 0.5rem;
    }

    .action-buttons button:hover {
      background-color: var(--primary-color);
    }

    .site-description {
      margin-bottom: 2rem;
    }

    .site-description h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .map-container {
      height: 400px;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      margin-bottom: 2rem;
    }

    .nearby-attractions {
      margin-bottom: 2rem;
    }

    .nearby-attractions h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .attractions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .attraction-card {
      background-color: white;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
    }

    .attraction-card:hover {
      transform: translateY(-5px);
    }

    .attraction-card img {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }

    .attraction-card-content {
      padding: 1rem;
    }

    .attraction-card-content h3 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    footer {
      background-color: var(--primary-color);
      color: white;
      text-align: center;
      padding: 1rem;
      margin-top: 2rem;
    }

    #backToTop {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      width: 3rem;
      height: 3rem;
      background-color: var(--primary-color);
      color: white;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
      display: none;
    }

    #backToTop:hover {
      background-color: var(--secondary-color);
    }

    /* Travel Information Section */
    .travel-info {
      margin-bottom: 2rem;
      background-color: white;
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      overflow: hidden;
    }

    .travel-info-header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .travel-info-header h2 {
      margin: 0;
      font-size: 1.5rem;
    }

    .travel-info-tabs {
      display: flex;
      background-color: var(--gray-200);
    }

    .travel-tab {
      padding: 1rem;
      cursor: pointer;
      flex: 1;
      text-align: center;
      font-weight: 500;
      transition: var(--transition);
      border-bottom: 3px solid transparent;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .travel-tab i {
      margin-right: 0.5rem;
    }

    .travel-tab.active {
      background-color: white;
      border-bottom-color: var(--accent-color);
    }

    .travel-tab:hover:not(.active) {
      background-color: var(--gray-300);
    }

    .travel-content {
      padding: 1.5rem;
    }

    .travel-panel {
      display: none;
    }

    .travel-panel.active {
      display: block;
    }

    /* Weather Panel */
    .weather-info {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      align-items: center;
    }

    .weather-current {
      display: flex;
      align-items: center;
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      flex: 1;
      min-width: 250px;
    }

    .weather-icon {
      font-size: 3rem;
      margin-right: 1rem;
      color: var(--secondary-color);
    }

    .weather-details h3 {
      margin: 0;
      color: var(--primary-color);
    }

    .weather-temp {
      font-size: 2rem;
      font-weight: bold;
    }

    .weather-desc {
      color: var(--gray-700);
    }

    .weather-forecast {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      flex: 2;
      min-width: 300px;
    }

    .forecast-day {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      text-align: center;
      flex: 1;
      min-width: 100px;
    }

    .forecast-day h4 {
      margin: 0 0 0.5rem 0;
      color: var(--primary-color);
    }

    .forecast-icon {
      font-size: 1.5rem;
      margin: 0.5rem 0;
      color: var(--secondary-color);
    }

    /* Transportation Panel */
    .transport-options {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .transport-option {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
      display: flex;
      flex-direction: column;
    }

    .transport-option h3 {
      display: flex;
      align-items: center;
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .transport-option h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .transport-option p {
      margin-bottom: 0.5rem;
    }

    .transport-option .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    /* Hours & Activities Panel */
    .hours-container {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .opening-hours, .activities-info {
      flex: 1;
      min-width: 250px;
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .opening-hours h3, .activities-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .opening-hours h3 i, .activities-info h3 i {
      margin-right: 0.5rem;
    }

    .hours-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .hours-list li {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .hours-list li:last-child {
      border-bottom: none;
    }

    .day {
      font-weight: 500;
    }

    .activity-item {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .activity-item:last-child {
      border-bottom: none;
    }

    .activity-name {
      font-weight: 500;
    }

    .activity-price {
      font-weight: bold;
    }

    /* Amenities Panel */
    .amenities-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .amenity-card {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .amenity-card h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .amenity-card h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .amenity-card p {
      margin-bottom: 0.5rem;
    }

    .amenity-card .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    .amenity-card .rating {
      color: var(--warning-color);
      margin-bottom: 0.5rem;
    }

    .weather-tips h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .local-transport h3, .best-time h3, .trekking-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .trekking-routes {
      margin-top: 1.5rem;
    }

    .trek-route {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      margin-bottom: 1rem;
    }

    .trek-route h4 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
    }

    .trek-route h4 i {
      margin-right: 0.5rem;
    }

    .trek-details {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 0.5rem;
    }

    .trek-detail {
      display: flex;
      align-items: center;
      font-size: 0.9rem;
      color: var(--gray-700);
    }

    .trek-detail i {
      margin-right: 0.25rem;
      font-size: 0.8rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>Heritage Explorer</h1>
    <p>Discover Tamil Nadu's rich heritage and culture</p>
  </header>

  <nav>
    <div>
      <a href="website.html"><i class="fas fa-home"></i> Home</a>
      <a href="#"><i class="fas fa-map-marked-alt"></i> Explore</a>
      <a href="#"><i class="fas fa-info-circle"></i> About</a>
    </div>
    <div>
      <a href="#" id="toggleLang">தமிழ்</a>
    </div>
  </nav>

  <main>
    <div class="site-details">
      <div class="site-image">
        <img src="https://t4.ftcdn.net/jpg/03/13/43/13/360_F_313431363_UClpHmoAEm0R2hmFU93piya9ScEFSiYm.jpg" alt="Kolli Hills">
      </div>
      <div class="site-info">
        <h1>Kolli Hills</h1>
        <div class="site-meta">
          <div class="meta-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>Namakkal, Tamil Nadu</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-mountain"></i>
            <span>Hill Station</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-road"></i>
            <span>70 Hairpin Bends</span>
          </div>
        </div>
        <p>Kolli Hills, known as the "Mountain of Death" due to its 70 challenging hairpin bends, is a serene hill station in Namakkal district. Famous for its Agaya Gangai waterfalls, medicinal herbs, and untouched natural beauty, it offers a perfect retreat for adventure seekers and nature lovers.</p>
        <div class="action-buttons">
          <button id="playAudioButton"><i class="fas fa-volume-up"></i> Listen</button>
          <button id="favoriteButton"><i class="far fa-heart"></i> Favorite</button>
          <button id="shareButton"><i class="fas fa-share-alt"></i> Share</button>
        </div>
      </div>
    </div>

    <div class="site-description">
      <h2>About Kolli Hills</h2>
      <p>Kolli Hills, locally known as Kolli Malai, is a small mountain range located in the Namakkal district of Tamil Nadu. Rising to an elevation of about 1,300 meters above sea level, these hills are part of the Eastern Ghats and are known for their natural beauty, biodiversity, and cultural significance.</p>
      <p>The name "Kolli" is derived from the Tamil word "Kollipavai," referring to a fearsome mountain goddess who was believed to kill anyone who dared to climb the hills. This gave rise to the nickname "Mountain of Death." Today, the journey to Kolli Hills involves navigating 70 hairpin bends on a steep, winding road, which is both a thrilling adventure and a test of driving skills.</p>
      <p>The hills are known for their rich biodiversity, with dense forests that are home to various species of flora and fauna. The region is particularly famous for its medicinal plants and herbs, which have been used in traditional Siddha medicine for centuries. The Arapaleeswarar Temple, dedicated to Lord Shiva, is a significant religious site in the hills and is believed to be over 1,500 years old.</p>
      <p>One of the most popular attractions in Kolli Hills is the Agaya Gangai waterfall, which cascades from a height of about 300 feet. The trek to the waterfall is challenging but rewarding, offering breathtaking views of the surrounding landscape. Other attractions include the Boat House, Botanical Garden, and various viewpoints that offer panoramic vistas of the valleys below. The hills are also known for their coffee, pepper, and other spice plantations, which contribute to the local economy.</p>
    </div>

    <div class="map-container">
      <!-- Map will be embedded here -->
      <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d31476.62311012707!2d78.33!3d11.248!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3ba96f52bfb04917%3A0x9eb559aa82d1006c!2sKolli%20Hills%2C%20Tamil%20Nadu!5e0!3m2!1sen!2sin!4v1652345678901!5m2!1sen!2sin" width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy" title="Map of Kolli Hills"></iframe>
    </div>

    <!-- Travel Information Section -->
    <div class="travel-info">
      <div class="travel-info-header">
        <h2>Practical Travel Information</h2>
        <div class="last-updated">Last updated: <span id="lastUpdated">Today</span></div>
      </div>

      <div class="travel-info-tabs">
        <div class="travel-tab active" data-tab="weather">
          <i class="fas fa-cloud-sun"></i> Weather
        </div>
        <div class="travel-tab" data-tab="transport">
          <i class="fas fa-bus"></i> Transportation
        </div>
        <div class="travel-tab" data-tab="hours">
          <i class="fas fa-hiking"></i> Activities & Hours
        </div>
        <div class="travel-tab" data-tab="amenities">
          <i class="fas fa-concierge-bell"></i> Nearby Amenities
        </div>
      </div>

      <div class="travel-content">
        <!-- Weather Panel -->
        <div class="travel-panel active" id="weatherPanel">
          <div class="weather-info">
            <div class="weather-current">
              <div class="weather-icon">
                <i class="fas fa-cloud-sun"></i>
              </div>
              <div class="weather-details">
                <h3>Current Weather</h3>
                <div class="weather-temp">24°C</div>
                <div class="weather-desc">Partly Cloudy</div>
                <div class="weather-extra">Humidity: 85% | Wind: 12 km/h</div>
              </div>
            </div>

            <div class="weather-forecast">
              <div class="forecast-day">
                <h4>Tomorrow</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun"></i></div>
                <div class="forecast-temp">25°C</div>
                <div class="forecast-desc">Partly Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Wed</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-rain"></i></div>
                <div class="forecast-temp">23°C</div>
                <div class="forecast-desc">Light Rain</div>
              </div>
              <div class="forecast-day">
                <h4>Thu</h4>
                <div class="forecast-icon"><i class="fas fa-sun"></i></div>
                <div class="forecast-temp">26°C</div>
                <div class="forecast-desc">Sunny</div>
              </div>
            </div>
          </div>

          <div class="weather-tips">
            <h3>Weather Tips</h3>
            <p>Kolli Hills enjoys a pleasant climate throughout the year, with temperatures ranging from 15°C to 30°C. The hills are located at an elevation of about 1,300 meters above sea level, resulting in cooler temperatures compared to the surrounding plains. The best time to visit is from October to March when the weather is cool and pleasant.</p>
            <p>During the monsoon season (June to September), the hills receive moderate to heavy rainfall, which can make trekking trails slippery and some roads difficult to navigate. However, this is also when the waterfalls are at their most spectacular and the landscape is lush and green. Summer months (April-May) are relatively warmer but still pleasant compared to the plains.</p>
            <p>Due to the elevation, temperatures can drop significantly in the evenings, especially during winter months (December-February). It's advisable to carry light woolens or jackets even if you're visiting during summer. The weather in the hills can change quickly, so it's a good idea to check the forecast before planning outdoor activities. Mornings are often misty, offering beautiful views but requiring careful driving on the winding roads.</p>
          </div>
        </div>

        <!-- Transportation Panel -->
        <div class="travel-panel" id="transportPanel">
          <div class="transport-options">
            <div class="transport-option">
              <h3><i class="fas fa-plane"></i> By Air</h3>
              <p>Trichy Airport is the nearest airport.</p>
              <p class="distance">90 km from Kolli Hills (approx. 3 hours by car)</p>
              <p>Salem Airport is another option.</p>
              <p class="distance">100 km from Kolli Hills (approx. 3.5 hours by car)</p>
              <p>Taxi services available from both airports (₹2,000-3,000).</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-train"></i> By Train</h3>
              <p>Salem Junction is the nearest major railway station.</p>
              <p class="distance">80 km from Kolli Hills (approx. 2.5 hours by car)</p>
              <p>Rasipuram is the nearest railway station.</p>
              <p class="distance">40 km from Kolli Hills (approx. 1.5 hours by car)</p>
              <p>Taxis and buses available from both stations.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-bus"></i> By Bus</h3>
              <p>Regular buses from Salem, Namakkal, and Rasipuram.</p>
              <p>Government buses run from Salem to Semmedu (main town in Kolli Hills).</p>
              <p>Private buses also available but less frequent.</p>
              <p>The journey involves navigating 70 hairpin bends.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-car"></i> By Car</h3>
              <p>Well-connected by roads from major cities.</p>
              <p>From Chennai: NH-79 (350 km, approx. 7 hours)</p>
              <p>From Salem: NH-79 (80 km, approx. 2.5 hours)</p>
              <p>From Namakkal: State Highway (45 km, approx. 1.5 hours)</p>
              <p>The drive includes 70 hairpin bends - challenging but scenic.</p>
            </div>
          </div>

          <div class="local-transport">
            <h3>Local Transportation</h3>
            <p>Within Kolli Hills, local transportation options are limited. The main town, Semmedu, serves as the central hub for the hills. Auto-rickshaws are available for short distances, but they are not as common as in the plains. Jeeps are a popular mode of transport for navigating the rugged terrain, especially to reach remote attractions like the Agaya Gangai waterfall.</p>
            <p>For exploring the hills thoroughly, it's recommended to have your own vehicle or hire a local taxi/jeep for the day (₹1,500-2,000). Some guesthouses and resorts also offer vehicle rentals or can arrange for transportation to major attractions. If you're adventurous, some parts of the hills can be explored on foot, but be prepared for steep trails and varying terrain. Local buses run on limited routes within the hills, primarily connecting Semmedu to other small settlements.</p>
          </div>
        </div>

        <!-- Hours & Activities Panel -->
        <div class="travel-panel" id="hoursPanel">
          <div class="hours-container">
            <div class="opening-hours">
              <h3><i class="far fa-clock"></i> Attraction Hours</h3>
              <ul class="hours-list">
                <li><span class="day">Agaya Gangai Waterfall:</span> <span class="time">6:00 AM - 5:00 PM</span></li>
                <li><span class="day">Arapaleeswarar Temple:</span> <span class="time">6:00 AM - 12:00 PM, 4:00 PM - 8:00 PM</span></li>
                <li><span class="day">Botanical Garden:</span> <span class="time">9:00 AM - 6:00 PM</span></li>
                <li><span class="day">Boat House:</span> <span class="time">9:00 AM - 5:00 PM</span></li>
                <li><span class="day">Siddhar Caves:</span> <span class="time">8:00 AM - 5:00 PM</span></li>
              </ul>
              <p class="hours-note">Most natural attractions in Kolli Hills are accessible throughout the day, but it's advisable to visit waterfalls and trekking spots during daylight hours for safety reasons. The trek to Agaya Gangai waterfall takes approximately 1-1.5 hours one way, so plan to start by 2:00 PM at the latest to ensure you can return before dark. During monsoon season (June-September), some attractions may have restricted hours or might be temporarily closed due to heavy rainfall and safety concerns.</p>
            </div>

            <div class="activities-info">
              <h3><i class="fas fa-hiking"></i> Activities & Prices</h3>
              <div class="activity-item">
                <span class="activity-name">Trekking to Agaya Gangai</span>
                <span class="activity-price">Free (Guide: ₹500)</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Boating at Boat House</span>
                <span class="activity-price">₹150 per person</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Botanical Garden Entry</span>
                <span class="activity-price">₹20 per person</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Zipline Adventure</span>
                <span class="activity-price">₹500 per person</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Jeep Safari</span>
                <span class="activity-price">₹1,500 for 4 hours</span>
              </div>
              <p class="activity-note">Adventure activities like ziplining and rock climbing are available at specific locations in Kolli Hills. These activities are operated by private vendors and prices may vary. It's recommended to book these activities through your accommodation or through verified tourism operators. Hiring a local guide for trekking is highly recommended, especially for first-time visitors, as some trails can be confusing and challenging to navigate.</p>
            </div>
          </div>

          <div class="trekking-info">
            <h3>Popular Trekking Routes</h3>
            <p>Kolli Hills offers several trekking routes of varying difficulty levels, making it a paradise for adventure enthusiasts. Here are some of the most popular trekking routes:</p>

            <div class="trekking-routes">
              <div class="trek-route">
                <h4><i class="fas fa-mountain"></i> Agaya Gangai Waterfall Trek</h4>
                <div class="trek-details">
                  <span class="trek-detail"><i class="fas fa-clock"></i> Duration: 3-4 hours (round trip)</span>
                  <span class="trek-detail"><i class="fas fa-signal"></i> Difficulty: Moderate</span>
                  <span class="trek-detail"><i class="fas fa-route"></i> Distance: 2.5 km one way</span>
                </div>
                <p>This is the most popular trek in Kolli Hills, leading to the magnificent 300-foot Agaya Gangai waterfall. The trail involves descending approximately 1,300 steps and then navigating through a forest path. The trek is moderately challenging but rewarding with stunning views of the waterfall. Best time for this trek is post-monsoon (October-December) when the waterfall is in full flow.</p>
              </div>

              <div class="trek-route">
                <h4><i class="fas fa-mountain"></i> Mini Falls Trek</h4>
                <div class="trek-details">
                  <span class="trek-detail"><i class="fas fa-clock"></i> Duration: 1-2 hours (round trip)</span>
                  <span class="trek-detail"><i class="fas fa-signal"></i> Difficulty: Easy</span>
                  <span class="trek-detail"><i class="fas fa-route"></i> Distance: 1 km one way</span>
                </div>
                <p>An easier alternative to the Agaya Gangai trek, the Mini Falls trek is suitable for beginners and families. The trail is well-marked and leads to a smaller but charming waterfall. The trek offers beautiful views of the surrounding valleys and is particularly scenic during the monsoon and post-monsoon seasons.</p>
              </div>

              <div class="trek-route">
                <h4><i class="fas fa-mountain"></i> Siddhar Caves Trek</h4>
                <div class="trek-details">
                  <span class="trek-detail"><i class="fas fa-clock"></i> Duration: 5-6 hours (round trip)</span>
                  <span class="trek-detail"><i class="fas fa-signal"></i> Difficulty: Challenging</span>
                  <span class="trek-detail"><i class="fas fa-route"></i> Distance: 4 km one way</span>
                </div>
                <p>This challenging trek leads to ancient caves believed to have been inhabited by Siddhar saints. The trail is less frequented and involves navigating through dense forests and rocky terrain. A guide is essential for this trek. The caves and the surrounding area have historical and spiritual significance, making it a unique trekking experience.</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Amenities Panel -->
        <div class="travel-panel" id="amenitiesPanel">
          <div class="amenities-grid">
            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Nallathambi Resort</strong></p>
              <p class="distance">Near Semmedu (central area)</p>
              <p class="rating">★★★☆☆ (3.8/5)</p>
              <p>Mid-range resort with mountain views and basic amenities.</p>
              <p>Price range: ₹2,000 - ₹3,500 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Green Valley Resort</strong></p>
              <p class="distance">2 km from Semmedu</p>
              <p class="rating">★★★★☆ (4.2/5)</p>
              <p>Eco-friendly resort with cottages and garden views.</p>
              <p>Price range: ₹3,000 - ₹5,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Tamil Nadu Tourism Development Corporation (TTDC) Hotel</strong></p>
              <p class="distance">Near Semmedu</p>
              <p class="rating">★★★☆☆ (3.5/5)</p>
              <p>Government-run hotel with basic facilities and reliable service.</p>
              <p>Price range: ₹1,500 - ₹2,500 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Kolli Hills Mess</strong></p>
              <p class="distance">Semmedu</p>
              <p class="rating">★★★★☆ (4.0/5)</p>
              <p>Local restaurant serving authentic Tamil cuisine.</p>
              <p>Price range: ₹200 - ₹400 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Green Valley Restaurant</strong></p>
              <p class="distance">2 km from Semmedu</p>
              <p class="rating">★★★★☆ (4.3/5)</p>
              <p>Restaurant serving both South Indian and North Indian cuisine.</p>
              <p>Price range: ₹400 - ₹700 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-shopping-bag"></i> Shopping</h3>
              <p><strong>Local Market</strong></p>
              <p class="distance">Semmedu</p>
              <p>Small market selling local produce, spices, and handicrafts.</p>
              <p>Must-buy: Coffee, pepper, honey, and medicinal herbs</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-first-aid"></i> Medical Facilities</h3>
              <p><strong>Government Primary Health Center</strong></p>
              <p class="distance">Semmedu</p>
              <p>Basic medical facilities for emergencies.</p>
              <p>For serious medical issues, Namakkal (45 km) has better facilities.</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-info-circle"></i> Tourist Information</h3>
              <p><strong>Tamil Nadu Tourism Office</strong></p>
              <p class="distance">Semmedu</p>
              <p>Provides maps, brochures, and guided tour information.</p>
              <p>Contact: +91 4283 220 235</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="nearby-attractions">
      <h2>Nearby Attractions</h2>
      <div class="attractions-grid">
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Agaya_Gangai_Waterfalls_20190212182003.jpg" alt="Agaya Gangai Waterfalls">
          <div class="attraction-card-content">
            <h3>Agaya Gangai Waterfalls</h3>
            <p>A spectacular waterfall cascading from a height of 300 feet, accessible via a challenging trek through lush forests.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Arapaleeswarar_Temple_20190212182004.jpg" alt="Arapaleeswarar Temple">
          <div class="attraction-card-content">
            <h3>Arapaleeswarar Temple</h3>
            <p>An ancient temple dedicated to Lord Shiva, believed to be over 1,500 years old and featuring unique architectural elements.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Botanical_Garden_Kolli_Hills_20190212182005.jpg" alt="Botanical Garden">
          <div class="attraction-card-content">
            <h3>Botanical Garden</h3>
            <p>A well-maintained garden showcasing the rich biodiversity of the region, including rare medicinal plants and herbs.</p>
          </div>
        </div>
      </div>
    </div>
  </main>

  <footer>
    <p>&copy; 2025 Heritage Explorer | Explore Tamil Nadu</p>
  </footer>

  <div id="backToTop">
    <i class="fas fa-arrow-up"></i>
  </div>

  <script>
    // Back to Top Button
    const backToTop = document.getElementById("backToTop");
    window.addEventListener("scroll", () => {
      if (window.scrollY > 300) {
        backToTop.style.display = "flex";
      } else {
        backToTop.style.display = "none";
      }
    });
    backToTop.addEventListener("click", () => {
      window.scrollTo({ top: 0, behavior: "smooth" });
    });

    // Audio Playback
    document.getElementById("playAudioButton").addEventListener("click", () => {
      const description = document.querySelector(".site-info p").textContent;
      const utterance = new SpeechSynthesisUtterance(description);
      utterance.lang = "en-US";
      speechSynthesis.speak(utterance);
    });

    // Favorite Button
    document.getElementById("favoriteButton").addEventListener("click", function() {
      const icon = this.querySelector("i");
      if (icon.classList.contains("far")) {
        icon.classList.remove("far");
        icon.classList.add("fas");
        alert("Added to favorites!");
      } else {
        icon.classList.remove("fas");
        icon.classList.add("far");
        alert("Removed from favorites!");
      }
    });

    // Share Button
    document.getElementById("shareButton").addEventListener("click", () => {
      const shareData = {
        title: "Kolli Hills - Heritage Explorer",
        text: "Check out this beautiful hill station in Tamil Nadu!",
        url: window.location.href
      };

      if (navigator.share) {
        navigator.share(shareData)
          .then(() => console.log("Shared successfully"))
          .catch((error) => console.error("Error sharing:", error));
      } else {
        navigator.clipboard.writeText(shareData.url)
          .then(() => alert("URL copied to clipboard"))
          .catch((err) => console.error("Could not copy text: ", err));
      }
    });

    // Initialize the travel info tabs
    document.addEventListener("DOMContentLoaded", function() {
      // Set the last updated date
      const today = new Date();
      document.getElementById("lastUpdated").textContent = today.toLocaleDateString();

      // Tab functionality
      const tabs = document.querySelectorAll(".travel-tab");
      tabs.forEach(tab => {
        tab.addEventListener("click", function() {
          // Remove active class from all tabs
          tabs.forEach(t => t.classList.remove("active"));

          // Add active class to clicked tab
          this.classList.add("active");

          // Hide all panels
          const panels = document.querySelectorAll(".travel-panel");
          panels.forEach(panel => panel.classList.remove("active"));

          // Show the corresponding panel
          const tabId = this.getAttribute("data-tab");
          document.getElementById(tabId + "Panel").classList.add("active");
        });
      });

      // Fetch weather data (mock)
      fetchWeatherData();
    });

    // Weather API Integration (Mock)
    function fetchWeatherData() {
      // Coordinates for Kolli Hills
      const lat = 11.2480;
      const lon = 78.3300;

      // Mock weather data
      const mockWeatherData = {
        current: {
          temp: 24,
          humidity: 85,
          wind_speed: 12,
          weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
        },
        daily: [
          {
            dt: Date.now() + 86400000, // Tomorrow
            temp: { day: 25 },
            weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
          },
          {
            dt: Date.now() + 172800000, // Day after tomorrow
            temp: { day: 23 },
            weather: [{ main: "Rain", description: "Light Rain", icon: "10d" }]
          },
          {
            dt: Date.now() + 259200000, // 3 days from now
            temp: { day: 26 },
            weather: [{ main: "Clear", description: "Sunny", icon: "01d" }]
          }
        ]
      };

      updateWeatherUI(mockWeatherData);
    }

    function updateWeatherUI(data) {
      // Update current weather
      document.querySelector(".weather-temp").textContent = `${data.current.temp}°C`;
      document.querySelector(".weather-desc").textContent = data.current.weather[0].description;
      document.querySelector(".weather-extra").textContent = `Humidity: ${data.current.humidity}% | Wind: ${data.current.wind_speed} km/h`;

      // Update weather icon
      const weatherIconElement = document.querySelector(".weather-icon i");
      updateWeatherIcon(weatherIconElement, data.current.weather[0].main);

      // Update forecast
      const forecastDays = document.querySelectorAll(".forecast-day");
      data.daily.forEach((day, index) => {
        if (index < forecastDays.length) {
          const dayElement = forecastDays[index];
          const date = new Date(day.dt);
          const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });

          dayElement.querySelector("h4").textContent = index === 0 ? "Tomorrow" : dayName;
          dayElement.querySelector(".forecast-temp").textContent = `${day.temp.day}°C`;
          dayElement.querySelector(".forecast-desc").textContent = day.weather[0].description;

          // Update forecast icon
          const iconElement = dayElement.querySelector(".forecast-icon i");
          updateWeatherIcon(iconElement, day.weather[0].main);
        }
      });
    }

    function updateWeatherIcon(iconElement, weatherType) {
      // Remove all existing weather classes
      iconElement.className = "";

      // Add the appropriate weather icon class
      switch (weatherType) {
        case "Clear":
          iconElement.classList.add("fas", "fa-sun");
          break;
        case "Clouds":
          iconElement.classList.add("fas", "fa-cloud-sun");
          break;
        case "Rain":
        case "Drizzle":
          iconElement.classList.add("fas", "fa-cloud-rain");
          break;
        case "Thunderstorm":
          iconElement.classList.add("fas", "fa-bolt");
          break;
        case "Snow":
          iconElement.classList.add("fas", "fa-snowflake");
          break;
        case "Mist":
        case "Fog":
        case "Haze":
          iconElement.classList.add("fas", "fa-smog");
          break;
        default:
          iconElement.classList.add("fas", "fa-cloud-sun");
      }
    }
  </script>

  <!-- Language Toggle Script -->
  <script src="language-toggle.js"></script>
</body>
</html>
