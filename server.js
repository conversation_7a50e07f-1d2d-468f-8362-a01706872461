#!/usr/bin/env node

/**
 * Heritage Explorer - Express Development Server
 * Enhanced development server with better PHP mocking
 */

const express = require('express');
const path = require('path');
const cors = require('cors');
const bodyParser = require('body-parser');

const app = express();
const PORT = process.env.PORT || 3000;
const HOST = process.env.HOST || 'localhost';

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Serve static files
app.use(express.static(__dirname));

// Mock PHP endpoints
app.get('/auth.php', (req, res) => {
    const { action, username, email } = req.query;
    
    switch (action) {
        case 'check_session':
            res.json({
                success: false,
                logged_in: false,
                message: 'Development mode - PHP not available'
            });
            break;
            
        case 'check_username':
            res.json({
                available: true,
                message: `Username "${username}" is available (dev mode)`
            });
            break;
            
        case 'check_email':
            res.json({
                available: true,
                message: `Email "${email}" is available (dev mode)`
            });
            break;
            
        default:
            res.json({
                success: false,
                message: 'Unknown action'
            });
    }
});

app.post('/auth.php', (req, res) => {
    const { action } = req.body;
    
    switch (action) {
        case 'login':
            res.json({
                success: false,
                message: 'Development mode - Use XAMPP for authentication'
            });
            break;
            
        case 'signup':
            res.json({
                success: false,
                message: 'Development mode - Use XAMPP for user registration'
            });
            break;
            
        case 'logout':
            res.json({
                success: true,
                message: 'Logged out (dev mode)'
            });
            break;
            
        default:
            res.json({
                success: false,
                message: 'Unknown action'
            });
    }
});

// Mock other PHP files
app.get('*.php', (req, res) => {
    const phpFile = path.basename(req.path);
    res.send(`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Development Mode - ${phpFile}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            max-width: 600px;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            text-align: center;
        }
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border: 2px solid #ffc107;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            display: inline-block;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
        code {
            background: rgba(0, 0, 0, 0.3);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚧 Development Mode</h1>
        <div class="warning">
            <h3>PHP File Requested: ${phpFile}</h3>
            <p>This is a development server running on Node.js.<br>
            PHP files cannot be executed in this environment.</p>
        </div>
        
        <h3>🔧 For Full Functionality:</h3>
        <ol style="text-align: left; max-width: 400px; margin: 0 auto;">
            <li>Install XAMPP from <a href="https://www.apachefriends.org/" target="_blank" style="color: #4fc3f7;">apachefriends.org</a></li>
            <li>Copy files to <code>C:\\xampp\\htdocs\\heritage-explorer\\</code></li>
            <li>Start Apache and MySQL services</li>
            <li>Access via <code>http://localhost/heritage-explorer/</code></li>
        </ol>
        
        <div style="margin-top: 30px;">
            <a href="/website.html" class="btn success">🏛️ Main Website</a>
            <a href="/project-status.html" class="btn">📊 Project Status</a>
            <a href="/" class="btn">🏠 Home</a>
        </div>
        
        <div style="margin-top: 20px; font-size: 0.9em; opacity: 0.8;">
            <p>💡 Tip: Use <code>pnpm xampp</code> to run the XAMPP setup script</p>
        </div>
    </div>
</body>
</html>
    `);
});

// Default route
app.get('/', (req, res) => {
    // Check if website.html exists, otherwise serve index.html
    const fs = require('fs');
    if (fs.existsSync(path.join(__dirname, 'website.html'))) {
        res.redirect('/website.html');
    } else if (fs.existsSync(path.join(__dirname, 'index.html'))) {
        res.redirect('/index.html');
    } else {
        res.send(`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Heritage Explorer - Development Server</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            max-width: 700px;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            text-align: center;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            display: inline-block;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        .btn.success { background: #28a745; }
        .btn.warning { background: #ffc107; color: #000; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏛️ Heritage Explorer</h1>
        <h2>Development Server Running</h2>
        <p>Your development server is running on port ${PORT}</p>
        
        <div style="margin: 30px 0;">
            <a href="/website.html" class="btn success">🏛️ Main Website</a>
            <a href="/login.html" class="btn">🔐 Login</a>
            <a href="/signup.html" class="btn">📝 Signup</a>
            <a href="/project-status.html" class="btn">📊 Status</a>
        </div>
        
        <div style="background: rgba(255, 193, 7, 0.2); padding: 20px; border-radius: 10px; margin: 20px 0;">
            <h3>⚠️ Development Mode</h3>
            <p>Authentication and database features are mocked.<br>
            For full functionality, use XAMPP with Apache and MySQL.</p>
        </div>
        
        <p style="font-size: 0.9em; opacity: 0.8;">
            💡 Run <code>pnpm xampp</code> to set up XAMPP automatically
        </p>
    </div>
</body>
</html>
        `);
    }
});

// Error handling
app.use((err, req, res, next) => {
    console.error('Error:', err);
    res.status(500).json({
        error: 'Internal Server Error',
        message: err.message
    });
});

// 404 handler
app.use((req, res) => {
    res.status(404).send(`
<!DOCTYPE html>
<html>
<head>
    <title>404 - Not Found</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 40px; text-align: center; background: #f0f0f0; }
        .container { max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>404 - File Not Found</h1>
        <p>The requested file <code>${req.path}</code> was not found.</p>
        <a href="/" class="btn">← Back to Home</a>
    </div>
</body>
</html>
    `);
});

// Start server
app.listen(PORT, HOST, () => {
    console.log('\n==========================================');
    console.log('   🏛️ Heritage Explorer - Dev Server');
    console.log('==========================================\n');
    console.log(`🚀 Server running at: http://${HOST}:${PORT}/`);
    console.log(`📁 Serving files from: ${__dirname}`);
    console.log('\n📋 Available URLs:');
    console.log(`   🏠 Home:       http://${HOST}:${PORT}/`);
    console.log(`   🏛️ Main Site:  http://${HOST}:${PORT}/website.html`);
    console.log(`   🔐 Login:      http://${HOST}:${PORT}/login.html`);
    console.log(`   📝 Signup:     http://${HOST}:${PORT}/signup.html`);
    console.log(`   📊 Status:     http://${HOST}:${PORT}/project-status.html`);
    console.log('\n⚠️  Note: This is a development server.');
    console.log('   PHP features (authentication, database) are mocked.');
    console.log('   For full functionality, use: pnpm xampp\n');
    console.log('Press Ctrl+C to stop the server');
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n\n🛑 Server shutting down...');
    process.exit(0);
});

module.exports = app;
