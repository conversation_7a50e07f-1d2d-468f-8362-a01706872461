@echo off
color 0A
echo ========================================
echo Heritage Explorer - XAMPP Setup Helper
echo ========================================
echo.

:: Check if XAMPP is installed
if exist "C:\xampp\xampp-control.exe" (
    echo [OK] XAMPP found at C:\xampp\
) else (
    echo [ERROR] XAMPP not found!
    echo.
    echo XAMPP is required for this project to work.
    echo.
    echo Please follow these steps:
    echo 1. Download XAMPP from: https://www.apachefriends.org/
    echo 2. Install it to C:\xampp\ (default location)
    echo 3. Run this script again
    echo.
    echo Opening download page...
    start https://www.apachefriends.org/download.html
    pause
    exit /b 1
)

:: Create heritage-explorer directory
echo.
echo Creating project directory...
if not exist "C:\xampp\htdocs\heritage-explorer" (
    mkdir "C:\xampp\htdocs\heritage-explorer"
    echo [OK] Created C:\xampp\htdocs\heritage-explorer\
) else (
    echo [OK] Directory already exists
)

:: Start XAMPP Control Panel
echo.
echo Starting XAMPP Control Panel...
start "" "C:\xampp\xampp-control.exe"

echo.
echo ========================================
echo IMPORTANT: Follow these steps exactly!
echo ========================================
echo.
echo 1. In XAMPP Control Panel:
echo    - Click START for Apache (must show green "Running")
echo    - Click START for MySQL (must show green "Running")
echo.
echo 2. Copy ALL your project files to:
echo    C:\xampp\htdocs\heritage-explorer\
echo.
echo 3. Open browser and go to:
echo    http://localhost/heritage-explorer/setup-database.php
echo.
echo 4. After database setup, go to:
echo    http://localhost/heritage-explorer/website.html
echo.
echo 5. DO NOT use 127.0.0.1:5500 anymore!
echo    Always use http://localhost/heritage-explorer/
echo.

echo Press any key to open the project folder for copying files...
pause > nul

:: Open the project folder
explorer "C:\xampp\htdocs\heritage-explorer"

echo.
echo ========================================
echo COPY FILES NOW!
echo ========================================
echo.
echo The project folder is now open.
echo Copy ALL your project files there, then:
echo.
echo 1. Make sure XAMPP Apache and MySQL are running
echo 2. Go to: http://localhost/heritage-explorer/setup-database.php
echo 3. Then: http://localhost/heritage-explorer/website.html
echo.
echo Your website will work perfectly after this!
echo.
pause
