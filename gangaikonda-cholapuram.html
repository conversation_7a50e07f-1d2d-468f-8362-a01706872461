<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gangaikonda Cholapuram - Heritage Explorer</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
  <style>
    :root {
      --primary-color: #1a237e;
      --secondary-color: #457b9d;
      --accent-color: #e63946;
      --light-color: #f1faee;
      --dark-color: #1d3557;
      --success-color: #2e7d32;
      --warning-color: #ff9800;
      --danger-color: #c62828;
      --gray-100: #f8f9fa;
      --gray-200: #e9ecef;
      --gray-300: #dee2e6;
      --gray-400: #ced4da;
      --gray-500: #adb5bd;
      --gray-600: #6c757d;
      --gray-700: #495057;
      --gray-800: #343a40;
      --gray-900: #212529;
      --border-radius: 0.5rem;
      --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
      --transition: all 0.3s ease-in-out;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: var(--gray-800);
      background-color: var(--gray-100);
    }

    header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      text-align: center;
    }

    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      background-color: white;
      box-shadow: var(--box-shadow);
    }

    nav a {
      color: var(--gray-700);
      text-decoration: none;
      margin: 0 1rem;
      font-weight: 500;
      transition: var(--transition);
    }

    nav a:hover {
      color: var(--primary-color);
    }

    main {
      max-width: 1200px;
      margin: 2rem auto;
      padding: 0 1rem;
    }

    .site-details {
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    @media (min-width: 768px) {
      .site-details {
        grid-template-columns: 1fr 1fr;
      }
    }

    .site-image {
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
    }

    .site-image img {
      width: 100%;
      height: auto;
      display: block;
    }

    .site-info h1 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .site-info p {
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }

    .site-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .meta-item {
      display: flex;
      align-items: center;
      background-color: var(--gray-200);
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
    }

    .meta-item i {
      margin-right: 0.5rem;
      color: var(--secondary-color);
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      margin-top: 1.5rem;
    }

    .action-buttons button {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      background-color: var(--secondary-color);
      color: white;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
      display: flex;
      align-items: center;
    }

    .action-buttons button i {
      margin-right: 0.5rem;
    }

    .action-buttons button:hover {
      background-color: var(--primary-color);
    }

    .site-description {
      margin-bottom: 2rem;
    }

    .site-description h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .map-container {
      height: 400px;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      margin-bottom: 2rem;
    }

    .nearby-attractions {
      margin-bottom: 2rem;
    }

    .nearby-attractions h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .attractions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .attraction-card {
      background-color: white;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
    }

    .attraction-card:hover {
      transform: translateY(-5px);
    }

    .attraction-card img {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }

    .attraction-card-content {
      padding: 1rem;
    }

    .attraction-card-content h3 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    footer {
      background-color: var(--primary-color);
      color: white;
      text-align: center;
      padding: 1rem;
      margin-top: 2rem;
    }

    #backToTop {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      width: 3rem;
      height: 3rem;
      background-color: var(--primary-color);
      color: white;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
      display: none;
    }

    #backToTop:hover {
      background-color: var(--secondary-color);
    }

    /* Travel Information Section */
    .travel-info {
      margin-bottom: 2rem;
      background-color: white;
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      overflow: hidden;
    }

    .travel-info-header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .travel-info-header h2 {
      margin: 0;
      font-size: 1.5rem;
    }

    .travel-info-tabs {
      display: flex;
      background-color: var(--gray-200);
    }

    .travel-tab {
      padding: 1rem;
      cursor: pointer;
      flex: 1;
      text-align: center;
      font-weight: 500;
      transition: var(--transition);
      border-bottom: 3px solid transparent;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .travel-tab i {
      margin-right: 0.5rem;
    }

    .travel-tab.active {
      background-color: white;
      border-bottom-color: var(--accent-color);
    }

    .travel-tab:hover:not(.active) {
      background-color: var(--gray-300);
    }

    .travel-content {
      padding: 1.5rem;
    }

    .travel-panel {
      display: none;
    }

    .travel-panel.active {
      display: block;
    }

    /* Weather Panel */
    .weather-info {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      align-items: center;
    }

    .weather-current {
      display: flex;
      align-items: center;
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      flex: 1;
      min-width: 250px;
    }

    .weather-icon {
      font-size: 3rem;
      margin-right: 1rem;
      color: var(--secondary-color);
    }

    .weather-details h3 {
      margin: 0;
      color: var(--primary-color);
    }

    .weather-temp {
      font-size: 2rem;
      font-weight: bold;
    }

    .weather-desc {
      color: var(--gray-700);
    }

    .weather-forecast {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      flex: 2;
      min-width: 300px;
    }

    .forecast-day {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      text-align: center;
      flex: 1;
      min-width: 100px;
    }

    .forecast-day h4 {
      margin: 0 0 0.5rem 0;
      color: var(--primary-color);
    }

    .forecast-icon {
      font-size: 1.5rem;
      margin: 0.5rem 0;
      color: var(--secondary-color);
    }

    /* Transportation Panel */
    .transport-options {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .transport-option {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
      display: flex;
      flex-direction: column;
    }

    .transport-option h3 {
      display: flex;
      align-items: center;
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .transport-option h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .transport-option p {
      margin-bottom: 0.5rem;
    }

    .transport-option .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    /* Hours & Tickets Panel */
    .hours-container {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .opening-hours, .ticket-info {
      flex: 1;
      min-width: 250px;
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .opening-hours h3, .ticket-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .opening-hours h3 i, .ticket-info h3 i {
      margin-right: 0.5rem;
    }

    .hours-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .hours-list li {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .hours-list li:last-child {
      border-bottom: none;
    }

    .day {
      font-weight: 500;
    }

    .ticket-type {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .ticket-type:last-child {
      border-bottom: none;
    }

    .ticket-price {
      font-weight: bold;
    }

    /* Amenities Panel */
    .amenities-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .amenity-card {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .amenity-card h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .amenity-card h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .amenity-card p {
      margin-bottom: 0.5rem;
    }

    .amenity-card .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    .amenity-card .rating {
      color: var(--warning-color);
      margin-bottom: 0.5rem;
    }

    .weather-tips h3, .local-transport h3, .best-time h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .temple-features {
      margin-top: 1.5rem;
    }

    .temple-features h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .feature-item {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      margin-bottom: 1rem;
    }

    .feature-item h4 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
    }

    .feature-item h4 i {
      margin-right: 0.5rem;
    }

    .feature-details {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 0.5rem;
    }

    .feature-detail {
      display: flex;
      align-items: center;
      font-size: 0.9rem;
      color: var(--gray-700);
    }

    .feature-detail i {
      margin-right: 0.25rem;
      font-size: 0.8rem;
    }

    .temple-rituals {
      margin-top: 1.5rem;
    }

    .temple-rituals h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .ritual-item {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      margin-bottom: 1rem;
    }

    .ritual-item h4 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    .ritual-time {
      font-weight: bold;
      color: var(--secondary-color);
      margin-bottom: 0.5rem;
    }
  </style>
</head>
<body>
  <header>
    <h1>Heritage Explorer</h1>
    <p>Discover Tamil Nadu's rich heritage and culture</p>
  </header>

  <nav>
    <div>
      <a href="website.html"><i class="fas fa-home"></i> Home</a>
      <a href="#"><i class="fas fa-map-marked-alt"></i> Explore</a>
      <a href="#"><i class="fas fa-info-circle"></i> About</a>
    </div>
    <div>
      <a href="#" id="toggleLang">தமிழ்</a>
    </div>
  </nav>

  <main>
    <div class="site-details">
      <div class="site-image">
        <img src="https://t3.ftcdn.net/jpg/00/77/23/82/360_F_77238230_lAgdTLmuaILeB9mBSxYmfEqtzaCkEyCq.jpg" alt="Gangaikonda Cholapuram">
      </div>
      <div class="site-info">
        <h1>Gangaikonda Cholapuram</h1>
        <div class="site-meta">
          <div class="meta-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>Jayankondam, Tamil Nadu</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-gopuram"></i>
            <span>Temple</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-calendar-alt"></i>
            <span>Built in 1025 CE</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-award"></i>
            <span>UNESCO World Heritage Site</span>
          </div>
        </div>
        <p>Gangaikonda Cholapuram is a magnificent temple and former capital city built by Rajendra Chola I in the 11th century. This UNESCO World Heritage Site features exquisite Dravidian architecture, intricate stone carvings, and a massive Nandi statue, showcasing the artistic brilliance of the Chola dynasty.</p>
        <div class="action-buttons">
          <button id="playAudioButton"><i class="fas fa-volume-up"></i> Listen</button>
          <button id="favoriteButton"><i class="far fa-heart"></i> Favorite</button>
          <button id="shareButton"><i class="fas fa-share-alt"></i> Share</button>
        </div>
      </div>
    </div>

    <div class="site-description">
      <h2>About Gangaikonda Cholapuram</h2>
      <p>Gangaikonda Cholapuram is a historic temple and the former capital city of the Chola dynasty, located in the Ariyalur district of Tamil Nadu. It was established by Emperor Rajendra Chola I (1012-1044 CE), the son of the great Chola king Raja Raja Chola I, to commemorate his victorious expedition to the Ganges River. The name "Gangaikonda Cholapuram" translates to "The city of the Chola who brought the Ganges," reflecting Rajendra Chola's military achievements.</p>
      <p>The centerpiece of Gangaikonda Cholapuram is its magnificent temple dedicated to Lord Shiva, known as Brihadisvara Temple or Gangaikondacholisvaram. Built around 1025 CE, it is one of the three "Great Living Chola Temples" designated as UNESCO World Heritage Sites, along with the Brihadisvara Temple at Thanjavur and the Airavatesvara Temple at Darasuram. The temple stands as a testament to the architectural prowess and artistic vision of the Chola dynasty.</p>
      <p>The temple's architecture follows the Dravidian style, with a 55-meter high vimana (tower) that is slightly smaller than the one at Thanjavur but more refined in its execution. The temple is renowned for its elegant sculptures and intricate stone carvings that adorn its walls, depicting various deities, mythological scenes, and dance poses. The main sanctum houses a large Lingam (symbolic representation of Lord Shiva), and the temple complex includes several smaller shrines dedicated to various deities.</p>
      <p>Beyond its religious significance, Gangaikonda Cholapuram holds immense historical importance as it was the capital of the Chola Empire for about 250 years. The city was planned with great care, featuring a royal palace, various administrative buildings, and a massive artificial lake called Ponneri. Today, while much of the ancient city lies in ruins, the temple stands as a magnificent reminder of the Chola dynasty's golden age, attracting historians, archaeologists, and tourists who come to admire its architectural splendor and historical significance.</p>
    </div>

    <div class="map-container">
      <!-- Map will be embedded here -->
      <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3919.0123456789!2d79.45!3d11.208!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3a5526c7d4e245f7%3A0x9f0207b5541e7a4f!2sGangaikonda%20Cholapuram!5e0!3m2!1sen!2sin!4v1652345678901!5m2!1sen!2sin" width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy" title="Map of Gangaikonda Cholapuram"></iframe>
    </div>

    <!-- Travel Information Section -->
    <div class="travel-info">
      <div class="travel-info-header">
        <h2>Practical Travel Information</h2>
        <div class="last-updated">Last updated: <span id="lastUpdated">Today</span></div>
      </div>

      <div class="travel-info-tabs">
        <div class="travel-tab active" data-tab="weather">
          <i class="fas fa-cloud-sun"></i> Weather
        </div>
        <div class="travel-tab" data-tab="transport">
          <i class="fas fa-bus"></i> Transportation
        </div>
        <div class="travel-tab" data-tab="hours">
          <i class="far fa-clock"></i> Hours & Temple Info
        </div>
        <div class="travel-tab" data-tab="amenities">
          <i class="fas fa-concierge-bell"></i> Nearby Amenities
        </div>
      </div>

      <div class="travel-content">
        <!-- Weather Panel -->
        <div class="travel-panel active" id="weatherPanel">
          <div class="weather-info">
            <div class="weather-current">
              <div class="weather-icon">
                <i class="fas fa-sun"></i>
              </div>
              <div class="weather-details">
                <h3>Current Weather</h3>
                <div class="weather-temp">33°C</div>
                <div class="weather-desc">Sunny</div>
                <div class="weather-extra">Humidity: 70% | Wind: 10 km/h</div>
              </div>
            </div>

            <div class="weather-forecast">
              <div class="forecast-day">
                <h4>Tomorrow</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun"></i></div>
                <div class="forecast-temp">32°C</div>
                <div class="forecast-desc">Partly Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Wed</h4>
                <div class="forecast-icon"><i class="fas fa-cloud"></i></div>
                <div class="forecast-temp">31°C</div>
                <div class="forecast-desc">Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Thu</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun-rain"></i></div>
                <div class="forecast-temp">30°C</div>
                <div class="forecast-desc">Light Rain</div>
              </div>
            </div>
          </div>

          <div class="weather-tips">
            <h3>Weather Tips</h3>
            <p>Gangaikonda Cholapuram experiences a tropical climate with hot summers and moderate winters. The region has three main seasons: summer (March-June), monsoon (July-November), and winter (December-February).</p>
            <p>The best time to visit is from November to February when the weather is relatively cooler with temperatures ranging from 20°C to 30°C. This period offers comfortable conditions for exploring the temple complex and archaeological sites. Summer months (March-June) can be extremely hot with temperatures often exceeding 35°C, making midday visits uncomfortable. The northeast monsoon (October-December) brings rainfall to the region, which can occasionally disrupt outdoor sightseeing.</p>
            <p>Morning hours (6:00 AM - 10:00 AM) and late afternoon to evening hours (4:00 PM - 6:00 PM) are the best times to visit the temple, as the midday sun can be quite intense. The temple's stone carvings are best viewed in the morning light when the sun illuminates the eastern façade. Always carry water, wear light cotton clothing, and use sun protection when visiting. A hat or umbrella is recommended for sun protection. During winter months, a light jacket might be useful for early morning visits.</p>
          </div>
        </div>

        <!-- Transportation Panel -->
        <div class="travel-panel" id="transportPanel">
          <div class="transport-options">
            <div class="transport-option">
              <h3><i class="fas fa-plane"></i> By Air</h3>
              <p>Tiruchirappalli International Airport is the nearest airport.</p>
              <p class="distance">85 km from Gangaikonda Cholapuram (approx. 2 hours by car)</p>
              <p>Regular flights from Chennai, Bangalore, and other major cities.</p>
              <p>Pre-paid taxis available from the airport (₹2,000-2,500).</p>
              <p>App-based cabs may not be readily available for this route.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-train"></i> By Train</h3>
              <p>Ariyalur Railway Station is the nearest railway station.</p>
              <p class="distance">30 km from Gangaikonda Cholapuram (approx. 45 minutes by car)</p>
              <p>Connected to Chennai, Trichy, and other major cities.</p>
              <p>Auto-rickshaws and taxis available from the station (₹500-700).</p>
              <p>Advance booking recommended for train tickets.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-bus"></i> By Bus</h3>
              <p>Jayankondam is the nearest bus terminal.</p>
              <p class="distance">15 km from Gangaikonda Cholapuram (approx. 30 minutes by car)</p>
              <p>Regular state-run buses from Trichy, Thanjavur, and Kumbakonam.</p>
              <p>Fare: ₹50-150 depending on the bus type and distance.</p>
              <p>Frequency: Limited, check schedules in advance.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-car"></i> By Car</h3>
              <p>Well-connected by road from major cities in Tamil Nadu.</p>
              <p>From Chennai: NH32 (250 km, approx. 5-6 hours)</p>
              <p>From Thanjavur: NH81 (70 km, approx. 1.5 hours)</p>
              <p>From Kumbakonam: Via Jayankondam (45 km, approx. 1 hour)</p>
              <p>Parking available near the temple complex (free).</p>
            </div>
          </div>

          <div class="local-transport">
            <h3>Local Transportation</h3>
            <p>Gangaikonda Cholapuram is a small archaeological site, and local transportation options are limited. The temple complex itself is compact and can be explored on foot. If you're staying in nearby towns like Jayankondam or Kumbakonam, you'll need to arrange transportation to reach the temple site.</p>
            <p>Auto-rickshaws are available in Jayankondam and can be hired for a round trip to Gangaikonda Cholapuram (₹300-400). For more flexibility, consider hiring a taxi for the day from larger towns like Kumbakonam or Thanjavur (₹2,000-3,000 per day). This allows you to visit multiple heritage sites in the region, including the other Great Living Chola Temples at Thanjavur and Darasuram.</p>
            <p>There are no regular public buses that stop directly at the temple site, so private transportation is recommended. If you're traveling on a budget, you can take a bus to Jayankondam and then hire an auto-rickshaw to the temple. Some local tour operators in Kumbakonam and Thanjavur offer day tours that include Gangaikonda Cholapuram along with other nearby attractions.</p>
          </div>
        </div>

        <!-- Hours & Temple Info Panel -->
        <div class="travel-panel" id="hoursPanel">
          <div class="hours-container">
            <div class="opening-hours">
              <h3><i class="far fa-clock"></i> Opening Hours</h3>
              <ul class="hours-list">
                <li><span class="day">Daily:</span> <span class="time">6:00 AM - 12:30 PM, 4:00 PM - 8:30 PM</span></li>
                <li><span class="day">Special Puja Times:</span> <span class="time">6:30 AM, 12:00 PM, 5:30 PM, 8:00 PM</span></li>
                <li><span class="day">Festival Days:</span> <span class="time">Extended hours (check locally)</span></li>
              </ul>
              <p class="hours-note">The temple is an active place of worship, and certain areas may be closed during ritual ceremonies. It's advisable to check locally for any schedule changes or special events. Photography is allowed in most areas of the temple complex, but may be restricted in the main sanctum. Dress modestly when visiting the temple - shoulders and knees should be covered.</p>
            </div>

            <div class="ticket-info">
              <h3><i class="fas fa-ticket-alt"></i> Entry Information</h3>
              <div class="ticket-type">
                <span>Temple Entry:</span>
                <span class="ticket-price">Free</span>
              </div>
              <div class="ticket-type">
                <span>Archaeological Museum:</span>
                <span class="ticket-price">₹15 (Indians), ₹200 (Foreigners)</span>
              </div>
              <div class="ticket-type">
                <span>Still Camera Fee:</span>
                <span class="ticket-price">₹50</span>
              </div>
              <div class="ticket-type">
                <span>Video Camera Fee:</span>
                <span class="ticket-price">₹200</span>
              </div>
              <p class="ticket-note">Children under 15 years enter free. The temple is maintained by the Archaeological Survey of India (ASI). Guides are available at the site for approximately ₹300-500, though official ASI guides may not always be present. It's advisable to carry cash as credit card facilities are not available at the site.</p>
            </div>
          </div>

          <div class="temple-features">
            <h3>Key Temple Features</h3>
            <p>Gangaikonda Cholapuram Temple is renowned for its architectural excellence and historical significance. Here are some of the most notable features to look for during your visit:</p>

            <div class="feature-item">
              <h4><i class="fas fa-gopuram"></i> Main Vimana (Tower)</h4>
              <div class="feature-details">
                <span class="feature-detail"><i class="fas fa-ruler-vertical"></i> Height: 55 meters</span>
                <span class="feature-detail"><i class="fas fa-calendar-alt"></i> Built: 1025 CE</span>
              </div>
              <p>The temple's main tower (vimana) is slightly smaller than the one at Thanjavur but is considered more refined in its execution. The 9-tiered tower is adorned with intricate sculptures and follows the Dravidian architectural style. The vimana is topped with a massive stone capstone (stupi) weighing several tons, showcasing the engineering prowess of the Chola architects.</p>
            </div>

            <div class="feature-item">
              <h4><i class="fas fa-om"></i> Main Sanctum</h4>
              <div class="feature-details">
                <span class="feature-detail"><i class="fas fa-landmark"></i> Deity: Lord Shiva as Brihadeeswara</span>
              </div>
              <p>The main sanctum houses a large Lingam (symbolic representation of Lord Shiva), which is one of the largest in South India. The sanctum is designed so that sunlight falls directly on the Lingam during specific times of the year. The walls of the sanctum are adorned with exquisite sculptures of various forms of Shiva, including Nataraja (the cosmic dancer).</p>
            </div>

            <div class="feature-item">
              <h4><i class="fas fa-chess-queen"></i> Sculptures and Carvings</h4>
              <div class="feature-details">
                <span class="feature-detail"><i class="fas fa-star"></i> Highlight: Dwarapalas and Dancing Shiva</span>
              </div>
              <p>The temple is famous for its elegant sculptures and intricate stone carvings. Notable among these are the massive Dwarapalas (doorkeepers) at the entrance, the beautiful depictions of Shiva in various poses, and the detailed narrative panels depicting stories from Hindu mythology. The sculpture of Shiva as Chandrasekhar (with the moon on his head) is particularly renowned for its artistic excellence.</p>
            </div>
          </div>

          <div class="temple-rituals">
            <h3>Temple Rituals and Festivals</h3>
            <div class="ritual-item">
              <h4>Daily Rituals</h4>
              <p class="ritual-time">Morning: 6:30 AM, Noon: 12:00 PM, Evening: 5:30 PM, Night: 8:00 PM</p>
              <p>The temple follows traditional Hindu rituals with four main pujas (worship ceremonies) performed daily. These include abhishekam (ritual bathing of the deity), alankaram (decoration), neivedyam (food offering), and deepa aradhana (lamp worship). Visitors can witness these rituals, though some portions may be restricted to devotees only.</p>
            </div>
            <div class="ritual-item">
              <h4>Maha Shivaratri</h4>
              <p class="ritual-time">February/March (based on Hindu lunar calendar)</p>
              <p>The most important festival celebrated at the temple, dedicated to Lord Shiva. The celebration includes special abhishekams, continuous chanting of prayers, and an all-night vigil. Thousands of devotees visit the temple during this festival, and special arrangements are made to accommodate the crowds.</p>
            </div>
            <div class="ritual-item">
              <h4>Tamil New Year</h4>
              <p class="ritual-time">Mid-April</p>
              <p>A significant celebration with special pujas and cultural programs. The temple is decorated with flowers and colorful rangoli designs, and special prasadam (blessed food) is distributed to devotees.</p>
            </div>
          </div>

          <div class="best-time">
            <h3>Best Time to Visit</h3>
            <p>The ideal time to visit Gangaikonda Cholapuram is from November to February when the weather is pleasant with temperatures ranging from 20°C to 30°C. This period offers comfortable conditions for exploring the temple complex and archaeological sites. Early mornings (6:00 AM - 9:00 AM) provide the best light for photography and a more peaceful experience with fewer visitors.</p>
            <p>If you're interested in witnessing temple rituals, plan your visit to coincide with the morning or evening puja times (6:30 AM or 5:30 PM). For those interested in the architectural details, mid-morning light (around 9:00 AM - 10:00 AM) illuminates the eastern façade of the temple beautifully, highlighting the intricate carvings.</p>
            <p>The temple is less crowded on weekdays compared to weekends. If you're visiting during major Hindu festivals like Maha Shivaratri or Tamil New Year, expect larger crowds but also a more vibrant atmosphere with special decorations and ceremonies. During the monsoon season (October-December), check weather forecasts before planning your visit, as heavy rainfall can make exploration less comfortable.</p>
          </div>
        </div>

        <!-- Amenities Panel -->
        <div class="travel-panel" id="amenitiesPanel">
          <div class="amenities-grid">
            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Hotel Gnanam</strong></p>
              <p class="distance">In Thanjavur (70 km from temple)</p>
              <p class="rating">★★★★☆ (4.2/5)</p>
              <p>Mid-range hotel with comfortable rooms and restaurant.</p>
              <p>Price range: ₹2,500 - ₹4,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Ideal River View Resort</strong></p>
              <p class="distance">In Kumbakonam (45 km from temple)</p>
              <p class="rating">★★★★☆ (4.3/5)</p>
              <p>Riverside resort with traditional architecture and modern amenities.</p>
              <p>Price range: ₹3,500 - ₹6,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Jayankondam Lodges</strong></p>
              <p class="distance">In Jayankondam (15 km from temple)</p>
              <p class="rating">★★☆☆☆ (2.8/5)</p>
              <p>Basic accommodation with limited amenities.</p>
              <p>Price range: ₹800 - ₹1,500 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Saravana Bhavan</strong></p>
              <p class="distance">In Kumbakonam (45 km from temple)</p>
              <p class="rating">★★★★☆ (4.1/5)</p>
              <p>Popular vegetarian restaurant serving South Indian cuisine.</p>
              <p>Price range: ₹300 - ₹500 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Local Eateries</strong></p>
              <p class="distance">In Jayankondam (15 km from temple)</p>
              <p class="rating">★★★☆☆ (3.0/5)</p>
              <p>Small restaurants serving basic South Indian meals.</p>
              <p>Price range: ₹100 - ₹200 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-shopping-bag"></i> Shopping</h3>
              <p><strong>Temple Souvenir Shop</strong></p>
              <p class="distance">At the temple complex</p>
              <p>Small shop selling religious items, postcards, and books about the temple.</p>
              <p>Price range: ₹50 - ₹500</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-first-aid"></i> Medical Facilities</h3>
              <p><strong>Government Hospital</strong></p>
              <p class="distance">In Jayankondam (15 km from temple)</p>
              <p>Basic medical facilities for minor emergencies.</p>
              <p>For serious medical issues, hospitals in Trichy (85 km) are recommended.</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-info-circle"></i> Tourist Information</h3>
              <p><strong>Archaeological Survey of India Office</strong></p>
              <p class="distance">At the temple complex</p>
              <p>Provides basic information about the temple and its history.</p>
              <p>Limited English-speaking staff may be available.</p>
            </div>
          </div>

          <div class="best-time">
            <h3>Visitor Tips</h3>
            <p>There are very limited facilities at the temple site itself, so it's advisable to carry water, snacks, and other essentials with you. The nearest ATMs are in Jayankondam (15 km away), so ensure you have sufficient cash before visiting. Mobile network coverage can be spotty in the area, with Airtel and Jio providing relatively better connectivity.</p>
            <p>Consider hiring a knowledgeable guide to fully appreciate the historical and architectural significance of the temple. While some guides may be available at the site, it's better to arrange one in advance from Kumbakonam or Thanjavur. A typical guided tour takes about 1-2 hours.</p>
            <p>Gangaikonda Cholapuram is often visited as part of a larger circuit of the Great Living Chola Temples, which includes Brihadisvara Temple at Thanjavur and Airavatesvara Temple at Darasuram. If you're interested in temple architecture and history, consider planning a 2-3 day trip to visit all three UNESCO World Heritage Sites. The temples showcase the progression of Chola architecture and provide a comprehensive understanding of this remarkable dynasty's contributions to art and culture.</p>
          </div>
        </div>
      </div>
    </div>

    <div class="nearby-attractions">
      <h2>Nearby Attractions</h2>
      <div class="attractions-grid">
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Airavatesvara_Temple_20190212182003.jpg" alt="Airavatesvara Temple">
          <div class="attraction-card-content">
            <h3>Airavatesvara Temple</h3>
            <p>A UNESCO World Heritage Site in Darasuram, built by Raja Raja Chola II in the 12th century, known for its intricate stone carvings and unique chariot-shaped mandapam.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Nageswaran_Temple_20190212182004.jpg" alt="Nageswaran Temple">
          <div class="attraction-card-content">
            <h3>Nageswaran Temple</h3>
            <p>An ancient Shiva temple in Kumbakonam, featuring beautiful sculptures, a five-tiered gopuram, and a sacred tank known as Kasi Theertham.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Veeranam_Lake_20190212182005.jpg" alt="Veeranam Lake">
          <div class="attraction-card-content">
            <h3>Veeranam Lake</h3>
            <p>A massive man-made lake built by Rajendra Chola I, which serves as a major water source for Chennai and offers scenic views of the surrounding countryside.</p>
          </div>
        </div>
      </div>
    </div>
  </main>

  <footer>
    <p>&copy; 2025 Heritage Explorer | Explore Tamil Nadu</p>
  </footer>

  <div id="backToTop">
    <i class="fas fa-arrow-up"></i>
  </div>

  <script>
    // Back to Top Button
    const backToTop = document.getElementById("backToTop");
    window.addEventListener("scroll", () => {
      if (window.scrollY > 300) {
        backToTop.style.display = "flex";
      } else {
        backToTop.style.display = "none";
      }
    });
    backToTop.addEventListener("click", () => {
      window.scrollTo({ top: 0, behavior: "smooth" });
    });

    // Audio Playback
    document.getElementById("playAudioButton").addEventListener("click", () => {
      const description = document.querySelector(".site-info p").textContent;
      const utterance = new SpeechSynthesisUtterance(description);
      utterance.lang = "en-US";
      speechSynthesis.speak(utterance);
    });

    // Favorite Button
    document.getElementById("favoriteButton").addEventListener("click", function() {
      const icon = this.querySelector("i");
      if (icon.classList.contains("far")) {
        icon.classList.remove("far");
        icon.classList.add("fas");
        alert("Added to favorites!");
      } else {
        icon.classList.remove("fas");
        icon.classList.add("far");
        alert("Removed from favorites!");
      }
    });

    // Share Button
    document.getElementById("shareButton").addEventListener("click", () => {
      const shareData = {
        title: "Gangaikonda Cholapuram - Heritage Explorer",
        text: "Check out this magnificent UNESCO World Heritage temple in Tamil Nadu!",
        url: window.location.href
      };

      if (navigator.share) {
        navigator.share(shareData)
          .then(() => console.log("Shared successfully"))
          .catch((error) => console.error("Error sharing:", error));
      } else {
        navigator.clipboard.writeText(shareData.url)
          .then(() => alert("URL copied to clipboard"))
          .catch((err) => console.error("Could not copy text: ", err));
      }
    });

    // Initialize the travel info tabs
    document.addEventListener("DOMContentLoaded", function() {
      // Set the last updated date
      const today = new Date();
      document.getElementById("lastUpdated").textContent = today.toLocaleDateString();

      // Tab functionality
      const tabs = document.querySelectorAll(".travel-tab");
      tabs.forEach(tab => {
        tab.addEventListener("click", function() {
          // Remove active class from all tabs
          tabs.forEach(t => t.classList.remove("active"));

          // Add active class to clicked tab
          this.classList.add("active");

          // Hide all panels
          const panels = document.querySelectorAll(".travel-panel");
          panels.forEach(panel => panel.classList.remove("active"));

          // Show the corresponding panel
          const tabId = this.getAttribute("data-tab");
          document.getElementById(tabId + "Panel").classList.add("active");
        });
      });

      // Fetch weather data (mock)
      fetchWeatherData();
    });

    // Weather API Integration (Mock)
    function fetchWeatherData() {
      // Coordinates for Gangaikonda Cholapuram
      const lat = 11.208;
      const lon = 79.450;

      // Mock weather data
      const mockWeatherData = {
        current: {
          temp: 33,
          humidity: 70,
          wind_speed: 10,
          weather: [{ main: "Clear", description: "Sunny", icon: "01d" }]
        },
        daily: [
          {
            dt: Date.now() + 86400000, // Tomorrow
            temp: { day: 32 },
            weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
          },
          {
            dt: Date.now() + 172800000, // Day after tomorrow
            temp: { day: 31 },
            weather: [{ main: "Clouds", description: "Cloudy", icon: "03d" }]
          },
          {
            dt: Date.now() + 259200000, // 3 days from now
            temp: { day: 30 },
            weather: [{ main: "Rain", description: "Light Rain", icon: "10d" }]
          }
        ]
      };

      updateWeatherUI(mockWeatherData);
    }

    function updateWeatherUI(data) {
      // Update current weather
      document.querySelector(".weather-temp").textContent = `${data.current.temp}°C`;
      document.querySelector(".weather-desc").textContent = data.current.weather[0].description;
      document.querySelector(".weather-extra").textContent = `Humidity: ${data.current.humidity}% | Wind: ${data.current.wind_speed} km/h`;

      // Update weather icon
      const weatherIconElement = document.querySelector(".weather-icon i");
      updateWeatherIcon(weatherIconElement, data.current.weather[0].main);

      // Update forecast
      const forecastDays = document.querySelectorAll(".forecast-day");
      data.daily.forEach((day, index) => {
        if (index < forecastDays.length) {
          const dayElement = forecastDays[index];
          const date = new Date(day.dt);
          const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });

          dayElement.querySelector("h4").textContent = index === 0 ? "Tomorrow" : dayName;
          dayElement.querySelector(".forecast-temp").textContent = `${day.temp.day}°C`;
          dayElement.querySelector(".forecast-desc").textContent = day.weather[0].description;

          // Update forecast icon
          const iconElement = dayElement.querySelector(".forecast-icon i");
          updateWeatherIcon(iconElement, day.weather[0].main);
        }
      });
    }

    function updateWeatherIcon(iconElement, weatherType) {
      // Remove all existing weather classes
      iconElement.className = "";

      // Add the appropriate weather icon class
      switch (weatherType) {
        case "Clear":
          iconElement.classList.add("fas", "fa-sun");
          break;
        case "Clouds":
          iconElement.classList.add("fas", "fa-cloud");
          break;
        case "Rain":
        case "Drizzle":
          iconElement.classList.add("fas", "fa-cloud-rain");
          break;
        case "Thunderstorm":
          iconElement.classList.add("fas", "fa-bolt");
          break;
        case "Snow":
          iconElement.classList.add("fas", "fa-snowflake");
          break;
        case "Mist":
        case "Fog":
        case "Haze":
          iconElement.classList.add("fas", "fa-smog");
          break;
        default:
          iconElement.classList.add("fas", "fa-cloud-sun");
      }
    }
  </script>

  <!-- Language Toggle Script -->
  <script src="language-toggle.js"></script>
</body>
</html>
