const express = require('express');
const User = require('../models/User');
const auth = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/admin/users
// @desc    Get all users (for database viewer)
// @access  Public (for demo purposes - in production, add admin auth)
router.get('/users', async (req, res) => {
  try {
    const users = await User.find({})
      .select('-password') // Exclude password field
      .sort({ createdAt: -1 });

    const stats = {
      totalUsers: users.length,
      activeUsers: users.filter(user => user.isActive).length,
      totalFavorites: users.reduce((sum, user) => sum + (user.favorites?.length || 0), 0),
      totalVisits: users.reduce((sum, user) => sum + (user.visitedSites?.length || 0), 0)
    };

    res.json({
      success: true,
      users,
      stats
    });

  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/sites
// @desc    Get all heritage sites
// @access  Public (for demo purposes)
router.get('/sites', async (req, res) => {
  try {
    const db = req.app.locals.db || require('mongoose').connection.db;
    const sitesCollection = db.collection('heritage_sites');
    
    const sites = await sitesCollection.find({})
      .sort({ createdAt: -1 })
      .toArray();

    const stats = {
      totalSites: sites.length,
      categories: [...new Set(sites.map(site => site.category))],
      locations: [...new Set(sites.map(site => site.location))]
    };

    res.json({
      success: true,
      sites,
      stats
    });

  } catch (error) {
    console.error('Get sites error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/stats
// @desc    Get database statistics
// @access  Public (for demo purposes)
router.get('/stats', async (req, res) => {
  try {
    const db = require('mongoose').connection.db;
    
    // Get database stats
    const dbStats = await db.stats();
    
    // Get collection stats
    const userCount = await User.countDocuments();
    const sitesCollection = db.collection('heritage_sites');
    const siteCount = await sitesCollection.countDocuments();
    
    // Get user activity stats
    const users = await User.find({}).select('favorites visitedSites lastLogin createdAt');
    
    const totalFavorites = users.reduce((sum, user) => sum + (user.favorites?.length || 0), 0);
    const totalVisits = users.reduce((sum, user) => sum + (user.visitedSites?.length || 0), 0);
    
    const recentLogins = users.filter(user => {
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
      return user.lastLogin && user.lastLogin > oneWeekAgo;
    }).length;

    const newUsersThisWeek = users.filter(user => {
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
      return user.createdAt && user.createdAt > oneWeekAgo;
    }).length;

    res.json({
      success: true,
      stats: {
        database: {
          collections: dbStats.collections,
          dataSize: Math.round(dbStats.dataSize / 1024), // KB
          storageSize: Math.round(dbStats.storageSize / 1024), // KB
          indexes: dbStats.indexes
        },
        users: {
          total: userCount,
          newThisWeek: newUsersThisWeek,
          recentLogins: recentLogins,
          totalFavorites: totalFavorites,
          totalVisits: totalVisits
        },
        sites: {
          total: siteCount
        }
      }
    });

  } catch (error) {
    console.error('Get stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   DELETE /api/admin/users/:id
// @desc    Delete a user (admin only)
// @access  Private (admin)
router.delete('/users/:id', auth, async (req, res) => {
  try {
    // In a real app, you'd check if the current user is an admin
    const user = await User.findById(req.params.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    await User.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'User deleted successfully'
    });

  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   PUT /api/admin/users/:id/status
// @desc    Update user status (activate/deactivate)
// @access  Private (admin)
router.put('/users/:id/status', auth, async (req, res) => {
  try {
    const { isActive } = req.body;
    
    const user = await User.findByIdAndUpdate(
      req.params.id,
      { isActive },
      { new: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      message: `User ${isActive ? 'activated' : 'deactivated'} successfully`,
      user
    });

  } catch (error) {
    console.error('Update user status error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
