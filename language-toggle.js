// Language Toggle Functionality for Detail Pages
// This script handles the language toggle between English and Tamil

// Content translations for all pages
const translations = {
  // Common elements across all pages
  common: {
    en: {
      mainHeading: "Heritage Explorer",
      subHeading: "Discover Tamil Nadu's rich heritage and culture",
      footerText: "© 2025 Heritage Explorer | Explore Tamil Nadu",
      toggleLang: "தமிழ்",
      homeLink: "Home",
      exploreLink: "Explore",
      aboutLink: "About",
      listenButton: "Listen",
      favoriteButton: "Favorite",
      shareButton: "Share",
      aboutHeading: "About",
      nearbyHeading: "Nearby Attractions",
      addedToFavorites: "Added to favorites!",
      removedFromFavorites: "Removed from favorites!",
      copiedToClipboard: "URL copied to clipboard",
      sharedSuccessfully: "Shared successfully",

      // Travel Info Section
      travelInfoHeading: "Practical Travel Information",
      lastUpdated: "Last updated:",
      weatherTab: "Weather",
      transportTab: "Transportation",
      hoursTab: "Hours & Tickets",
      amenitiesTab: "Nearby Amenities",
      currentWeather: "Current Weather",
      weatherTips: "Weather Tips",
      byAir: "By Air",
      byTrain: "By Train",
      byBus: "By Bus",
      byCar: "By Car",
      localTransport: "Local Transportation",
      openingHours: "Opening Hours",
      ticketInfo: "Ticket Information",
      bestTimeToVisit: "Best Time to Visit",
      accommodation: "Accommodation",
      restaurants: "Restaurants",
      shopping: "Shopping",
      medicalFacilities: "Medical Facilities",
      cafes: "Cafes",
      touristInfo: "Tourist Information",
      showMap: "Show Map",
      showList: "Show List",
      kmAway: "km away"
    },
    ta: {
      mainHeading: "பாரம்பரிய கையேடு",
      subHeading: "தமிழகத்தின் பாரம்பரியத்தை மற்றும் பண்பாட்டை ஆராயுங்கள்",
      footerText: "© 2025 பாரம்பரிய கையேடு | தமிழகத்தை ஆராயுங்கள்",
      toggleLang: "English",
      homeLink: "முகப்பு",
      exploreLink: "ஆராய்க",
      aboutLink: "எங்களை பற்றி",
      listenButton: "கேட்க",
      favoriteButton: "பிடித்தவை",
      shareButton: "பகிர்",
      aboutHeading: "பற்றி",
      nearbyHeading: "அருகிலுள்ள சுற்றுலா தலங்கள்",
      addedToFavorites: "பிடித்தவையில் சேர்க்கப்பட்டது!",
      removedFromFavorites: "பிடித்தவையிலிருந்து நீக்கப்பட்டது!",
      copiedToClipboard: "URL கிளிப்போர்டில் நகலெடுக்கப்பட்டது",
      sharedSuccessfully: "வெற்றிகரமாக பகிரப்பட்டது",

      // Travel Info Section
      travelInfoHeading: "பயணத்திற்கான நடைமுறை தகவல்கள்",
      lastUpdated: "கடைசியாக புதுப்பிக்கப்பட்டது:",
      weatherTab: "வானிலை",
      transportTab: "போக்குவரத்து",
      hoursTab: "நேரங்கள் & டிக்கெட்டுகள்",
      amenitiesTab: "அருகிலுள்ள வசதிகள்",
      currentWeather: "தற்போதைய வானிலை",
      weatherTips: "வானிலை குறிப்புகள்",
      byAir: "விமானம் மூலம்",
      byTrain: "ரயில் மூலம்",
      byBus: "பேருந்து மூலம்",
      byCar: "கார் மூலம்",
      localTransport: "உள்ளூர் போக்குவரத்து",
      openingHours: "திறந்திருக்கும் நேரங்கள்",
      ticketInfo: "டிக்கெட் தகவல்",
      bestTimeToVisit: "வருகை தர சிறந்த நேரம்",
      accommodation: "தங்குமிடம்",
      restaurants: "உணவகங்கள்",
      shopping: "கடைகள்",
      medicalFacilities: "மருத்துவ வசதிகள்",
      cafes: "காபி கடைகள்",
      touristInfo: "சுற்றுலா தகவல்",
      showMap: "வரைபடத்தைக் காட்டு",
      showList: "பட்டியலைக் காட்டு",
      kmAway: "கி.மீ தொலைவில்"
    }
  },

  // Yelagiri Hills
  "yelagiri-hills": {
    en: {
      title: "Yelagiri Hills",
      location: "Vellore, Tamil Nadu",
      category: "Hill Station",
      elevation: "1,110 meters above sea level",
      description: "Yelagiri is a serene hill station in the Vellore district of Tamil Nadu, known for its orchards, rose gardens, and artificial lakes. It's a perfect destination for trekking, paragliding, and nature lovers seeking a peaceful retreat.",
      about: [
        "Yelagiri Hills is a small hill station located in Vellore district of Tamil Nadu. It is situated at an altitude of 1,110 meters above sea level and is spread across 30 square kilometers. The hill station is surrounded by orchards, rose gardens, and verdant valleys.",
        "Originally belonging to the Zamindar family of Yelagiri, the hill station was taken over by the government of India in 1950. The region is still developing and offers a peaceful retreat away from the hustle and bustle of city life.",
        "Yelagiri is known for its pleasant climate throughout the year, making it an ideal destination for weekend getaways. The temperature ranges from 15°C to 30°C during summer and can drop to as low as 5°C during winter.",
        "The hill station offers various activities for adventure enthusiasts, including trekking to Swamimalai, the highest point in Yelagiri, paragliding, and boating in the Punganoor Lake. The annual summer festival held in May-June attracts many tourists with its flower shows, dog shows, and cultural programs."
      ],
      attractions: [
        {
          name: "Swamimalai Peak",
          description: "The highest point in Yelagiri, offering panoramic views of the surrounding valleys and hills."
        },
        {
          name: "Punganoor Lake",
          description: "An artificial lake with boating facilities and a beautiful garden surrounding it."
        },
        {
          name: "Jalagamparai Waterfalls",
          description: "A scenic waterfall located about 14 km from Yelagiri, best visited during the monsoon season."
        }
      ],
      shareText: "Check out this beautiful hill station in Tamil Nadu!"
    },
    ta: {
      title: "ஏலகிரி மலைகள்",
      location: "வேலூர், தமிழ்நாடு",
      category: "மலை நிலையம்",
      elevation: "கடல் மட்டத்திலிருந்து 1,110 மீட்டர் உயரம்",
      description: "ஏலகிரி தமிழ்நாட்டின் வேலூர் மாவட்டத்தில் உள்ள ஒரு அமைதியான மலை நிலையமாகும், இது தோட்டங்கள், ரோஜா தோட்டங்கள் மற்றும் செயற்கை ஏரிகளுக்கு பெயர் பெற்றது. இது மலையேற்றம், பாராகிளைடிங் மற்றும் அமைதியான சுற்றுலாவை நாடும் இயற்கை ஆர்வலர்களுக்கு ஒரு சிறந்த இடமாகும்.",
      about: [
        "ஏலகிரி மலைகள் தமிழ்நாட்டின் வேலூர் மாவட்டத்தில் அமைந்துள்ள ஒரு சிறிய மலை நிலையமாகும். இது கடல் மட்டத்திலிருந்து 1,110 மீட்டர் உயரத்தில் அமைந்துள்ளது மற்றும் 30 சதுர கிலோமீட்டர் பரப்பளவில் பரவியுள்ளது. இந்த மலை நிலையம் தோட்டங்கள், ரோஜா தோட்டங்கள் மற்றும் பசுமையான பள்ளத்தாக்குகளால் சூழப்பட்டுள்ளது.",
        "முதலில் ஏலகிரி ஜமீன்தார் குடும்பத்திற்கு சொந்தமான இந்த மலை நிலையம், 1950 ஆம் ஆண்டில் இந்திய அரசாங்கத்தால் கைப்பற்றப்பட்டது. இந்த பகுதி இன்னும் வளர்ந்து வருகிறது மற்றும் நகர வாழ்க்கையின் பரபரப்பிலிருந்து ஒரு அமைதியான தங்குமிடத்தை வழங்குகிறது.",
        "ஏலகிரி ஆண்டு முழுவதும் இனிமையான காலநிலைக்கு பெயர் பெற்றது, இது வார இறுதி விடுமுறைக்கு ஒரு சிறந்த இடமாக உள்ளது. கோடை காலத்தில் வெப்பநிலை 15°C முதல் 30°C வரை இருக்கும் மற்றும் குளிர்காலத்தில் 5°C வரை குறையலாம்.",
        "இந்த மலை நிலையம் சாகச ஆர்வலர்களுக்கு பல்வேறு செயல்பாடுகளை வழங்குகிறது, அதில் ஏலகிரியின் உயர்ந்த புள்ளியான சுவாமிமலைக்கு மலையேற்றம், பாராகிளைடிங் மற்றும் புங்கனூர் ஏரியில் படகு சவாரி ஆகியவை அடங்கும். மே-ஜூன் மாதங்களில் நடைபெறும் வருடாந்திர கோடைகால விழா, பூ கண்காட்சிகள், நாய் கண்காட்சிகள் மற்றும் கலாச்சார நிகழ்ச்சிகளுடன் பல சுற்றுலாப் பயணிகளை ஈர்க்கிறது."
      ],
      attractions: [
        {
          name: "சுவாமிமலை சிகரம்",
          description: "ஏலகிரியின் உயர்ந்த புள்ளி, சுற்றியுள்ள பள்ளத்தாக்குகள் மற்றும் மலைகளின் பனோரமிக் காட்சிகளை வழங்குகிறது."
        },
        {
          name: "புங்கனூர் ஏரி",
          description: "படகு வசதிகள் மற்றும் அழகான தோட்டம் சூழ்ந்த ஒரு செயற்கை ஏரி."
        },
        {
          name: "ஜலகம்பாறை நீர்வீழ்ச்சி",
          description: "ஏலகிரியிலிருந்து சுமார் 14 கிமீ தொலைவில் அமைந்துள்ள ஒரு அழகான நீர்வீழ்ச்சி, மழைக்காலத்தில் சிறப்பாக பார்வையிடலாம்."
        }
      ],
      shareText: "தமிழ்நாட்டில் உள்ள இந்த அழகான மலை நிலையத்தைப் பாருங்கள்!"
    }
  },

  // Brihadeeswarar Temple
  "brihadeeswarar-temple": {
    en: {
      title: "Brihadeeswarar Temple",
      location: "Thanjavur, Tamil Nadu",
      category: "Temple",
      description: "Brihadeeswarar Temple, also known as Peruvudaiyar Kovil, is a magnificent UNESCO World Heritage Site built by Raja Raja Chola I in the 11th century. This architectural marvel features a 216-foot tall vimana, a massive Nandi statue carved from a single stone, and exquisite frescoes that showcase the pinnacle of Chola art and architecture.",
      about: [
        "Brihadeeswarar Temple, also known as Rajarajesvaram or Peruvudaiyar Kovil, is a magnificent Hindu temple dedicated to Lord Shiva located in Thanjavur, Tamil Nadu. Built by Raja Raja Chola I between 1003 and 1010 CE, the temple is a part of the UNESCO World Heritage Site known as the \"Great Living Chola Temples\" and stands as a testament to the architectural prowess and artistic vision of the Chola dynasty.",
        "The most striking feature of the temple is its vimana (tower), which rises to a height of 216 feet (66 meters), making it one of the tallest temple towers in the world. The vimana is topped by a massive stone cupola, estimated to weigh about 80 tons, which was placed at that height using a technique that continues to baffle engineers today. The temple is built entirely of granite, with an estimated 130,000 tons of the stone used in its construction. The fact that there are no granite quarries within a 50-mile radius of Thanjavur adds to the marvel of its construction.",
        "The temple complex is a treasure trove of art and architecture. The walls of the temple are adorned with exquisite sculptures and frescoes depicting various deities and scenes from Hindu mythology. The inner sanctum houses a massive Lingam (symbolic representation of Lord Shiva), and the circumambulatory path around it features numerous smaller shrines. The Nandi (Shiva's bull mount) pavilion in front of the temple contains a monolithic statue of Nandi, carved from a single stone and measuring 16 feet in length and 13 feet in height.",
        "Beyond its architectural significance, Brihadeeswarar Temple holds immense cultural and religious importance. It has been a center of religious, cultural, and artistic activities for over a millennium. The temple walls contain inscriptions that provide valuable insights into the administrative, social, and economic systems of the Chola period. Even today, the temple continues to be a living place of worship, with daily rituals and annual festivals attracting thousands of devotees and tourists from around the world."
      ],
      attractions: [
        {
          name: "Thanjavur Palace",
          description: "A royal palace complex built by the Nayak rulers and later renovated by the Marathas, housing the Saraswathi Mahal Library and Royal Museum."
        },
        {
          name: "Saraswathi Mahal Library",
          description: "One of the oldest libraries in Asia, housing over 30,000 rare manuscripts and books in various languages including Tamil, Sanskrit, and Marathi."
        },
        {
          name: "Thanjavur Art Gallery",
          description: "Houses an exquisite collection of bronze and stone sculptures from the Chola period, along with paintings and artifacts showcasing the region's rich artistic heritage."
        }
      ],
      shareText: "Check out this magnificent UNESCO World Heritage temple in Tamil Nadu!"
    },
    ta: {
      title: "பிரகதீஸ்வரர் கோவில்",
      location: "தஞ்சாவூர், தமிழ்நாடு",
      category: "கோவில்",
      description: "பிரகதீஸ்வரர் கோவில், பெருவுடையார் கோவில் என்றும் அழைக்கப்படுகிறது, 11 ஆம் நூற்றாண்டில் ராஜ ராஜ சோழன் I ஆல் கட்டப்பட்ட ஒரு அற்புதமான யுனெஸ்கோ உலக பாரம்பரிய தளமாகும். இந்த கட்டிடக்கலை அதிசயம் 216 அடி உயரமான விமானம், ஒரே கல்லில் செதுக்கப்பட்ட பிரம்மாண்டமான நந்தி சிலை, மற்றும் சோழர் கலை மற்றும் கட்டிடக்கலையின் உச்சத்தை காட்டும் அற்புதமான சுவர் ஓவியங்களைக் கொண்டுள்ளது.",
      about: [
        "பிரகதீஸ்வரர் கோவில், ராஜராஜேஸ்வரம் அல்லது பெருவுடையார் கோவில் என்றும் அழைக்கப்படுகிறது, தமிழ்நாட்டின் தஞ்சாவூரில் அமைந்துள்ள சிவபெருமானுக்கு அர்ப்பணிக்கப்பட்ட ஒரு அற்புதமான இந்து கோவிலாகும். ராஜ ராஜ சோழன் I ஆல் 1003 மற்றும் 1010 CE இடையே கட்டப்பட்ட இந்த கோவில், \"மகத்தான வாழும் சோழர் கோவில்கள்\" என்று அழைக்கப்படும் யுனெஸ்கோ உலக பாரம்பரிய தளத்தின் ஒரு பகுதியாகும் மற்றும் சோழர் வம்சத்தின் கட்டிடக்கலை திறமை மற்றும் கலை பார்வைக்கு சான்றாக நிற்கிறது.",
        "கோவிலின் மிகவும் குறிப்பிடத்தக்க அம்சம் அதன் விமானம் (கோபுரம்), இது 216 அடி (66 மீட்டர்) உயரத்திற்கு உயர்கிறது, இது உலகின் மிக உயரமான கோவில் கோபுரங்களில் ஒன்றாகும். விமானத்தின் மேல் ஒரு பிரம்மாண்டமான கல் குவிமாடம் உள்ளது, இது சுமார் 80 டன் எடை கொண்டது, இது இன்றைய பொறியாளர்களை திகைக்க வைக்கும் ஒரு நுட்பத்தைப் பயன்படுத்தி அந்த உயரத்தில் வைக்கப்பட்டது. கோவில் முழுவதும் கிரானைட்டால் கட்டப்பட்டுள்ளது, அதன் கட்டுமானத்தில் சுமார் 130,000 டன் கல் பயன்படுத்தப்பட்டுள்ளது. தஞ்சாவூரில் இருந்து 50 மைல் சுற்றளவில் கிரானைட் சுரங்கங்கள் இல்லை என்ற உண்மை அதன் கட்டுமானத்தின் அதிசயத்தை அதிகரிக்கிறது.",
        "கோவில் வளாகம் கலை மற்றும் கட்டிடக்கலையின் கருவூலமாகும். கோவிலின் சுவர்கள் பல்வேறு தெய்வங்கள் மற்றும் இந்து புராணக் காட்சிகளைச் சித்தரிக்கும் அற்புதமான சிற்பங்கள் மற்றும் சுவர் ஓவியங்களால் அலங்கரிக்கப்பட்டுள்ளன. உள் கருவறையில் ஒரு பிரம்மாண்டமான லிங்கம் (சிவபெருமானின் சின்னமான பிரதிநிதித்துவம்) உள்ளது, மற்றும் அதைச் சுற்றியுள்ள பிரதட்சண பாதையில் பல சிறிய கோவில்கள் உள்ளன. கோவிலின் முன்னால் உள்ள நந்தி (சிவனின் காளை வாகனம்) மண்டபத்தில் ஒரே கல்லில் செதுக்கப்பட்ட நந்தியின் ஒற்றைக்கல் சிலை உள்ளது, இது 16 அடி நீளம் மற்றும் 13 அடி உயரம் கொண்டது.",
        "அதன் கட்டிடக்கலை முக்கியத்துவத்திற்கு அப்பால், பிரகதீஸ்வரர் கோவில் மிகுந்த கலாச்சார மற்றும் மத முக்கியத்துவத்தைக் கொண்டுள்ளது. இது ஆயிரம் ஆண்டுகளுக்கும் மேலாக மத, கலாச்சார மற்றும் கலை நடவடிக்கைகளின் மையமாக இருந்து வருகிறது. கோவில் சுவர்களில் சோழர் காலத்தின் நிர்வாக, சமூக மற்றும் பொருளாதார அமைப்புகள் பற்றிய மதிப்புமிக்க நுண்ணறிவுகளை வழங்கும் கல்வெட்டுகள் உள்ளன. இன்றும் கூட, கோவில் வழிபாட்டுத் தலமாக இருந்து வருகிறது, தினசரி சடங்குகள் மற்றும் வருடாந்திர விழாக்கள் உலகம் முழுவதிலும் இருந்து ஆயிரக்கணக்கான பக்தர்கள் மற்றும் சுற்றுலாப் பயணிகளை ஈர்க்கின்றன."
      ],
      attractions: [
        {
          name: "தஞ்சாவூர் அரண்மனை",
          description: "நாயக்கர் ஆட்சியாளர்களால் கட்டப்பட்டு பின்னர் மராத்தியர்களால் புதுப்பிக்கப்பட்ட அரச அரண்மனை வளாகம், சரஸ்வதி மகால் நூலகம் மற்றும் அரச அருங்காட்சியகத்தைக் கொண்டுள்ளது."
        },
        {
          name: "சரஸ்வதி மகால் நூலகம்",
          description: "ஆசியாவின் பழமையான நூலகங்களில் ஒன்று, தமிழ், சமஸ்கிருதம் மற்றும் மராத்தி உள்ளிட்ட பல்வேறு மொழிகளில் 30,000க்கும் மேற்பட்ட அரிய கையெழுத்துப் பிரதிகள் மற்றும் புத்தகங்களைக் கொண்டுள்ளது."
        },
        {
          name: "தஞ்சாவூர் கலை அருங்காட்சியகம்",
          description: "சோழர் காலத்தின் வெண்கல மற்றும் கல் சிற்பங்களின் அற்புதமான சேகரிப்பு, பிராந்தியத்தின் செழுமையான கலை பாரம்பரியத்தைக் காட்டும் ஓவியங்கள் மற்றும் பொருட்களுடன் கூடியது."
        }
      ],
      shareText: "தமிழ்நாட்டில் உள்ள இந்த அற்புதமான யுனெஸ்கோ உலக பாரம்பரிய கோவிலைப் பாருங்கள்!"
    }
  },

  // Ramanathaswamy Temple
  "ramanathaswamy-temple": {
    en: {
      title: "Ramanathaswamy Temple",
      location: "Rameswaram, Tamil Nadu",
      category: "Temple",
      description: "The Ramanathaswamy Temple is one of the holiest Hindu temples dedicated to Lord Shiva, located on Rameswaram Island. It's renowned for its magnificent corridors, the longest in India, and 22 sacred water tanks believed to wash away sins.",
      about: [
        "The Ramanathaswamy Temple is a sacred Hindu temple dedicated to Lord Shiva, located on Rameswaram Island in Tamil Nadu. It is one of the twelve Jyotirlinga temples, where Shiva is worshipped as a Jyotirlinga or \"pillar of light.\" The temple is also significant as one of the Char Dham pilgrimage sites that Hindus aspire to visit at least once in their lifetime.",
        "According to Hindu mythology, the temple was built by Lord Rama, an avatar of Lord Vishnu, to worship Lord Shiva. It is believed that Rama worshipped Shiva here to cleanse himself of the sin of killing Ravana, who was a Brahmin. The main deity in the temple is in the form of a Lingam (a symbolic representation of Shiva) called Ramalingam, which was installed by Rama himself.",
        "The temple is renowned for its magnificent corridors, which are the longest in India. The third corridor measures 197 meters east to west and 133 meters north to south, with massive pillars adorned with intricate carvings. The temple has 22 theerthams (sacred water tanks/wells) within its complex, and it is believed that bathing in these wells cleanses sins and cures diseases. Pilgrims traditionally bathe in these wells before entering the main shrine.",
        "The temple's architecture is a splendid example of Dravidian style, with towering gopurams (gateway towers), expansive courtyards, and ornate pillared halls. The eastern gopuram, which is the tallest, rises to a height of 126 feet. The temple complex covers an area of 15 acres and has been expanded over the centuries by various rulers, including the Pandyas, Cholas, and Nayaks."
      ],
      attractions: [
        {
          name: "Dhanushkodi",
          description: "A ghost town at the southeastern tip of Pamban Island, where the Bay of Bengal meets the Indian Ocean, with ruins from a 1964 cyclone."
        },
        {
          name: "Pamban Bridge",
          description: "India's first sea bridge and once the longest sea bridge in India, connecting Rameswaram Island to mainland India."
        },
        {
          name: "Agni Theertham",
          description: "A sacred beach where pilgrims take a holy dip before entering the Ramanathaswamy Temple, believed to wash away sins."
        }
      ],
      shareText: "Check out this magnificent temple in Tamil Nadu!"
    },
    ta: {
      title: "இராமநாதசுவாமி கோவில்",
      location: "இராமேஸ்வரம், தமிழ்நாடு",
      category: "கோவில்",
      description: "இராமநாதசுவாமி கோவில் சிவபெருமானுக்கு அர்ப்பணிக்கப்பட்ட மிகவும் புனிதமான இந்து கோவில்களில் ஒன்றாகும், இது இராமேஸ்வரம் தீவில் அமைந்துள்ளது. இது இந்தியாவில் மிக நீளமான அதன் அற்புதமான நடைபாதைகள் மற்றும் பாவங்களை கழுவும் என்று நம்பப்படும் 22 புனித நீர் தொட்டிகளுக்கு பெயர் பெற்றது.",
      about: [
        "இராமநாதசுவாமி கோவில் தமிழ்நாட்டின் இராமேஸ்வரம் தீவில் அமைந்துள்ள சிவபெருமானுக்கு அர்ப்பணிக்கப்பட்ட ஒரு புனிதமான இந்து கோவிலாகும். இது பன்னிரண்டு ஜோதிர்லிங்க கோவில்களில் ஒன்றாகும், அங்கு சிவன் ஜோதிர்லிங்கமாக அல்லது \"ஒளி தூண்\" என வழிபடப்படுகிறார். இந்துக்கள் தங்கள் வாழ்நாளில் குறைந்தது ஒரு முறையாவது பார்க்க விரும்பும் சார் தாம் யாத்திரை தலங்களில் ஒன்றாகவும் இந்த கோவில் முக்கியத்துவம் பெறுகிறது.",
        "இந்து புராணங்களின்படி, இந்த கோவில் விஷ்ணுவின் அவதாரமான இராமரால் சிவபெருமானை வழிபட கட்டப்பட்டது. பிராமணரான இராவணனைக் கொன்ற பாவத்தை நீக்க இராமர் இங்கு சிவனை வழிபட்டதாக நம்பப்படுகிறது. கோவிலின் முக்கிய தெய்வம் இராமலிங்கம் என்று அழைக்கப்படும் லிங்கம் (சிவனின் சின்னமான பிரதிநிதித்துவம்) வடிவில் உள்ளது, இது இராமரால் நிறுவப்பட்டது.",
        "இந்த கோவில் அதன் அற்புதமான நடைபாதைகளுக்கு பெயர் பெற்றது, இவை இந்தியாவில் மிக நீளமானவை. மூன்றாவது நடைபாதை கிழக்கிலிருந்து மேற்கு வரை 197 மீட்டர் மற்றும் வடக்கிலிருந்து தெற்கு வரை 133 மீட்டர் அளவுடையது, சிக்கலான செதுக்கல்களால் அலங்கரிக்கப்பட்ட பிரம்மாண்டமான தூண்களைக் கொண்டுள்ளது. கோவில் வளாகத்தில் 22 தீர்த்தங்கள் (புனித நீர் தொட்டிகள்/கிணறுகள்) உள்ளன, மற்றும் இந்த கிணறுகளில் குளிப்பது பாவங்களை கழுவி நோய்களை குணப்படுத்தும் என்று நம்பப்படுகிறது. யாத்ரீகர்கள் பாரம்பரியமாக முக்கிய கோவிலுக்குள் நுழைவதற்கு முன் இந்த கிணறுகளில் குளிக்கிறார்கள்.",
        "கோவிலின் கட்டிடக்கலை திராவிட பாணியின் அற்புதமான எடுத்துக்காட்டாகும், உயர்ந்த கோபுரங்கள் (நுழைவாயில் கோபுரங்கள்), விரிவான முற்றங்கள், மற்றும் அலங்காரமான தூண் மண்டபங்களைக் கொண்டுள்ளது. மிக உயரமான கிழக்கு கோபுரம் 126 அடி உயரத்திற்கு உயர்கிறது. கோவில் வளாகம் 15 ஏக்கர் பரப்பளவில் பரவியுள்ளது மற்றும் பாண்டியர்கள், சோழர்கள் மற்றும் நாயக்கர்கள் உள்ளிட்ட பல்வேறு ஆட்சியாளர்களால் நூற்றாண்டுகளாக விரிவுபடுத்தப்பட்டுள்ளது."
      ],
      attractions: [
        {
          name: "தனுஷ்கோடி",
          description: "பாம்பன் தீவின் தென்கிழக்கு முனையில் உள்ள ஒரு வெறிச்சோடிய நகரம், அங்கு வங்காள விரிகுடா இந்தியப் பெருங்கடலை சந்திக்கிறது, 1964 புயலின் இடிபாடுகளுடன்."
        },
        {
          name: "பாம்பன் பாலம்",
          description: "இந்தியாவின் முதல் கடல் பாலம் மற்றும் ஒரு காலத்தில் இந்தியாவின் மிக நீளமான கடல் பாலம், இராமேஸ்வரம் தீவை இந்தியாவின் பிரதான நிலப்பரப்புடன் இணைக்கிறது."
        },
        {
          name: "அக்னி தீர்த்தம்",
          description: "இராமநாதசுவாமி கோவிலுக்குள் நுழைவதற்கு முன் யாத்ரீகர்கள் புனித நீராடல் செய்யும் ஒரு புனித கடற்கரை, பாவங்களை கழுவும் என்று நம்பப்படுகிறது."
        }
      ],
      shareText: "தமிழ்நாட்டில் உள்ள இந்த அற்புதமான கோவிலைப் பாருங்கள்!"
    }
  },

  // Meenakshi Amman Temple
  "meenakshi-amman-temple": {
    en: {
      title: "Meenakshi Amman Temple",
      location: "Madurai, Tamil Nadu",
      category: "Temple",
      description: "A historic Hindu temple dedicated to Goddess Meenakshi, known for its magnificent architecture, colorful sculptures, and towering gopurams that dominate Madurai's skyline.",
      about: [
        "The Meenakshi Amman Temple, also known as Meenakshi Sundareswarar Temple, is a historic Hindu temple located in the heart of Madurai, Tamil Nadu. It is dedicated to Goddess Meenakshi, an avatar of the Hindu goddess Parvati, and her consort, Lord Sundareswarar, a form of Lord Shiva. The temple forms the heart and lifeline of the 2,500-year-old city of Madurai and is a significant symbol of the Tamil people's art and culture.",
        "The temple complex is massive, covering an area of about 14 acres, and is enclosed by high walls with four gateway towers (gopurams) in each direction. The most striking feature of the temple is its 14 colorful gopurams, with the southern tower being the tallest at 170 feet. These gopurams are adorned with thousands of colorful stone figures of deities, mythical animals, and celestial beings, making them a spectacular sight.",
        "Inside the temple, the architecture is equally impressive with numerous pillared halls, shrines, and a sacred tank called the Golden Lotus Tank (Porthamarai Kulam). The Thousand Pillar Hall, built in 1569, is a marvel of engineering and architecture with its 985 intricately carved pillars. Each pillar produces a different musical note when tapped, showcasing the advanced understanding of acoustics during that period.",
        "The temple is not just a place of worship but also a center for Tamil culture and art. It hosts the annual 10-day Meenakshi Tirukalyanam festival, celebrating the divine marriage of Goddess Meenakshi and Lord Sundareswarar, which attracts over a million visitors. The temple's rich history, stunning architecture, and cultural significance make it one of the most important and visited religious sites in India."
      ],
      attractions: [
        {
          name: "Thirumalai Nayakkar Palace",
          description: "A 17th-century palace built by King Thirumalai Nayak, featuring a blend of Dravidian and Islamic architectural styles with massive pillars and ornate arches."
        },
        {
          name: "Gandhi Museum",
          description: "Housed in the historic Tamukkam Palace, this museum displays artifacts related to India's freedom struggle and Mahatma Gandhi's life."
        },
        {
          name: "Vandiyur Mariamman Teppakulam",
          description: "A massive temple tank with a mandapam (hall) in the center, famous for the float festival held during the Thai month (January-February)."
        }
      ],
      shareText: "Check out this magnificent ancient temple in Madurai, Tamil Nadu!"
    },
    ta: {
      title: "மீனாட்சி அம்மன் கோவில்",
      location: "மதுரை, தமிழ்நாடு",
      category: "கோவில்",
      description: "மீனாட்சி தேவிக்கு அர்ப்பணிக்கப்பட்ட ஒரு வரலாற்று சிறப்புமிக்க இந்து கோவில், அதன் அற்புதமான கட்டிடக்கலை, வண்ணமயமான சிற்பங்கள், மற்றும் மதுரையின் வானத்தை ஆக்கிரமிக்கும் உயர்ந்த கோபுரங்களுக்கு பெயர் பெற்றது.",
      about: [
        "மீனாட்சி அம்மன் கோவில், மீனாட்சி சுந்தரேஸ்வரர் கோவில் என்றும் அழைக்கப்படுகிறது, தமிழ்நாட்டின் மதுரை நகரின் மையத்தில் அமைந்துள்ள ஒரு வரலாற்று சிறப்புமிக்க இந்து கோவிலாகும். இது இந்து தேவி பார்வதியின் அவதாரமான மீனாட்சி தேவிக்கும், அவரது துணைவரான சுந்தரேஸ்வரர் (சிவபெருமானின் ஒரு வடிவம்) க்கும் அர்ப்பணிக்கப்பட்டுள்ளது. இந்த கோவில் 2,500 ஆண்டுகள் பழமையான மதுரை நகரத்தின் இதயமாகவும் உயிர்நாடியாகவும் விளங்குகிறது மற்றும் தமிழ் மக்களின் கலை மற்றும் கலாச்சாரத்தின் முக்கியமான சின்னமாகும்.",
        "கோவில் வளாகம் மிகப் பெரியது, சுமார் 14 ஏக்கர் பரப்பளவில் பரவியுள்ளது, மற்றும் ஒவ்வொரு திசையிலும் நான்கு நுழைவாயில் கோபுரங்களுடன் உயர்ந்த சுவர்களால் சூழப்பட்டுள்ளது. கோவிலின் மிகவும் குறிப்பிடத்தக்க அம்சம் அதன் 14 வண்ணமயமான கோபுரங்கள், தெற்கு கோபுரம் 170 அடி உயரத்துடன் மிக உயரமானது. இந்த கோபுரங்கள் தெய்வங்கள், புராண விலங்குகள், மற்றும் வானுலக உயிரினங்களின் ஆயிரக்கணக்கான வண்ணமயமான கல் உருவங்களால் அலங்கரிக்கப்பட்டுள்ளன, இவை ஒரு அற்புதமான காட்சியாக உள்ளன.",
        "கோவிலின் உள்ளே, கட்டிடக்கலை பல தூண் மண்டபங்கள், சிறிய கோவில்கள், மற்றும் பொற்றாமரை குளம் என்று அழைக்கப்படும் ஒரு புனித குளத்துடன் அதே அளவு அற்புதமாக உள்ளது. 1569 இல் கட்டப்பட்ட ஆயிரம் தூண் மண்டபம், அதன் 985 சிக்கலான செதுக்கப்பட்ட தூண்களுடன் பொறியியல் மற்றும் கட்டிடக்கலையின் ஒரு அற்புதமாகும். ஒவ்வொரு தூணும் தட்டும்போது ஒரு வித்தியாசமான இசைக் குறிப்பை உருவாக்குகிறது, அந்த காலத்தில் ஒலியியல் பற்றிய மேம்பட்ட புரிதலைக் காட்டுகிறது.",
        "கோவில் வெறும் வழிபாட்டுத் தலம் மட்டுமல்ல, தமிழ் கலாச்சாரம் மற்றும் கலைக்கான மையமாகவும் உள்ளது. இது வருடாந்திர 10 நாள் மீனாட்சி திருக்கல்யாணம் விழாவை நடத்துகிறது, மீனாட்சி தேவி மற்றும் சுந்தரேஸ்வரர் இடையேயான திருமணத்தைக் கொண்டாடுகிறது, இது ஒரு மில்லியனுக்கும் அதிகமான பார்வையாளர்களை ஈர்க்கிறது. கோவிலின் செழுமையான வரலாறு, அற்புதமான கட்டிடக்கலை, மற்றும் கலாச்சார முக்கியத்துவம் இதை இந்தியாவின் மிக முக்கியமான மற்றும் அதிகம் பார்வையிடப்படும் மத தலங்களில் ஒன்றாக ஆக்குகிறது."
      ],
      attractions: [
        {
          name: "திருமலை நாயக்கர் அரண்மனை",
          description: "திருமலை நாயக்கர் மன்னரால் கட்டப்பட்ட 17 ஆம் நூற்றாண்டு அரண்மனை, பிரம்மாண்டமான தூண்கள் மற்றும் அலங்காரமான வளைவுகளுடன் திராவிட மற்றும் இஸ்லாமிய கட்டிடக்கலை பாணிகளின் கலவையைக் கொண்டுள்ளது."
        },
        {
          name: "காந்தி அருங்காட்சியகம்",
          description: "வரலாற்று சிறப்புமிக்க தமுக்கம் அரண்மனையில் அமைந்துள்ள இந்த அருங்காட்சியகம், இந்தியாவின் சுதந்திரப் போராட்டம் மற்றும் மகாத்மா காந்தியின் வாழ்க்கை தொடர்பான பொருட்களைக் காட்சிப்படுத்துகிறது."
        },
        {
          name: "வண்டியூர் மாரியம்மன் தெப்பக்குளம்",
          description: "மையத்தில் ஒரு மண்டபத்துடன் கூடிய ஒரு பிரம்மாண்டமான கோவில் குளம், தை மாதத்தில் (ஜனவரி-பிப்ரவரி) நடைபெறும் தெப்பத் திருவிழாவிற்கு பெயர் பெற்றது."
        }
      ],
      shareText: "தமிழ்நாட்டின் மதுரையில் உள்ள இந்த அற்புதமான பழங்கால கோவிலைப் பாருங்கள்!"
    }
  },

  // Gangaikonda Cholapuram
  "gangaikonda-cholapuram": {
    en: {
      title: "Gangaikonda Cholapuram",
      location: "Ariyalur, Tamil Nadu",
      category: "Temple",
      description: "Gangaikonda Cholapuram is a magnificent temple built by Rajendra Chola I in the 11th century to commemorate his victory over the Ganges region. This UNESCO World Heritage Site features exquisite Chola architecture, intricate sculptures, and a massive Nandi statue.",
      about: [
        "Gangaikonda Cholapuram is a temple town built by Rajendra Chola I, the son and successor of the great Chola king Raja Raja Chola I, in the early 11th century. The name translates to \"The town of the Chola who took the Ganges,\" commemorating Rajendra Chola's successful northern military campaign to the Ganges River. It served as the capital of the Chola dynasty for about 250 years.",
        "The centerpiece of Gangaikonda Cholapuram is the magnificent Brihadeeswarar Temple, also known as Gangaikondacholisvaram Temple, dedicated to Lord Shiva. Built between 1020 and 1035 CE, it is part of the UNESCO World Heritage Site collectively known as the \"Great Living Chola Temples.\" The temple follows the architectural style of the Brihadeeswarar Temple at Thanjavur but has its own unique features and is slightly smaller in size.",
        "The temple's vimana (tower) rises to a height of about 185 feet and is adorned with intricate sculptures and carvings. The main sanctum houses a massive Lingam, and the walls are decorated with various forms of Lord Shiva and other deities. One of the most remarkable features is the sculpture of Ardhanarisvara (half male and half female form of Shiva), which is considered one of the finest examples of Chola art.",
        "The temple complex also includes a massive Nandi (bull) statue, various mandapams (halls), and subsidiary shrines. The engineering marvel of the temple is evident in its perfect proportions, the quality of its sculptures, and the fact that the shadow of the temple's vimana never falls on the ground. Today, while the palace and other structures of the ancient capital have largely disappeared, the temple stands as a testament to the architectural prowess and artistic vision of the Chola dynasty."
      ],
      attractions: [
        {
          name: "Airavatesvara Temple",
          description: "Another UNESCO World Heritage Site located in Darasuram, known for its exquisite stone carvings and unique chariot-shaped mandapam."
        },
        {
          name: "Kumbakonam Temples",
          description: "A cluster of ancient temples in the nearby town of Kumbakonam, including the Nageshwara Temple and Adi Kumbeswarar Temple."
        },
        {
          name: "Chidambaram Nataraja Temple",
          description: "A significant Shiva temple about 60 km away, famous for its representation of Shiva as the cosmic dancer Nataraja."
        }
      ],
      shareText: "Check out this magnificent UNESCO World Heritage temple in Tamil Nadu!"
    },
    ta: {
      title: "கங்கைகொண்ட சோழபுரம்",
      location: "அரியலூர், தமிழ்நாடு",
      category: "கோவில்",
      description: "கங்கைகொண்ட சோழபுரம் 11 ஆம் நூற்றாண்டில் ராஜேந்திர சோழன் I ஆல் கங்கைப் பகுதியில் அவரது வெற்றியை நினைவுகூரும் வகையில் கட்டப்பட்ட ஒரு அற்புதமான கோவிலாகும். இந்த யுனெஸ்கோ உலக பாரம்பரிய தளம் அற்புதமான சோழர் கட்டிடக்கலை, சிக்கலான சிற்பங்கள், மற்றும் ஒரு பிரம்மாண்டமான நந்தி சிலையைக் கொண்டுள்ளது.",
      about: [
        "கங்கைகொண்ட சோழபுரம் 11 ஆம் நூற்றாண்டின் தொடக்கத்தில் மாபெரும் சோழ மன்னர் ராஜ ராஜ சோழன் I இன் மகனும் வாரிசுமான ராஜேந்திர சோழன் I ஆல் கட்டப்பட்ட ஒரு கோவில் நகரமாகும். இந்த பெயர் \"கங்கையை எடுத்த சோழனின் நகரம்\" என்று மொழிபெயர்க்கப்படுகிறது, ராஜேந்திர சோழனின் கங்கை நதி வரையிலான வெற்றிகரமான வடக்கு இராணுவப் பிரச்சாரத்தை நினைவுகூருகிறது. இது சுமார் 250 ஆண்டுகளாக சோழ வம்சத்தின் தலைநகராக செயல்பட்டது.",
        "கங்கைகொண்ட சோழபுரத்தின் மையப்பகுதி சிவபெருமானுக்கு அர்ப்பணிக்கப்பட்ட அற்புதமான பிரகதீஸ்வரர் கோவில், கங்கைகொண்டசோழீஸ்வரம் கோவில் என்றும் அழைக்கப்படுகிறது. 1020 மற்றும் 1035 CE இடையே கட்டப்பட்ட இது \"மகத்தான வாழும் சோழர் கோவில்கள்\" என்று கூட்டாக அறியப்படும் யுனெஸ்கோ உலக பாரம்பரிய தளத்தின் ஒரு பகுதியாகும். கோவில் தஞ்சாவூரில் உள்ள பிரகதீஸ்வரர் கோவிலின் கட்டிடக்கலை பாணியைப் பின்பற்றுகிறது, ஆனால் அதற்கே உரிய தனித்துவமான அம்சங்களைக் கொண்டுள்ளது மற்றும் அளவில் சற்று சிறியது.",
        "கோவிலின் விமானம் (கோபுரம்) சுமார் 185 அடி உயரத்திற்கு உயர்கிறது மற்றும் சிக்கலான சிற்பங்கள் மற்றும் செதுக்கல்களால் அலங்கரிக்கப்பட்டுள்ளது. முக்கிய கருவறையில் ஒரு பிரம்மாண்டமான லிங்கம் உள்ளது, மற்றும் சுவர்கள் சிவபெருமானின் பல்வேறு வடிவங்கள் மற்றும் பிற தெய்வங்களால் அலங்கரிக்கப்பட்டுள்ளன. மிகவும் குறிப்பிடத்தக்க அம்சங்களில் ஒன்று அர்த்தநாரீஸ்வரர் (சிவனின் பாதி ஆண் மற்றும் பாதி பெண் வடிவம்) சிற்பம், இது சோழர் கலையின் சிறந்த எடுத்துக்காட்டுகளில் ஒன்றாகக் கருதப்படுகிறது.",
        "கோவில் வளாகத்தில் ஒரு பிரம்மாண்டமான நந்தி (காளை) சிலை, பல்வேறு மண்டபங்கள் (மண்டபங்கள்), மற்றும் துணை கோவில்களும் அடங்கும். கோவிலின் பொறியியல் அதிசயம் அதன் சரியான விகிதங்கள், அதன் சிற்பங்களின் தரம், மற்றும் கோவிலின் விமானத்தின் நிழல் ஒருபோதும் தரையில் விழாது என்ற உண்மையில் தெளிவாகத் தெரிகிறது. இன்று, பழைய தலைநகரின் அரண்மனை மற்றும் பிற கட்டமைப்புகள் பெரும்பாலும் மறைந்துவிட்ட போதிலும், கோவில் சோழ வம்சத்தின் கட்டிடக்கலை திறமை மற்றும் கலை பார்வைக்கு சான்றாக நிற்கிறது."
      ],
      attractions: [
        {
          name: "ஐராவதேஸ்வரர் கோவில்",
          description: "தாராசுரத்தில் அமைந்துள்ள மற்றொரு யுனெஸ்கோ உலக பாரம்பரிய தளம், அதன் அற்புதமான கல் செதுக்கல்கள் மற்றும் தனித்துவமான தேர் வடிவ மண்டபத்திற்கு பெயர் பெற்றது."
        },
        {
          name: "கும்பகோணம் கோவில்கள்",
          description: "அருகிலுள்ள கும்பகோணம் நகரத்தில் உள்ள பழங்கால கோவில்களின் தொகுப்பு, நாகேஸ்வரர் கோவில் மற்றும் ஆதி கும்பேஸ்வரர் கோவில் உள்ளிட்டவை."
        },
        {
          name: "சிதம்பரம் நடராஜர் கோவில்",
          description: "சுமார் 60 கிமீ தொலைவில் உள்ள ஒரு முக்கியமான சிவன் கோவில், சிவனை நடராஜா என்ற பிரபஞ்ச நடனமாடுபவராக சித்தரிப்பதற்கு பெயர் பெற்றது."
        }
      ],
      shareText: "தமிழ்நாட்டில் உள்ள இந்த அற்புதமான யுனெஸ்கோ உலக பாரம்பரிய கோவிலைப் பாருங்கள்!"
    }
  },

  // Shore Temple
  "shore-temple": {
    en: {
      title: "Shore Temple",
      location: "Mahabalipuram, Tamil Nadu",
      category: "Monument",
      built: "Built in 8th century CE",
      unesco: "UNESCO World Heritage Site",
      description: "The Shore Temple is an ancient granite temple complex built during the Pallava dynasty, standing majestically on the shores of the Bay of Bengal. This UNESCO World Heritage Site is one of the oldest structural stone temples of South India.",
      about: [
        "The Shore Temple, located in Mahabalipuram (also known as Mamallapuram), is one of the oldest structural stone temples in South India and was built during the reign of the Pallava King Narasimhavarman II, also known as Rajasimha, between 700-728 CE. It stands on the shores of the Bay of Bengal, facing the sea, which gives it its name.",
        "The temple is a complex of three shrines, with two dedicated to Lord Shiva and one to Lord Vishnu. The main shrine faces east so that the rising sun rays illuminate the main deity. The temple's structure is a fine example of Dravidian architecture, with its pyramidal tower (shikhara), and is built using blocks of granite that were cut and sculpted on site.",
        "What makes the Shore Temple particularly remarkable is its location and survival. Despite centuries of exposure to sea winds and occasional flooding, the temple has withstood the test of time. The complex is surrounded by a series of sculptures, including a magnificent monolithic rock carving of a lion, and a small shrine with a reclining Vishnu.",
        "The Shore Temple is part of the Group of Monuments at Mahabalipuram, which was declared a UNESCO World Heritage Site in 1984. It represents the final phase of Pallava art, transitioning from rock-cut architecture to structural building. The temple's walls are adorned with intricate carvings, including the famous panel of 'Descent of the Ganges' and various mythological narratives."
      ],
      attractions: [
        {
          name: "Pancha Rathas",
          description: "Five monolithic rock-cut temples in the shape of chariots, each dedicated to a different deity, showcasing the evolution of Dravidian architecture."
        },
        {
          name: "Arjuna's Penance",
          description: "A massive open-air bas-relief monolith depicting scenes from Hindu mythology, including the story of Arjuna's penance to obtain Lord Shiva's weapon."
        },
        {
          name: "Krishna's Butter Ball",
          description: "A giant natural rock perched on a hillside, seemingly defying gravity, which has remained unmoved for over 1,200 years."
        }
      ],
      shareText: "Check out this ancient shore temple in Tamil Nadu, a UNESCO World Heritage Site!"
    },
    ta: {
      title: "கடற்கரை கோவில்",
      location: "மகாபலிபுரம், தமிழ்நாடு",
      category: "நினைவுச்சின்னம்",
      built: "8 ஆம் நூற்றாண்டில் கட்டப்பட்டது",
      unesco: "யுனெஸ்கோ உலக பாரம்பரிய தளம்",
      description: "கடற்கரை கோவில் பல்லவ வம்சத்தின் போது கட்டப்பட்ட ஒரு பழங்கால கிரானைட் கோவில் வளாகம், வங்காள விரிகுடாவின் கரையில் மகத்துவமாக நிற்கிறது. இந்த யுனெஸ்கோ உலக பாரம்பரிய தளம் தென்னிந்தியாவின் மிகப் பழமையான கட்டமைப்பு கல் கோவில்களில் ஒன்றாகும்.",
      about: [
        "மகாபலிபுரத்தில் (மாமல்லபுரம் என்றும் அழைக்கப்படுகிறது) அமைந்துள்ள கடற்கரை கோவில், தென்னிந்தியாவின் மிகப் பழமையான கட்டமைப்பு கல் கோவில்களில் ஒன்றாகும், இது ராஜசிம்மா என்றும் அழைக்கப்படும் பல்லவ மன்னர் நரசிம்மவர்மன் II ஆட்சிக் காலத்தில், 700-728 CE இடையே கட்டப்பட்டது. இது வங்காள விரிகுடாவின் கரையில், கடலை நோக்கி நிற்கிறது, இதனால்தான் இதற்கு இந்த பெயர் வந்தது.",
        "கோவில் மூன்று சிறிய கோவில்களின் தொகுப்பாகும், இரண்டு சிவபெருமானுக்கும் ஒன்று விஷ்ணுவிற்கும் அர்ப்பணிக்கப்பட்டுள்ளது. முக்கிய கோவில் கிழக்கு நோக்கி அமைந்துள்ளது, இதனால் உதயசூரியனின் கதிர்கள் முக்கிய தெய்வத்தை ஒளிரச் செய்கின்றன. கோவிலின் கட்டமைப்பு அதன் பிரமிட் வடிவ கோபுரத்துடன் (சிகரம்) திராவிட கட்டிடக்கலையின் சிறந்த எடுத்துக்காட்டாகும், மற்றும் தளத்தில் வெட்டப்பட்டு செதுக்கப்பட்ட கிரானைட் கற்களால் கட்டப்பட்டுள்ளது.",
        "கடற்கரை கோவிலை குறிப்பாக குறிப்பிடத்தக்கதாக்குவது அதன் இருப்பிடம் மற்றும் உயிர்வாழ்வு. கடல் காற்று மற்றும் அவ்வப்போது ஏற்படும் வெள்ளத்திற்கு நூற்றாண்டுகளாக உட்பட்டிருந்தாலும், கோவில் காலத்தின் சோதனையை தாங்கி நிற்கிறது. வளாகம் பல சிற்பங்களால் சூழப்பட்டுள்ளது, அதில் ஒரு சிங்கத்தின் அற்புதமான ஒற்றைக்கல் செதுக்கல் மற்றும் சயனித்த விஷ்ணுவுடன் ஒரு சிறிய கோவில் ஆகியவை அடங்கும்.",
        "கடற்கரை கோவில் மகாபலிபுரத்தில் உள்ள நினைவுச்சின்னங்களின் குழுவின் ஒரு பகுதியாகும், இது 1984 இல் யுனெஸ்கோ உலக பாரம்பரிய தளமாக அறிவிக்கப்பட்டது. இது பாறை வெட்டப்பட்ட கட்டிடக்கலையிலிருந்து கட்டமைப்பு கட்டிடத்திற்கு மாறும் பல்லவ கலையின் இறுதி கட்டத்தை குறிக்கிறது. கோவிலின் சுவர்கள் சிக்கலான செதுக்கல்களால் அலங்கரிக்கப்பட்டுள்ளன, அதில் பிரபலமான 'கங்கையின் இறக்கம்' பேனல் மற்றும் பல்வேறு புராண கதைகள் அடங்கும்."
      ],
      attractions: [
        {
          name: "பஞ்ச ரதங்கள்",
          description: "தேர்கள் வடிவில் ஐந்து ஒற்றைக்கல் பாறை வெட்டப்பட்ட கோவில்கள், ஒவ்வொன்றும் வெவ்வேறு தெய்வத்திற்கு அர்ப்பணிக்கப்பட்டுள்ளது, திராவிட கட்டிடக்கலையின் பரிணாமத்தைக் காட்டுகிறது."
        },
        {
          name: "அர்ஜுனன் தவம்",
          description: "இந்து புராணக் காட்சிகளைச் சித்தரிக்கும் ஒரு பிரம்மாண்டமான திறந்தவெளி பாஸ்-ரிலீஃப் ஒற்றைக்கல், அர்ஜுனன் சிவபெருமானின் ஆயுதத்தைப் பெறுவதற்காக செய்த தவத்தின் கதை உட்பட."
        },
        {
          name: "கிருஷ்ணரின் வெண்ணெய் பந்து",
          description: "ஒரு மலைச்சரிவில் அமர்ந்துள்ள ஒரு பிரம்மாண்டமான இயற்கை பாறை, ஈர்ப்பு விசையை மீறுவது போல் தோன்றுகிறது, இது 1,200 ஆண்டுகளுக்கும் மேலாக அசையாமல் இருந்து வருகிறது."
        }
      ],
      shareText: "தமிழ்நாட்டில் உள்ள இந்த பழங்கால கடற்கரை கோவிலைப் பாருங்கள், ஒரு யுனெஸ்கோ உலக பாரம்பரிய தளம்!"
    }
  },

  // Ooty Hill Station
  "ooty": {
    en: {
      title: "Ooty Hill Station",
      location: "Nilgiris, Tamil Nadu",
      category: "Hill Station",
      elevation: "2,240 meters above sea level",
      description: "Ooty, also known as Udhagamandalam, is a picturesque hill station in the Nilgiri Hills, famous for its tea gardens, colonial architecture, and pleasant climate throughout the year.",
      about: [
        "Ooty, officially known as Udhagamandalam, is a popular hill station located in the Nilgiri Hills of Tamil Nadu. Situated at an altitude of 2,240 meters (7,350 feet) above sea level, it is often referred to as the 'Queen of Hill Stations' due to its scenic beauty and pleasant climate throughout the year. The temperature rarely exceeds 25°C in summer and rarely falls below 5°C in winter, making it an ideal retreat from the scorching heat of the plains.",
        "The history of Ooty dates back to the early 19th century when it was discovered by British officials. John Sullivan, the Collector of Coimbatore, is credited with establishing Ooty as a summer resort for the British in 1819. The colonial influence is still evident in the architecture of many buildings, churches, and the famous Nilgiri Mountain Railway, a UNESCO World Heritage Site that offers a scenic journey through tunnels, bridges, and tea plantations.",
        "Ooty is renowned for its lush tea gardens that carpet the hillsides with vibrant shades of green. The tea industry, established by the British in the 1830s, continues to be a significant part of the local economy. Visitors can tour tea factories to learn about the tea-making process and sample some of the finest varieties of tea produced in the region.",
        "The hill station is also home to the Government Botanical Garden, established in 1848, which houses a diverse collection of plants, including rare tree species, exotic ferns, and colorful flowers. The annual flower show held in May attracts thousands of visitors. Other attractions include the Ooty Lake, a man-made lake created by Sullivan in 1824, where visitors can enjoy boating, and Doddabetta Peak, the highest point in the Nilgiri Hills, offering panoramic views of the surrounding landscape."
      ],
      attractions: [
        {
          name: "Nilgiri Mountain Railway",
          description: "A UNESCO World Heritage Site, this toy train offers a scenic journey through tunnels, bridges, and tea plantations from Mettupalayam to Ooty."
        },
        {
          name: "Botanical Gardens",
          description: "Established in 1848, these gardens house a diverse collection of plants, including rare tree species, exotic ferns, and colorful flowers."
        },
        {
          name: "Doddabetta Peak",
          description: "The highest point in the Nilgiri Hills at 2,637 meters, offering panoramic views of the surrounding landscape and a telescope house for better viewing."
        }
      ],
      shareText: "Check out this beautiful hill station in Tamil Nadu!"
    },
    ta: {
      title: "ஊட்டி மலை நிலையம்",
      location: "நீலகிரி, தமிழ்நாடு",
      category: "மலை நிலையம்",
      elevation: "கடல் மட்டத்திலிருந்து 2,240 மீட்டர் உயரம்",
      description: "ஊட்டி, உதகமண்டலம் என்றும் அழைக்கப்படுகிறது, நீலகிரி மலைகளில் அமைந்துள்ள ஒரு அழகான மலை நிலையமாகும், இது தேயிலைத் தோட்டங்கள், காலனித்துவ கட்டிடக்கலை, மற்றும் ஆண்டு முழுவதும் இனிமையான காலநிலைக்கு பெயர் பெற்றது.",
      about: [
        "ஊட்டி, அதிகாரப்பூர்வமாக உதகமண்டலம் என்று அழைக்கப்படுகிறது, தமிழ்நாட்டின் நீலகிரி மலைகளில் அமைந்துள்ள ஒரு பிரபலமான மலை நிலையமாகும். கடல் மட்டத்திலிருந்து 2,240 மீட்டர் (7,350 அடி) உயரத்தில் அமைந்துள்ள இது, அதன் இயற்கை அழகு மற்றும் ஆண்டு முழுவதும் இனிமையான காலநிலை காரணமாக 'மலை நிலையங்களின் ராணி' என்று அழைக்கப்படுகிறது. வெப்பநிலை கோடையில் 25°C ஐ மிஞ்சுவதில்லை மற்றும் குளிர்காலத்தில் 5°C க்கு கீழே குறைவதில்லை, இது சமவெளிகளின் கடுமையான வெப்பத்திலிருந்து ஒரு சிறந்த தங்குமிடமாக உள்ளது.",
        "ஊட்டியின் வரலாறு 19 ஆம் நூற்றாண்டின் தொடக்கத்திற்கு திரும்புகிறது, அப்போது இது பிரிட்டிஷ் அதிகாரிகளால் கண்டுபிடிக்கப்பட்டது. கோயம்புத்தூரின் கலெக்டரான ஜான் சல்லிவன், 1819 இல் பிரிட்டிஷாருக்கான கோடைகால தங்குமிடமாக ஊட்டியை நிறுவியதற்கு அங்கீகாரம் பெற்றவர். காலனித்துவ தாக்கம் பல கட்டிடங்கள், தேவாலயங்கள், மற்றும் யுனெஸ்கோ உலக பாரம்பரிய தளமான பிரபலமான நீலகிரி மலை ரயில்வேயின் கட்டிடக்கலையில் இன்னும் தெளிவாகத் தெரிகிறது, இது சுரங்கங்கள், பாலங்கள், மற்றும் தேயிலைத் தோட்டங்கள் வழியாக ஒரு அழகான பயணத்தை வழங்குகிறது.",
        "ஊட்டி அதன் செழுமையான தேயிலைத் தோட்டங்களுக்கு பெயர் பெற்றது, அவை மலைச்சரிவுகளை பசுமையான நிறங்களால் மூடுகின்றன. 1830 களில் பிரிட்டிஷாரால் நிறுவப்பட்ட தேயிலைத் தொழில், உள்ளூர் பொருளாதாரத்தின் ஒரு முக்கிய பகுதியாக தொடர்கிறது. பார்வையாளர்கள் தேயிலை தயாரிப்பு செயல்முறையைப் பற்றி அறிந்து கொள்ளவும், பிராந்தியத்தில் உற்பத்தி செய்யப்படும் சிறந்த தேயிலை வகைகளை ருசிக்கவும் தேயிலை தொழிற்சாலைகளுக்குச் சுற்றுப்பயணம் செய்யலாம்.",
        "இந்த மலை நிலையம் 1848 இல் நிறுவப்பட்ட அரசு தாவரவியல் பூங்காவையும் கொண்டுள்ளது, இது அரிய மர இனங்கள், அரிய வகை பெரணிகள், மற்றும் வண்ணமயமான பூக்கள் உட்பட தாவரங்களின் பல்வேறு சேகரிப்பைக் கொண்டுள்ளது. மே மாதத்தில் நடைபெறும் வருடாந்திர பூக்கள் கண்காட்சி ஆயிரக்கணக்கான பார்வையாளர்களை ஈர்க்கிறது. மற்ற சுற்றுலா தலங்களில் 1824 இல் சல்லிவனால் உருவாக்கப்பட்ட ஒரு செயற்கை ஏரியான ஊட்டி ஏரி, அங்கு பார்வையாளர்கள் படகு சவாரி செய்யலாம், மற்றும் நீலகிரி மலைகளின் உயர்ந்த புள்ளியான டொட்டாபெட்டா சிகரம், சுற்றியுள்ள நிலப்பரப்பின் பனோரமிக் காட்சிகளை வழங்குகிறது."
      ],
      attractions: [
        {
          name: "நீலகிரி மலை ரயில்வே",
          description: "ஒரு யுனெஸ்கோ உலக பாரம்பரிய தளம், இந்த டாய் ரயில் மெட்டுப்பாளையத்திலிருந்து ஊட்டி வரை சுரங்கங்கள், பாலங்கள், மற்றும் தேயிலைத் தோட்டங்கள் வழியாக ஒரு அழகான பயணத்தை வழங்குகிறது."
        },
        {
          name: "தாவரவியல் பூங்காக்கள்",
          description: "1848 இல் நிறுவப்பட்ட இந்த பூங்காக்கள் அரிய மர இனங்கள், அரிய வகை பெரணிகள், மற்றும் வண்ணமயமான பூக்கள் உட்பட தாவரங்களின் பல்வேறு சேகரிப்பைக் கொண்டுள்ளன."
        },
        {
          name: "டொட்டாபெட்டா சிகரம்",
          description: "2,637 மீட்டர் உயரத்தில் நீலகிரி மலைகளின் உயர்ந்த புள்ளி, சுற்றியுள்ள நிலப்பரப்பின் பனோரமிக் காட்சிகளை வழங்குகிறது மற்றும் சிறந்த காட்சிக்காக ஒரு தொலைநோக்கி வீடு உள்ளது."
        }
      ],
      shareText: "தமிழ்நாட்டில் உள்ள இந்த அழகான மலை நிலையத்தைப் பாருங்கள்!"
    }
  },

  // Chettinad Palace
  "chettinad-palace": {
    en: {
      title: "Chettinad Palace",
      location: "Karaikudi, Tamil Nadu",
      category: "Palace",
      built: "Built in early 20th century",
      description: "Chettinad Palace, also known as the Chettinad Mansion, is a magnificent heritage structure showcasing the opulent lifestyle of the Chettiars, a prosperous merchant community. The palace features imported materials, intricate woodwork, colorful tiles, and exquisite craftsmanship that reflects the community's wealth and global connections.",
      about: [
        "Chettinad Palace is a grand mansion located in the Chettinad region of Tamil Nadu, specifically in Karaikudi. It stands as a testament to the wealth and cultural sophistication of the Nattukottai Chettiars, a prosperous community of merchants and bankers who amassed their fortune through trade with Southeast Asia and beyond during the 19th and early 20th centuries.",
        "The palace is renowned for its architectural grandeur, which is a unique blend of various styles including Tamil, European, and East Asian influences. The most striking feature of the palace is its use of materials imported from around the world. The mansion boasts of Italian marble floors, teakwood from Burma, chandeliers from Czechoslovakia, tiles from Japan and Europe, and mirrors from Belgium. The entrance to the palace is particularly impressive, with massive wooden doors adorned with intricate carvings.",
        "The interior of the palace is equally magnificent, featuring spacious courtyards, high ceilings, and elaborate pillars. The walls are adorned with vibrant frescoes and paintings depicting scenes from Hindu mythology and daily life. The palace also houses a collection of antique furniture, vintage photographs, and artifacts that provide insights into the lifestyle of the Chettiar community. The kitchen area, with its traditional setup and utensils, offers a glimpse into the culinary heritage of the region.",
        "Today, Chettinad Palace serves as a cultural heritage site and a popular tourist destination. It offers visitors a chance to step back in time and experience the opulence and grandeur of a bygone era. The palace is also a venue for cultural events and has been featured in several Tamil films due to its picturesque setting. The surrounding Chettinad region is known for its spicy cuisine, handwoven cotton sarees, and antique shops, making it a comprehensive cultural experience for visitors."
      ],
      attractions: [
        {
          name: "Chettinad Heritage Houses",
          description: "Magnificent mansions with unique architecture featuring imported materials, intricate woodwork, and colorful tiles that showcase the wealth of the Chettiar community."
        },
        {
          name: "Athangudi Palace Tiles Factory",
          description: "A traditional handmade tile factory where visitors can observe the unique process of creating the famous colorful Athangudi tiles that adorn Chettinad mansions."
        },
        {
          name: "Chettinad Cuisine Restaurants",
          description: "Restaurants serving authentic Chettinad cuisine, known for its aromatic spices, fresh ingredients, and unique cooking techniques that create bold and flavorful dishes."
        }
      ],
      shareText: "Check out this magnificent palace in Tamil Nadu!"
    },
    ta: {
      title: "செட்டிநாடு அரண்மனை",
      location: "காரைக்குடி, தமிழ்நாடு",
      category: "அரண்மனை",
      built: "20 ஆம் நூற்றாண்டின் தொடக்கத்தில் கட்டப்பட்டது",
      description: "செட்டிநாடு அரண்மனை, செட்டிநாடு மாளிகை என்றும் அழைக்கப்படுகிறது, செழிப்பான வணிக சமூகமான செட்டியார்களின் ஆடம்பரமான வாழ்க்கை முறையைக் காட்டும் ஒரு அற்புதமான பாரம்பரிய கட்டமைப்பாகும். அரண்மனையில் இறக்குமதி செய்யப்பட்ட பொருட்கள், சிக்கலான மரவேலைப்பாடுகள், வண்ணமயமான ஓடுகள் மற்றும் சமூகத்தின் செல்வம் மற்றும் உலகளாவிய தொடர்புகளைப் பிரதிபலிக்கும் அற்புதமான கைவினைப் பொருட்கள் உள்ளன.",
      about: [
        "செட்டிநாடு அரண்மனை தமிழ்நாட்டின் செட்டிநாடு பகுதியில், குறிப்பாக காரைக்குடியில் அமைந்துள்ள ஒரு பெரிய மாளிகையாகும். இது 19 மற்றும் 20 ஆம் நூற்றாண்டுகளில் தென்கிழக்கு ஆசியா மற்றும் அதற்கு அப்பால் வர்த்தகம் மூலம் தங்கள் செல்வத்தை சேகரித்த செழிப்பான வணிகர்கள் மற்றும் வங்கியாளர்களின் சமூகமான நாட்டுக்கோட்டை செட்டியார்களின் செல்வம் மற்றும் கலாச்சார நுட்பத்திற்கு சான்றாக நிற்கிறது.",
        "அரண்மனை தமிழ், ஐரோப்பிய மற்றும் கிழக்கு ஆசிய தாக்கங்கள் உட்பட பல்வேறு பாணிகளின் தனித்துவமான கலவையான அதன் கட்டிடக்கலை பெருமைக்குப் பெயர் பெற்றது. அரண்மனையின் மிகவும் கவர்ச்சிகரமான அம்சம் உலகம் முழுவதிலிருந்தும் இறக்குமதி செய்யப்பட்ட பொருட்களைப் பயன்படுத்துவதாகும். இந்த மாளிகையில் இத்தாலிய மார்பிள் தளங்கள், பர்மாவில் இருந்து தேக்கு மரம், செக்கோஸ்லோவாகியாவில் இருந்து மின்விளக்குகள், ஜப்பான் மற்றும் ஐரோப்பாவில் இருந்து ஓடுகள் மற்றும் பெல்ஜியத்தில் இருந்து கண்ணாடிகள் ஆகியவை உள்ளன. அரண்மனையின் நுழைவாயில் சிக்கலான செதுக்கல்களால் அலங்கரிக்கப்பட்ட பாரிய மரக் கதவுகளுடன் குறிப்பாக அற்புதமாக உள்ளது.",
        "அரண்மனையின் உள்புறம் விசாலமான முற்றங்கள், உயரமான கூரைகள் மற்றும் விரிவான தூண்களைக் கொண்டு சமமாக அற்புதமாக உள்ளது. சுவர்கள் இந்து புராணங்கள் மற்றும் அன்றாட வாழ்க்கையின் காட்சிகளைச் சித்தரிக்கும் துடிப்பான ஓவியங்கள் மற்றும் ஓவியங்களால் அலங்கரிக்கப்பட்டுள்ளன. அரண்மனையில் பண்டைய மரச்சாமான்கள், பழைய புகைப்படங்கள் மற்றும் செட்டியார் சமூகத்தின் வாழ்க்கை முறை பற்றிய நுண்ணறிவுகளை வழங்கும் பொருட்களின் தொகுப்பும் உள்ளது. பாரம்பரிய அமைப்பு மற்றும் பாத்திரங்களுடன் கூடிய சமையலறை பகுதி, பிராந்தியத்தின் சமையல் பாரம்பரியத்தைப் பற்றிய ஒரு பார்வையை வழங்குகிறது.",
        "இன்று, செட்டிநாடு அரண்மனை ஒரு கலாச்சார பாரம்பரிய தளமாகவும், பிரபலமான சுற்றுலா தலமாகவும் செயல்படுகிறது. இது பார்வையாளர்களுக்கு கடந்த காலத்திற்குத் திரும்பி, கடந்த காலத்தின் ஆடம்பரம் மற்றும் பெருமையை அனுபவிக்க ஒரு வாய்ப்பை வழங்குகிறது. அரண்மனை கலாச்சார நிகழ்வுகளுக்கான இடமாகவும் உள்ளது, மேலும் அதன் அழகிய சூழல் காரணமாக பல தமிழ் திரைப்படங்களில் இடம்பெற்றுள்ளது. சுற்றியுள்ள செட்டிநாடு பகுதி அதன் காரமான உணவு, கைத்தறி பருத்தி புடவைகள் மற்றும் பழங்கால கடைகளுக்கு பெயர் பெற்றது, இது பார்வையாளர்களுக்கு ஒரு விரிவான கலாச்சார அனுபவத்தை வழங்குகிறது."
      ],
      attractions: [
        {
          name: "செட்டிநாடு பாரம்பரிய வீடுகள்",
          description: "செட்டியார் சமூகத்தின் செல்வத்தைக் காட்டும் இறக்குமதி செய்யப்பட்ட பொருட்கள், சிக்கலான மரவேலைப்பாடுகள் மற்றும் வண்ணமயமான ஓடுகளைக் கொண்ட தனித்துவமான கட்டிடக்கலையுடன் கூடிய அற்புதமான மாளிகைகள்."
        },
        {
          name: "அத்தங்குடி அரண்மனை ஓடுகள் தொழிற்சாலை",
          description: "செட்டிநாடு மாளிகைகளை அலங்கரிக்கும் பிரபலமான வண்ணமயமான அத்தங்குடி ஓடுகளை உருவாக்கும் தனித்துவமான செயல்முறையை பார்வையாளர்கள் கவனிக்கக்கூடிய ஒரு பாரம்பரிய கைவினை ஓடுகள் தொழிற்சாலை."
        },
        {
          name: "செட்டிநாடு உணவு உணவகங்கள்",
          description: "மணமுள்ள மசாலாக்கள், புதிய பொருட்கள் மற்றும் துணிச்சலான மற்றும் சுவையான உணவுகளை உருவாக்கும் தனித்துவமான சமையல் நுட்பங்களுக்கு பெயர் பெற்ற உண்மையான செட்டிநாடு உணவை வழங்கும் உணவகங்கள்."
        }
      ],
      shareText: "தமிழ்நாட்டில் உள்ள இந்த அற்புதமான அரண்மனையைப் பாருங்கள்!"
    }
  },

  // Gingee Fort
  "gingee-fort": {
    en: {
      title: "Gingee Fort",
      location: "Villupuram, Tamil Nadu",
      category: "Monument",
      built: "Built in 9th century CE",
      feature: "Three Hill Fortress",
      description: "Gingee Fort, known as the \"Troy of the East,\" is a magnificent 9th-century fortress spanning three hills in Villupuram district. This impregnable fort complex features impressive defensive structures, temples, granaries, and water systems, showcasing the military architecture and engineering skills of various dynasties that ruled the region.",
      about: [
        "Gingee Fort, also known as Senji Fort or Chenji Fort, is a remarkable fortification located in the Villupuram district of Tamil Nadu. Often referred to as the \"Troy of the East\" by the British, this imposing fortress is spread across three hills: Rajagiri, Krishnagiri, and Chandrayandurg, forming a triangular layout. The fort's origins date back to the 9th century, with significant expansions and modifications made by various dynasties over the centuries, including the Cholas, Vijayanagara Empire, Marathas, Bijapur Sultanate, Mughals, French, and British.",
        "The fort's strategic importance stems from its unique design and natural defenses. The three hills are connected by walls and fortifications, creating an almost impregnable fortress. The main citadel is situated on Rajagiri hill, which rises to a height of about 800 feet and is accessible only through a narrow, winding path protected by multiple gateways and defensive structures. The fort complex includes numerous buildings such as temples, granaries, stables, ammunition stores, and living quarters, showcasing the military architecture and engineering skills of its builders.",
        "One of the most impressive features of Gingee Fort is its water management system. The fort has several ponds, wells, and underground water storage facilities that ensured a continuous water supply during sieges. The Kalyana Mahal (marriage hall) with its distinctive pyramid-shaped tower is a notable structure within the fort complex. Other significant structures include the Ranganatha Temple, Venkataramana Temple, Sadatulla Khan's Tomb, and the Elephant Tank. The fort also houses a museum that displays artifacts and provides information about its rich history.",
        "Despite its formidable defenses, Gingee Fort changed hands multiple times throughout history. It was captured by the Marathas under Shivaji in 1677, who strengthened its fortifications. Later, it was controlled by the Mughals, the French, and finally the British. Today, the fort stands as a protected monument under the Archaeological Survey of India and attracts history enthusiasts, trekkers, and tourists who come to explore its extensive ruins and enjoy the panoramic views from its hilltops. The fort's historical significance, architectural grandeur, and natural setting make it one of the most impressive fortifications in South India."
      ],
      attractions: [
        {
          name: "Kalyana Mahal",
          description: "A seven-story pyramid-shaped tower within the fort complex, believed to be a palace for royal women with a unique architectural style."
        },
        {
          name: "Venkataramana Temple",
          description: "A beautiful temple dedicated to Lord Vishnu, featuring intricate carvings, pillared halls, and a sacred tank within the fort complex."
        },
        {
          name: "Anantasayanam Lake",
          description: "A serene lake near the fort, offering boating facilities and a peaceful environment with views of the surrounding hills."
        }
      ],
      shareText: "Check out this magnificent fortress in Tamil Nadu!"
    },
    ta: {
      title: "செஞ்சி கோட்டை",
      location: "விழுப்புரம், தமிழ்நாடு",
      category: "நினைவுச்சின்னம்",
      built: "9 ஆம் நூற்றாண்டில் கட்டப்பட்டது",
      feature: "மூன்று மலை கோட்டை",
      description: "\"கிழக்கின் ட்ராய்\" என்று அழைக்கப்படும் செஞ்சி கோட்டை, விழுப்புரம் மாவட்டத்தில் மூன்று மலைகளில் பரந்து விரிந்துள்ள ஒரு அற்புதமான 9 ஆம் நூற்றாண்டு கோட்டையாகும். இந்த எடுத்துக்கொள்ள முடியாத கோட்டை வளாகம் அற்புதமான பாதுகாப்பு கட்டமைப்புகள், கோவில்கள், தானிய களஞ்சியங்கள், மற்றும் நீர் அமைப்புகளைக் கொண்டுள்ளது, இது பிராந்தியத்தை ஆண்ட பல்வேறு வம்சங்களின் இராணுவ கட்டிடக்கலை மற்றும் பொறியியல் திறன்களைக் காட்டுகிறது.",
      about: [
        "செஞ்சி கோட்டை, செஞ்சி கோட்டை அல்லது செஞ்சி கோட்டை என்றும் அழைக்கப்படுகிறது, தமிழ்நாட்டின் விழுப்புரம் மாவட்டத்தில் அமைந்துள்ள ஒரு குறிப்பிடத்தக்க கோட்டையாகும். பிரிட்டிஷாரால் \"கிழக்கின் ட்ராய்\" என்று அடிக்கடி குறிப்பிடப்படும் இந்த அச்சுறுத்தும் கோட்டை மூன்று மலைகளில் பரவியுள்ளது: ராஜகிரி, கிருஷ்ணகிரி, மற்றும் சந்திரயாண்டுர்க், ஒரு முக்கோண அமைப்பை உருவாக்குகிறது. கோட்டையின் தோற்றம் 9 ஆம் நூற்றாண்டிற்கு திரும்புகிறது, சோழர்கள், விஜயநகர பேரரசு, மராத்தியர்கள், பீஜாப்பூர் சுல்தானியம், முகலாயர்கள், பிரெஞ்சு, மற்றும் பிரிட்டிஷ் உட்பட பல நூற்றாண்டுகளாக பல்வேறு வம்சங்களால் குறிப்பிடத்தக்க விரிவாக்கங்கள் மற்றும் மாற்றங்கள் செய்யப்பட்டன.",
        "கோட்டையின் மூலோபாய முக்கியத்துவம் அதன் தனித்துவமான வடிவமைப்பு மற்றும் இயற்கை பாதுகாப்புகளில் இருந்து வருகிறது. மூன்று மலைகள் சுவர்கள் மற்றும் கோட்டைகளால் இணைக்கப்பட்டுள்ளன, இது கிட்டத்தட்ட எடுத்துக்கொள்ள முடியாத கோட்டையை உருவாக்குகிறது. முக்கிய கோட்டை ராஜகிரி மலையில் அமைந்துள்ளது, இது சுமார் 800 அடி உயரத்திற்கு உயர்கிறது மற்றும் பல நுழைவாயில்கள் மற்றும் பாதுகாப்பு கட்டமைப்புகளால் பாதுகாக்கப்படும் ஒரு குறுகிய, வளைந்து செல்லும் பாதை மூலம் மட்டுமே அணுகக்கூடியது. கோட்டை வளாகத்தில் கோவில்கள், தானிய களஞ்சியங்கள், லாயங்கள், வெடிமருந்து கிடங்குகள், மற்றும் வாழும் இடங்கள் போன்ற பல கட்டிடங்கள் உள்ளன, இது அதன் கட்டுமானியாளர்களின் இராணுவ கட்டிடக்கலை மற்றும் பொறியியல் திறன்களைக் காட்டுகிறது.",
        "செஞ்சி கோட்டையின் மிகவும் அற்புதமான அம்சங்களில் ஒன்று அதன் நீர் மேலாண்மை அமைப்பு. கோட்டையில் பல குளங்கள், கிணறுகள், மற்றும் நிலத்தடி நீர் சேமிப்பு வசதிகள் உள்ளன, இவை முற்றுகைகளின் போது தொடர்ச்சியான நீர் விநியோகத்தை உறுதி செய்தன. அதன் தனித்துவமான பிரமிட் வடிவ கோபுரத்துடன் கூடிய கல்யாண மஹால் (திருமண மண்டபம்) கோட்டை வளாகத்தில் ஒரு குறிப்பிடத்தக்க கட்டமைப்பாகும். மற்ற முக்கியமான கட்டமைப்புகளில் ரங்கநாத கோவில், வெங்கடராமன கோவில், சதாதுல்லா கானின் கல்லறை, மற்றும் யானை தொட்டி ஆகியவை அடங்கும். கோட்டையில் பொருட்களைக் காட்சிப்படுத்தும் மற்றும் அதன் செழுமையான வரலாறு பற்றிய தகவல்களை வழங்கும் ஒரு அருங்காட்சியகமும் உள்ளது.",
        "அதன் அச்சுறுத்தும் பாதுகாப்புகள் இருந்தபோதிலும், செஞ்சி கோட்டை வரலாற்றில் பல முறை கைமாறியது. இது 1677 இல் சிவாஜியின் கீழ் மராத்தியர்களால் கைப்பற்றப்பட்டது, அவர்கள் அதன் கோட்டைகளை வலுப்படுத்தினர். பின்னர், இது முகலாயர்கள், பிரெஞ்சு, மற்றும் இறுதியாக பிரிட்டிஷாரால் கட்டுப்படுத்தப்பட்டது. இன்று, கோட்டை இந்திய தொல்பொருள் ஆய்வுத் துறையின் கீழ் பாதுகாக்கப்பட்ட நினைவுச்சின்னமாக நிற்கிறது மற்றும் அதன் விரிவான இடிபாடுகளை ஆராய வரும் மற்றும் அதன் மலை உச்சிகளில் இருந்து பனோரமிக் காட்சிகளை அனுபவிக்கும் வரலாற்று ஆர்வலர்கள், மலையேற்ற வீரர்கள், மற்றும் சுற்றுலாப் பயணிகளை ஈர்க்கிறது. கோட்டையின் வரலாற்று முக்கியத்துவம், கட்டிடக்கலை பெருமை, மற்றும் இயற்கை அமைப்பு இதை தென்னிந்தியாவின் மிகவும் அற்புதமான கோட்டைகளில் ஒன்றாக ஆக்குகிறது."
      ],
      attractions: [
        {
          name: "கல்யாண மஹால்",
          description: "கோட்டை வளாகத்தில் ஏழு மாடி பிரமிட் வடிவ கோபுரம், தனித்துவமான கட்டிடக்கலை பாணியுடன் அரச பெண்களுக்கான அரண்மனை என்று நம்பப்படுகிறது."
        },
        {
          name: "வெங்கடராமன கோவில்",
          description: "விஷ்ணு பகவானுக்கு அர்ப்பணிக்கப்பட்ட ஒரு அழகான கோவில், சிக்கலான செதுக்கல்கள், தூண் மண்டபங்கள், மற்றும் கோட்டை வளாகத்தில் ஒரு புனித குளத்தைக் கொண்டுள்ளது."
        },
        {
          name: "அனந்தசயனம் ஏரி",
          description: "கோட்டைக்கு அருகில் உள்ள ஒரு அமைதியான ஏரி, படகு வசதிகள் மற்றும் சுற்றியுள்ள மலைகளின் காட்சிகளுடன் ஒரு அமைதியான சூழலை வழங்குகிறது."
        }
      ],
      shareText: "தமிழ்நாட்டில் உள்ள இந்த அற்புதமான கோட்டையைப் பாருங்கள்!"
    }
  },

  // Vivekananda Rock Memorial
  "vivekananda-rock-memorial": {
    en: {
      title: "Vivekananda Rock Memorial",
      location: "Kanyakumari, Tamil Nadu",
      category: "Monument",
      built: "Completed in 1970",
      description: "The Vivekananda Rock Memorial is a monument built on a rock island off the coast of Kanyakumari, where Swami Vivekananda meditated in 1892. It features a meditation hall, a statue of the saint, and offers breathtaking views of the meeting point of three oceans.",
      about: [
        "The Vivekananda Rock Memorial is a sacred monument built on a small rock island situated about 500 meters off the mainland of Kanyakumari, at the southernmost tip of India. The memorial stands on one of the two rocks that are situated about 500 meters east of the mainland, where the Arabian Sea, the Bay of Bengal, and the Indian Ocean meet.",
        "The memorial was built in 1970 in honor of Swami Vivekananda, who is said to have attained enlightenment on this rock in December 1892. According to local legends, it was on this rock that Goddess Kanyakumari performed austerities. The rock is also known as Shripada Parai, meaning the rock that has been blessed by the touch of a sacred foot.",
        "The memorial consists of two main structures: the Vivekananda Mandapam and the Shripada Mandapam. The Vivekananda Mandapam houses a statue of Swami Vivekananda and is the main meditation hall where visitors can sit and meditate. The Shripada Mandapam contains a footprint believed to be that of Goddess Kanyakumari.",
        "The architecture of the memorial is a blend of various styles from across India, symbolizing the unity in diversity that Swami Vivekananda preached. The memorial was designed by Eknath Ranade and was built using funds collected from the public. The construction took six years to complete, from 1964 to 1970. Visitors can reach the memorial by ferry services that operate from the mainland, and the memorial offers panoramic views of the surrounding ocean and the mainland."
      ],
      attractions: [
        {
          name: "Thiruvalluvar Statue",
          description: "A 133-foot tall stone statue of the Tamil poet and philosopher Thiruvalluvar, standing on a small island near the memorial."
        },
        {
          name: "Kanyakumari Beach",
          description: "A unique beach where three oceans meet, offering spectacular sunrise and sunset views and a peaceful atmosphere."
        },
        {
          name: "Kumari Amman Temple",
          description: "An ancient temple dedicated to Goddess Kanyakumari (Parvati), with a diamond nose ring that's said to be visible from the sea."
        }
      ],
      shareText: "Check out this historic monument in Tamil Nadu!"
    },
    ta: {
      title: "விவேகானந்தர் பாறை நினைவுச்சின்னம்",
      location: "கன்னியாகுமரி, தமிழ்நாடு",
      category: "நினைவுச்சின்னம்",
      built: "1970 இல் நிறைவு செய்யப்பட்டது",
      description: "விவேகானந்தர் பாறை நினைவுச்சின்னம் கன்னியாகுமரி கடற்கரையில் இருந்து ஒரு பாறைத் தீவில் கட்டப்பட்ட ஒரு நினைவுச்சின்னமாகும், அங்கு சுவாமி விவேகானந்தர் 1892 இல் தியானம் செய்தார். இது ஒரு தியான மண்டபம், துறவியின் சிலை, மற்றும் மூன்று கடல்கள் சந்திக்கும் புள்ளியின் மூச்சடைக்கும் காட்சிகளை வழங்குகிறது.",
      about: [
        "விவேகானந்தர் பாறை நினைவுச்சின்னம் இந்தியாவின் தென்கோடியில், கன்னியாகுமரி பிரதான நிலப்பரப்பில் இருந்து சுமார் 500 மீட்டர் தொலைவில் அமைந்துள்ள ஒரு சிறிய பாறைத் தீவில் கட்டப்பட்ட ஒரு புனித நினைவுச்சின்னமாகும். இந்த நினைவுச்சின்னம் பிரதான நிலப்பரப்பில் இருந்து சுமார் 500 மீட்டர் கிழக்கே அமைந்துள்ள இரண்டு பாறைகளில் ஒன்றில் நிற்கிறது, அங்கு அரபிக் கடல், வங்காள விரிகுடா மற்றும் இந்தியப் பெருங்கடல் சந்திக்கின்றன.",
        "இந்த நினைவுச்சின்னம் 1970 ஆம் ஆண்டில் சுவாமி விவேகானந்தரின் நினைவாக கட்டப்பட்டது, அவர் 1892 ஆம் ஆண்டு டிசம்பரில் இந்த பாறையில் ஞானம் பெற்றதாகக் கூறப்படுகிறது. உள்ளூர் கதைகளின்படி, கன்னியாகுமரி தேவி தவம் செய்தது இந்த பாறையில்தான். இந்த பாறை ஸ்ரீபாத பாறை என்றும் அழைக்கப்படுகிறது, அதாவது புனித பாதத்தின் தொடுதலால் ஆசீர்வதிக்கப்பட்ட பாறை என்று பொருள்.",
        "இந்த நினைவுச்சின்னம் இரண்டு முக்கிய கட்டமைப்புகளைக் கொண்டுள்ளது: விவேகானந்தர் மண்டபம் மற்றும் ஸ்ரீபாத மண்டபம். விவேகானந்தர் மண்டபத்தில் சுவாமி விவேகானந்தரின் சிலை உள்ளது மற்றும் இது முக்கிய தியான மண்டபமாகும், அங்கு பார்வையாளர்கள் அமர்ந்து தியானம் செய்யலாம். ஸ்ரீபாத மண்டபத்தில் கன்னியாகுமரி தேவியின் பாதச்சுவடு என நம்பப்படும் ஒரு பாதச்சுவடு உள்ளது.",
        "நினைவுச்சின்னத்தின் கட்டிடக்கலை இந்தியா முழுவதிலும் இருந்து பல்வேறு பாணிகளின் கலவையாகும், இது சுவாமி விவேகானந்தர் போதித்த பன்முகத்தன்மையில் ஒற்றுமையைக் குறிக்கிறது. இந்த நினைவுச்சின்னம் ஏக்நாத் ரானடேவால் வடிவமைக்கப்பட்டது மற்றும் பொதுமக்களிடமிருந்து சேகரிக்கப்பட்ட நிதியைப் பயன்படுத்தி கட்டப்பட்டது. கட்டுமானம் 1964 முதல் 1970 வரை ஆறு ஆண்டுகள் நிறைவடைந்தது. பார்வையாளர்கள் பிரதான நிலப்பரப்பில் இருந்து இயங்கும் படகு சேவைகள் மூலம் நினைவுச்சின்னத்தை அடையலாம், மற்றும் நினைவுச்சின்னம் சுற்றியுள்ள கடல் மற்றும் பிரதான நிலப்பரப்பின் பனோரமிக் காட்சிகளை வழங்குகிறது."
      ],
      attractions: [
        {
          name: "திருவள்ளுவர் சிலை",
          description: "நினைவுச்சின்னத்திற்கு அருகில் ஒரு சிறிய தீவில் நிற்கும் தமிழ் கவிஞர் மற்றும் தத்துவவாதி திருவள்ளுவரின் 133 அடி உயர கல் சிலை."
        },
        {
          name: "கன்னியாகுமரி கடற்கரை",
          description: "மூன்று கடல்கள் சந்திக்கும் ஒரு தனித்துவமான கடற்கரை, அற்புதமான சூரியோதய மற்றும் சூரியஅஸ்தமன காட்சிகளையும் அமைதியான சூழலையும் வழங்குகிறது."
        },
        {
          name: "குமரி அம்மன் கோவில்",
          description: "கன்னியாகுமரி தேவிக்கு (பார்வதி) அர்ப்பணிக்கப்பட்ட ஒரு பழங்கால கோவில், கடலில் இருந்து பார்க்கக்கூடிய ஒரு வைர மூக்கு வளையத்துடன்."
        }
      ],
      shareText: "தமிழ்நாட்டில் உள்ள இந்த வரலாற்று நினைவுச்சின்னத்தைப் பாருங்கள்!"
    }
  },

  // Mahabalipuram
  "mahabalipuram": {
    en: {
      title: "Mahabalipuram",
      location: "Chengalpattu, Tamil Nadu",
      category: "Monument",
      built: "Built in 7th-8th century CE",
      unesco: "UNESCO World Heritage Site",
      description: "Famous for its rock-cut temples and sculptures, Mahabalipuram is an ancient port city that showcases the architectural brilliance of the Pallava dynasty, featuring magnificent monuments carved from single rocks.",
      about: [
        "Mahabalipuram, also known as Mamallapuram, is an ancient historical town located on the Coromandel Coast of the Bay of Bengal, approximately 60 km south of Chennai. It was a bustling seaport during the time of the Pallava dynasty from the 7th to 9th century CE and served as an important center for art, architecture, and maritime trade.",
        "The town is renowned for its extraordinary rock-cut monuments, monolithic rathas (chariots), cave sanctuaries, and giant open-air reliefs, all carved out of the local granite rock. These structures showcase the evolution of Dravidian temple architecture and the artistic vision of the Pallava rulers, particularly King Narasimhavarman I, who was also known as Mamalla, from whom the town derives its alternate name.",
        "One of the most famous attractions in Mahabalipuram is the Shore Temple, which stands on the beach overlooking the Bay of Bengal. Built during the reign of Narasimhavarman II (Rajasimha), it is one of the oldest structural stone temples in South India. The temple complex consists of three shrines dedicated to Lord Shiva and Lord Vishnu and is adorned with intricate carvings and sculptures.",
        "Another remarkable feature is the massive open-air bas-relief known as 'Arjuna's Penance' or 'Descent of the Ganges,' which is one of the largest rock reliefs in the world. This magnificent carving depicts scenes from Hindu mythology and is a testament to the artistic excellence of the Pallava craftsmen. The Group of Monuments at Mahabalipuram was declared a UNESCO World Heritage Site in 1984, recognizing its outstanding universal value and cultural significance."
      ],
      attractions: [
        {
          name: "Shore Temple",
          description: "An 8th-century granite temple complex standing on the shores of the Bay of Bengal, featuring intricate carvings and a blend of religious iconography."
        },
        {
          name: "Pancha Rathas",
          description: "Five monolithic rock-cut temples in the shape of chariots, each dedicated to a different deity, showcasing the evolution of Dravidian architecture."
        },
        {
          name: "Krishna's Butter Ball",
          description: "A giant natural rock perched on a hillside, seemingly defying gravity, which has remained unmoved for over 1,200 years."
        }
      ],
      shareText: "Check out this ancient city of rock-cut monuments in Tamil Nadu, a UNESCO World Heritage Site!"
    },
    ta: {
      title: "மகாபலிபுரம்",
      location: "செங்கல்பட்டு, தமிழ்நாடு",
      category: "நினைவுச்சின்னம்",
      built: "7-8 ஆம் நூற்றாண்டில் கட்டப்பட்டது",
      unesco: "யுனெஸ்கோ உலக பாரம்பரிய தளம்",
      description: "பாறை வெட்டப்பட்ட கோவில்கள் மற்றும் சிற்பங்களுக்கு பெயர் பெற்ற மகாபலிபுரம், ஒற்றை பாறைகளில் செதுக்கப்பட்ட அற்புதமான நினைவுச்சின்னங்களைக் கொண்ட பல்லவ வம்சத்தின் கட்டிடக்கலை பிரகாசத்தைக் காட்டும் ஒரு பழங்கால துறைமுக நகரமாகும்.",
      about: [
        "மகாபலிபுரம், மாமல்லபுரம் என்றும் அழைக்கப்படுகிறது, வங்காள விரிகுடாவின் கோரமாண்டல் கடற்கரையில் அமைந்துள்ள ஒரு பழங்கால வரலாற்று நகரம், சென்னையிலிருந்து சுமார் 60 கிமீ தெற்கே அமைந்துள்ளது. இது 7 முதல் 9 ஆம் நூற்றாண்டு CE வரை பல்லவ வம்சத்தின் காலத்தில் ஒரு பரபரப்பான கடல் துறைமுகமாக இருந்தது மற்றும் கலை, கட்டிடக்கலை, மற்றும் கடல்சார் வர்த்தகத்திற்கான ஒரு முக்கிய மையமாக செயல்பட்டது.",
        "இந்த நகரம் அதன் அசாதாரண பாறை வெட்டப்பட்ட நினைவுச்சின்னங்கள், ஒற்றைக்கல் ரதங்கள் (தேர்கள்), குகை சரணாலயங்கள், மற்றும் பிரம்மாண்டமான திறந்தவெளி ரிலீஃப்களுக்கு பெயர் பெற்றது, இவை அனைத்தும் உள்ளூர் கிரானைட் பாறையில் செதுக்கப்பட்டவை. இந்த கட்டமைப்புகள் திராவிட கோவில் கட்டிடக்கலையின் பரிணாமத்தையும், குறிப்பாக மாமல்லா என்றும் அழைக்கப்படும் மன்னர் நரசிம்மவர்மன் I உட்பட பல்லவ ஆட்சியாளர்களின் கலை பார்வையையும் காட்டுகின்றன, இவரிடமிருந்து நகரம் அதன் மாற்று பெயரைப் பெறுகிறது.",
        "மகாபலிபுரத்தில் உள்ள மிகவும் பிரபலமான சுற்றுலா தலங்களில் ஒன்று கடற்கரை கோவில், இது வங்காள விரிகுடாவைப் பார்த்தவாறு கடற்கரையில் நிற்கிறது. நரசிம்மவர்மன் II (ராஜசிம்மா) ஆட்சிக் காலத்தில் கட்டப்பட்ட இது தென்னிந்தியாவின் மிகப் பழமையான கட்டமைப்பு கல் கோவில்களில் ஒன்றாகும். கோவில் வளாகம் சிவபெருமான் மற்றும் விஷ்ணுவிற்கு அர்ப்பணிக்கப்பட்ட மூன்று சிறிய கோவில்களைக் கொண்டுள்ளது மற்றும் சிக்கலான செதுக்கல்கள் மற்றும் சிற்பங்களால் அலங்கரிக்கப்பட்டுள்ளது.",
        "மற்றொரு குறிப்பிடத்தக்க அம்சம் 'அர்ஜுனன் தவம்' அல்லது 'கங்கையின் இறக்கம்' என்று அழைக்கப்படும் பிரம்மாண்டமான திறந்தவெளி பாஸ்-ரிலீஃப், இது உலகின் மிகப்பெரிய பாறை ரிலீஃப்களில் ஒன்றாகும். இந்த அற்புதமான செதுக்கல் இந்து புராணக் காட்சிகளைச் சித்தரிக்கிறது மற்றும் பல்லவ கைவினைஞர்களின் கலை சிறப்பிற்கு ஒரு சான்றாகும். மகாபலிபுரத்தில் உள்ள நினைவுச்சின்னங்களின் குழு 1984 இல் யுனெஸ்கோ உலக பாரம்பரிய தளமாக அறிவிக்கப்பட்டது, இது அதன் சிறந்த உலகளாவிய மதிப்பு மற்றும் கலாச்சார முக்கியத்துவத்தை அங்கீகரிக்கிறது."
      ],
      attractions: [
        {
          name: "கடற்கரை கோவில்",
          description: "வங்காள விரிகுடாவின் கரையில் நிற்கும் 8 ஆம் நூற்றாண்டு கிரானைட் கோவில் வளாகம், சிக்கலான செதுக்கல்கள் மற்றும் மத சின்னவியலின் கலவையைக் கொண்டுள்ளது."
        },
        {
          name: "பஞ்ச ரதங்கள்",
          description: "தேர்கள் வடிவில் ஐந்து ஒற்றைக்கல் பாறை வெட்டப்பட்ட கோவில்கள், ஒவ்வொன்றும் வெவ்வேறு தெய்வத்திற்கு அர்ப்பணிக்கப்பட்டுள்ளது, திராவிட கட்டிடக்கலையின் பரிணாமத்தைக் காட்டுகிறது."
        },
        {
          name: "கிருஷ்ணரின் வெண்ணெய் பந்து",
          description: "ஒரு மலைச்சரிவில் அமர்ந்துள்ள ஒரு பிரம்மாண்டமான இயற்கை பாறை, ஈர்ப்பு விசையை மீறுவது போல் தோன்றுகிறது, இது 1,200 ஆண்டுகளுக்கும் மேலாக அசையாமல் இருந்து வருகிறது."
        }
      ],
      shareText: "தமிழ்நாட்டில் உள்ள பாறை வெட்டப்பட்ட நினைவுச்சின்னங்களின் இந்த பழங்கால நகரத்தைப் பாருங்கள், ஒரு யுனெஸ்கோ உலக பாரம்பரிய தளம்!"
    }
  },

  // Marina Beach
  "marina-beach": {
    en: {
      title: "Marina Beach",
      location: "Chennai, Tamil Nadu",
      category: "Beach",
      length: "13 kilometers long",
      description: "Marina Beach is the second-longest urban beach in the world, stretching along the Bay of Bengal in Chennai. This iconic landmark offers stunning sunrise views, historic monuments, and a vibrant atmosphere with various recreational activities.",
      about: [
        "Marina Beach, located in Chennai (formerly Madras), is the second-longest urban beach in the world, stretching approximately 13 kilometers (8.1 miles) along the Bay of Bengal. The beach was named after the Italian word 'marina,' meaning a promenade by the sea, during the British colonial period in the late 19th century. It has since become one of the most iconic landmarks of Chennai and a popular recreational spot for both locals and tourists.",
        "The beach's history is intertwined with the development of Chennai city. The construction of the Madras Harbor in the late 19th century led to the formation of the beach as we know it today. Over the years, it has witnessed significant historical events, including being a site for political rallies during India's freedom struggle. Today, the beach is not just a natural attraction but also a cultural and historical landmark.",
        "Marina Beach is known for its golden sands and offers spectacular views of sunrise over the Bay of Bengal. The beach is lined with several important monuments and statues of Tamil historical figures and leaders, including Mahatma Gandhi, poet Bharathidasan, and former Chief Ministers of Tamil Nadu. The Indo-Saracenic style Madras University buildings and the iconic lighthouse add to the beach's architectural charm.",
        "Despite being a popular tourist destination, Marina Beach is not considered safe for swimming due to strong undercurrents. However, it offers various recreational activities such as beach volleyball, kite flying, and horse riding. The beach is also known for its vibrant atmosphere, with numerous food stalls selling local delicacies, especially in the evenings when families and friends gather to enjoy the sea breeze and sunset."
      ],
      attractions: [
        {
          name: "Chennai Lighthouse",
          description: "A historic lighthouse offering panoramic views of the city and the Bay of Bengal from its observation deck."
        },
        {
          name: "Madras University",
          description: "A prestigious institution with beautiful Indo-Saracenic architecture, established in 1857, making it one of the oldest universities in India."
        },
        {
          name: "MGR Memorial",
          description: "A memorial dedicated to former Tamil Nadu Chief Minister and actor M.G. Ramachandran, featuring a grand structure with a museum showcasing his life and achievements."
        }
      ],
      shareText: "Check out the second-longest urban beach in the world in Chennai, Tamil Nadu!"
    },
    ta: {
      title: "மெரினா கடற்கரை",
      location: "சென்னை, தமிழ்நாடு",
      category: "கடற்கரை",
      length: "13 கிலோமீட்டர் நீளம்",
      description: "மெரினா கடற்கரை உலகின் இரண்டாவது நீளமான நகர்ப்புற கடற்கரையாகும், சென்னையில் வங்காள விரிகுடா முழுவதும் நீண்டுள்ளது. இந்த புகழ்பெற்ற நிலச்சின்னம் அற்புதமான சூரிய உதய காட்சிகள், வரலாற்று நினைவுச்சின்னங்கள், மற்றும் பல்வேறு பொழுதுபோக்கு நடவடிக்கைகளுடன் ஒரு துடிப்பான சூழலை வழங்குகிறது.",
      about: [
        "சென்னையில் (முன்னர் மெட்ராஸ்) அமைந்துள்ள மெரினா கடற்கரை, உலகின் இரண்டாவது நீளமான நகர்ப்புற கடற்கரையாகும், வங்காள விரிகுடா முழுவதும் சுமார் 13 கிலோமீட்டர் (8.1 மைல்) நீளத்திற்கு நீண்டுள்ளது. 19 ஆம் நூற்றாண்டின் பிற்பகுதியில் பிரிட்டிஷ் காலனித்துவ காலத்தில் கடலோரப் பகுதியில் ஒரு நடைபாதை என்று பொருள்படும் இத்தாலிய வார்த்தையான 'மரினா' என்ற பெயரிடப்பட்டது. அன்றிலிருந்து இது சென்னையின் மிகவும் புகழ்பெற்ற நிலச்சின்னங்களில் ஒன்றாகவும், உள்ளூர் மக்கள் மற்றும் சுற்றுலாப் பயணிகள் இருவருக்கும் ஒரு பிரபலமான பொழுதுபோக்கு இடமாகவும் மாறியுள்ளது.",
        "கடற்கரையின் வரலாறு சென்னை நகரத்தின் வளர்ச்சியுடன் பின்னிப் பிணைந்துள்ளது. 19 ஆம் நூற்றாண்டின் பிற்பகுதியில் மெட்ராஸ் துறைமுகத்தின் கட்டுமானம் இன்று நாம் அறிந்த கடற்கரையின் உருவாக்கத்திற்கு வழிவகுத்தது. ஆண்டுகளாக, இது இந்தியாவின் சுதந்திரப் போராட்டத்தின் போது அரசியல் பேரணிகளுக்கான இடமாக இருந்தது உட்பட குறிப்பிடத்தக்க வரலாற்று நிகழ்வுகளை கண்டுள்ளது. இன்று, கடற்கரை வெறும் இயற்கை சுற்றுலா தலம் மட்டுமல்ல, ஒரு கலாச்சார மற்றும் வரலாற்று நிலச்சின்னமாகவும் உள்ளது.",
        "மெரினா கடற்கரை அதன் தங்க நிற மணல்களுக்கு பெயர் பெற்றது மற்றும் வங்காள விரிகுடாவில் சூரிய உதயத்தின் அற்புதமான காட்சிகளை வழங்குகிறது. கடற்கரையில் மகாத்மா காந்தி, கவிஞர் பாரதிதாசன், மற்றும் தமிழ்நாட்டின் முன்னாள் முதலமைச்சர்கள் உட்பட பல முக்கியமான நினைவுச்சின்னங்கள் மற்றும் தமிழ் வரலாற்று ஆளுமைகள் மற்றும் தலைவர்களின் சிலைகள் உள்ளன. இந்தோ-சாரசெனிக் பாணியிலான மெட்ராஸ் பல்கலைக்கழக கட்டிடங்கள் மற்றும் புகழ்பெற்ற கலங்கரை விளக்கம் கடற்கரையின் கட்டிடக்கலை கவர்ச்சியை அதிகரிக்கின்றன.",
        "ஒரு பிரபலமான சுற்றுலா தலமாக இருந்தாலும், வலுவான அடியோட்டங்கள் காரணமாக மெரினா கடற்கரை நீச்சலுக்கு பாதுகாப்பானதாக கருதப்படவில்லை. இருப்பினும், இது கடற்கரை வாலிபால், பட்டம் பறக்கவிடுதல், மற்றும் குதிரை சவாரி போன்ற பல்வேறு பொழுதுபோக்கு நடவடிக்கைகளை வழங்குகிறது. கடற்கரை அதன் துடிப்பான சூழலுக்கும் பெயர் பெற்றது, குறிப்பாக மாலை நேரங்களில் குடும்பங்கள் மற்றும் நண்பர்கள் கடல் காற்று மற்றும் சூரிய அஸ்தமனத்தை அனுபவிக்க கூடும்போது, உள்ளூர் உணவு வகைகளை விற்கும் பல உணவு கடைகள் உள்ளன."
      ],
      attractions: [
        {
          name: "சென்னை கலங்கரை விளக்கம்",
          description: "அதன் பார்வை தளத்திலிருந்து நகரம் மற்றும் வங்காள விரிகுடாவின் பனோரமிக் காட்சிகளை வழங்கும் ஒரு வரலாற்று கலங்கரை விளக்கம்."
        },
        {
          name: "மெட்ராஸ் பல்கலைக்கழகம்",
          description: "அழகான இந்தோ-சாரசெனிக் கட்டிடக்கலையுடன் கூடிய ஒரு மதிப்புமிக்க நிறுவனம், 1857 இல் நிறுவப்பட்டது, இது இந்தியாவின் மிகப் பழமையான பல்கலைக்கழகங்களில் ஒன்றாகும்."
        },
        {
          name: "எம்ஜிஆர் நினைவிடம்",
          description: "முன்னாள் தமிழ்நாடு முதலமைச்சர் மற்றும் நடிகர் எம்.ஜி.ராமச்சந்திரனுக்கு அர்ப்பணிக்கப்பட்ட ஒரு நினைவிடம், அவரது வாழ்க்கை மற்றும் சாதனைகளைக் காட்சிப்படுத்தும் ஒரு அருங்காட்சியகத்துடன் கூடிய ஒரு பிரம்மாண்டமான கட்டமைப்பைக் கொண்டுள்ளது."
        }
      ],
      shareText: "சென்னை, தமிழ்நாட்டில் உள்ள உலகின் இரண்டாவது நீளமான நகர்ப்புற கடற்கரையைப் பாருங்கள்!"
    }
  },

  // Government Museum Chennai
  "government-museum-chennai": {
    en: {
      title: "Government Museum Chennai",
      location: "Chennai, Tamil Nadu",
      category: "Museum",
      established: "Established in 1851",
      description: "The Government Museum Chennai, also known as Madras Museum, is one of the oldest museums in India, housing a rich collection of archaeological artifacts, sculptures, paintings, and natural history specimens that showcase the cultural heritage of South India.",
      about: [
        "The Government Museum Chennai, established in 1851, is the second oldest museum in India after the Indian Museum in Kolkata. Located in the Egmore neighborhood of Chennai, it is housed in a stunning Indo-Saracenic building complex that was designed by Henry Irwin and completed in 1896. The museum complex spans over 16.25 acres and includes six buildings with 46 galleries, making it one of the largest museums in South Asia.",
        "The museum's collection is vast and diverse, covering archaeology, numismatics, zoology, natural history, and art. The archaeological section is particularly noteworthy, featuring artifacts from the ancient civilizations of South India, including items from the Indus Valley, Buddhist relics, and sculptures from the Chola, Pallava, and Vijayanagara periods. The bronze gallery houses one of the world's finest collections of South Indian bronzes, with exquisite Chola bronzes dating back to the 10th-13th centuries.",
        "The museum also boasts an impressive collection of paintings, including works from the Tanjore and Mysore schools, as well as British colonial-era portraits and landscapes. The natural history section features a wide range of specimens, including a 150-million-year-old fossilized tree trunk and a complete skeleton of a whale. The children's museum within the complex offers interactive exhibits and educational programs designed to engage young visitors.",
        "Beyond its role as a repository of artifacts, the Government Museum Chennai serves as an important educational and research institution. It regularly hosts exhibitions, workshops, and cultural events that promote awareness and appreciation of India's rich cultural heritage. The museum's library contains rare manuscripts and publications, providing valuable resources for scholars and researchers interested in the history and culture of South India."
      ],
      attractions: [
        {
          name: "National Art Gallery",
          description: "Located within the museum complex, it houses a collection of paintings from various Indian schools, including Tanjore, Mysore, and Rajput, as well as miniature paintings and portraits."
        },
        {
          name: "Contemporary Art Gallery",
          description: "Features modern Indian art, including works by prominent artists like Raja Ravi Varma, Jamini Roy, and members of the Bengal School."
        },
        {
          name: "Children's Museum",
          description: "An interactive museum designed specifically for children, with educational exhibits on science, history, and culture, including a dinosaur section."
        }
      ],
      shareText: "Check out this historic museum in Chennai, one of the oldest in India!"
    },
    ta: {
      title: "சென்னை அரசு அருங்காட்சியகம்",
      location: "சென்னை, தமிழ்நாடு",
      category: "அருங்காட்சியகம்",
      established: "1851 இல் நிறுவப்பட்டது",
      description: "சென்னை அரசு அருங்காட்சியகம், மெட்ராஸ் அருங்காட்சியகம் என்றும் அழைக்கப்படுகிறது, இது இந்தியாவின் மிகப் பழமையான அருங்காட்சியகங்களில் ஒன்றாகும், தென்னிந்தியாவின் கலாச்சார பாரம்பரியத்தைக் காட்சிப்படுத்தும் தொல்பொருள் பொருட்கள், சிற்பங்கள், ஓவியங்கள், மற்றும் இயற்கை வரலாற்று மாதிரிகளின் செழுமையான சேகரிப்பைக் கொண்டுள்ளது.",
      about: [
        "1851 இல் நிறுவப்பட்ட சென்னை அரசு அருங்காட்சியகம், கொல்கத்தாவில் உள்ள இந்திய அருங்காட்சியகத்திற்கு அடுத்தபடியாக இந்தியாவின் இரண்டாவது பழமையான அருங்காட்சியகமாகும். சென்னையின் எழும்பூர் பகுதியில் அமைந்துள்ள இது, ஹென்றி இர்வின் வடிவமைத்து 1896 இல் முடிக்கப்பட்ட அற்புதமான இந்தோ-சாரசெனிக் கட்டிட வளாகத்தில் அமைந்துள்ளது. அருங்காட்சியக வளாகம் 16.25 ஏக்கர் பரப்பளவில் பரவியுள்ளது மற்றும் 46 காட்சியகங்களுடன் ஆறு கட்டிடங்களைக் கொண்டுள்ளது, இது தெற்காசியாவின் மிகப்பெரிய அருங்காட்சியகங்களில் ஒன்றாக உள்ளது.",
        "அருங்காட்சியகத்தின் சேகரிப்பு பரந்தது மற்றும் பல்வேறு வகைப்பட்டது, தொல்பொருளியல், நாணயவியல், விலங்கியல், இயற்கை வரலாறு, மற்றும் கலையை உள்ளடக்கியது. தொல்பொருள் பிரிவு குறிப்பாக குறிப்பிடத்தக்கது, சிந்து சமவெளி, பௌத்த எச்சங்கள், மற்றும் சோழர், பல்லவர், மற்றும் விஜயநகர காலங்களின் சிற்பங்கள் உட்பட தென்னிந்தியாவின் பழங்கால நாகரிகங்களின் பொருட்களைக் காட்சிப்படுத்துகிறது. வெண்கல காட்சியகம் உலகின் சிறந்த தென்னிந்திய வெண்கலங்களின் சேகரிப்புகளில் ஒன்றைக் கொண்டுள்ளது, 10-13 ஆம் நூற்றாண்டுகளைச் சேர்ந்த அற்புதமான சோழர் வெண்கலங்களுடன்.",
        "அருங்காட்சியகம் தஞ்சாவூர் மற்றும் மைசூர் பள்ளிகளின் படைப்புகள், அத்துடன் பிரிட்டிஷ் காலனித்துவ கால உருவப்படங்கள் மற்றும் நிலப்பரப்புகள் உட்பட ஓவியங்களின் அற்புதமான சேகரிப்பையும் கொண்டுள்ளது. இயற்கை வரலாற்று பிரிவு 150 மில்லியன் ஆண்டுகள் பழமையான புதைபடிவ மரத்தடி மற்றும் ஒரு திமிங்கலத்தின் முழுமையான எலும்புக்கூடு உட்பட பல்வேறு மாதிரிகளைக் கொண்டுள்ளது. வளாகத்தில் உள்ள குழந்தைகள் அருங்காட்சியகம் இளம் பார்வையாளர்களை ஈடுபடுத்த வடிவமைக்கப்பட்ட ஊடாடும் காட்சிப்பொருட்கள் மற்றும் கல்வி நிகழ்ச்சிகளை வழங்குகிறது.",
        "பொருட்களின் களஞ்சியமாக அதன் பங்கைத் தாண்டி, சென்னை அரசு அருங்காட்சியகம் ஒரு முக்கியமான கல்வி மற்றும் ஆராய்ச்சி நிறுவனமாக செயல்படுகிறது. இது இந்தியாவின் செழுமையான கலாச்சார பாரம்பரியத்தைப் பற்றிய விழிப்புணர்வையும் பாராட்டையும் ஊக்குவிக்கும் கண்காட்சிகள், பட்டறைகள், மற்றும் கலாச்சார நிகழ்வுகளை வழக்கமாக நடத்துகிறது. அருங்காட்சியகத்தின் நூலகம் அரிய கையெழுத்துப் பிரதிகள் மற்றும் வெளியீடுகளைக் கொண்டுள்ளது, தென்னிந்தியாவின் வரலாறு மற்றும் கலாச்சாரத்தில் ஆர்வமுள்ள அறிஞர்கள் மற்றும் ஆராய்ச்சியாளர்களுக்கு மதிப்புமிக்க ஆதாரங்களை வழங்குகிறது."
      ],
      attractions: [
        {
          name: "தேசிய கலை காட்சியகம்",
          description: "அருங்காட்சியக வளாகத்தில் அமைந்துள்ள இது, தஞ்சாவூர், மைசூர், மற்றும் ராஜபுத் உட்பட பல்வேறு இந்திய பள்ளிகளின் ஓவியங்கள், அத்துடன் சிறிய ஓவியங்கள் மற்றும் உருவப்படங்களின் சேகரிப்பைக் கொண்டுள்ளது."
        },
        {
          name: "சமகால கலை காட்சியகம்",
          description: "ராஜா ரவி வர்மா, ஜமினி ராய், மற்றும் வங்காள பள்ளியின் உறுப்பினர்கள் போன்ற முக்கிய கலைஞர்களின் படைப்புகள் உட்பட நவீன இந்திய கலையை காட்சிப்படுத்துகிறது."
        },
        {
          name: "குழந்தைகள் அருங்காட்சியகம்",
          description: "குறிப்பாக குழந்தைகளுக்காக வடிவமைக்கப்பட்ட ஒரு ஊடாடும் அருங்காட்சியகம், டைனோசர் பிரிவு உட்பட அறிவியல், வரலாறு, மற்றும் கலாச்சாரம் பற்றிய கல்வி காட்சிப்பொருட்களைக் கொண்டுள்ளது."
        }
      ],
      shareText: "சென்னையில் உள்ள இந்த வரலாற்று அருங்காட்சியகத்தைப் பாருங்கள், இந்தியாவின் மிகப் பழமையானவற்றில் ஒன்று!"
    }
  },

  // Add translations for other pages here...
};

// Initialize language state
let currentLanguage = "en";

// Function to update content based on language
function updateContent(pageName) {
  // Get translations for this page
  const pageTranslations = translations[pageName] || {};
  const commonTranslations = translations.common;

  // Update common elements
  document.querySelector("header h1").textContent = commonTranslations[currentLanguage].mainHeading;
  document.querySelector("header p").textContent = commonTranslations[currentLanguage].subHeading;
  document.querySelector("footer p").textContent = commonTranslations[currentLanguage].footerText;
  document.getElementById("toggleLang").textContent = commonTranslations[currentLanguage].toggleLang;

  // Update navigation links
  const navLinks = document.querySelectorAll("nav a:not(#toggleLang)");
  if (navLinks.length >= 3) {
    navLinks[0].innerHTML = `<i class="fas fa-home"></i> ${commonTranslations[currentLanguage].homeLink}`;
    navLinks[1].innerHTML = `<i class="fas fa-map-marked-alt"></i> ${commonTranslations[currentLanguage].exploreLink}`;
    navLinks[2].innerHTML = `<i class="fas fa-info-circle"></i> ${commonTranslations[currentLanguage].aboutLink}`;
  }

  // Update buttons
  document.getElementById("playAudioButton").innerHTML = `<i class="fas fa-volume-up"></i> ${commonTranslations[currentLanguage].listenButton}`;
  document.getElementById("favoriteButton").innerHTML = `<i class="${document.getElementById("favoriteButton").querySelector("i").className}"></i> ${commonTranslations[currentLanguage].favoriteButton}`;
  document.getElementById("shareButton").innerHTML = `<i class="fas fa-share-alt"></i> ${commonTranslations[currentLanguage].shareButton}`;

  // Update travel information section
  updateTravelInfo(commonTranslations[currentLanguage]);

  // Update page-specific content if available
  if (pageTranslations[currentLanguage]) {
    // Update title and meta information
    document.querySelector(".site-info h1").textContent = pageTranslations[currentLanguage].title;

    // Update meta items if they exist
    const metaItems = document.querySelectorAll(".meta-item span");
    if (metaItems.length >= 3) {
      metaItems[0].textContent = pageTranslations[currentLanguage].location;
      metaItems[1].textContent = pageTranslations[currentLanguage].category;
      if (pageTranslations[currentLanguage].elevation) {
        metaItems[2].textContent = pageTranslations[currentLanguage].elevation;
      }
    }

    // Update description
    document.querySelector(".site-info p").textContent = pageTranslations[currentLanguage].description;

    // Update about section heading and paragraphs
    document.querySelector(".site-description h2").textContent = `${commonTranslations[currentLanguage].aboutHeading} ${pageTranslations[currentLanguage].title}`;

    const aboutParagraphs = document.querySelectorAll(".site-description p");
    if (aboutParagraphs.length === pageTranslations[currentLanguage].about.length) {
      aboutParagraphs.forEach((p, index) => {
        p.textContent = pageTranslations[currentLanguage].about[index];
      });
    }

    // Update nearby attractions heading
    document.querySelector(".nearby-attractions h2").textContent = commonTranslations[currentLanguage].nearbyHeading;

    // Update attraction cards if they exist
    const attractionCards = document.querySelectorAll(".attraction-card");
    if (attractionCards.length === pageTranslations[currentLanguage].attractions.length) {
      attractionCards.forEach((card, index) => {
        card.querySelector("h3").textContent = pageTranslations[currentLanguage].attractions[index].name;
        card.querySelector("p").textContent = pageTranslations[currentLanguage].attractions[index].description;
      });
    }
  }

  // Update audio language
  const audioButton = document.getElementById("playAudioButton");
  if (audioButton) {
    audioButton.onclick = () => {
      const description = document.querySelector(".site-info p").textContent;
      const utterance = new SpeechSynthesisUtterance(description);
      utterance.lang = currentLanguage === "en" ? "en-US" : "ta-IN";
      speechSynthesis.speak(utterance);
    };
  }

  // Update share button text
  const shareButton = document.getElementById("shareButton");
  if (shareButton && pageTranslations[currentLanguage] && pageTranslations[currentLanguage].shareText) {
    const originalShareData = {
      title: `${pageTranslations[currentLanguage].title} - ${commonTranslations[currentLanguage].mainHeading}`,
      text: pageTranslations[currentLanguage].shareText,
      url: window.location.href
    };

    shareButton.onclick = () => {
      if (navigator.share) {
        navigator.share(originalShareData)
          .then(() => console.log(commonTranslations[currentLanguage].sharedSuccessfully))
          .catch((error) => console.error("Error sharing:", error));
      } else {
        navigator.clipboard.writeText(originalShareData.url)
          .then(() => alert(commonTranslations[currentLanguage].copiedToClipboard))
          .catch((err) => console.error("Could not copy text: ", err));
      }
    };
  }

  // Update favorite button alerts
  const favoriteButton = document.getElementById("favoriteButton");
  if (favoriteButton) {
    favoriteButton.onclick = function() {
      const icon = this.querySelector("i");
      if (icon.classList.contains("far")) {
        icon.classList.remove("far");
        icon.classList.add("fas");
        alert(commonTranslations[currentLanguage].addedToFavorites);
      } else {
        icon.classList.remove("fas");
        icon.classList.add("far");
        alert(commonTranslations[currentLanguage].removedFromFavorites);
      }
    };
  }
}

// Function to toggle language
function toggleLanguage(pageName) {
  currentLanguage = currentLanguage === "en" ? "ta" : "en";
  updateContent(pageName);
}

// Function to update travel information section
function updateTravelInfo(translations) {
  // Travel info header
  const travelInfoHeading = document.querySelector(".travel-info-header h2");
  if (travelInfoHeading) {
    travelInfoHeading.textContent = translations.travelInfoHeading;
  }

  const lastUpdatedLabel = document.querySelector(".last-updated");
  if (lastUpdatedLabel) {
    const dateSpan = lastUpdatedLabel.querySelector("span");
    if (dateSpan) {
      const date = dateSpan.textContent;
      lastUpdatedLabel.innerHTML = `${translations.lastUpdated} <span id="lastUpdated">${date}</span>`;
    }
  }

  // Travel info tabs
  const weatherTab = document.querySelector('.travel-tab[data-tab="weather"]');
  if (weatherTab) {
    weatherTab.innerHTML = `<i class="fas fa-cloud-sun"></i> ${translations.weatherTab}`;
  }

  const transportTab = document.querySelector('.travel-tab[data-tab="transport"]');
  if (transportTab) {
    transportTab.innerHTML = `<i class="fas fa-bus"></i> ${translations.transportTab}`;
  }

  const hoursTab = document.querySelector('.travel-tab[data-tab="hours"]');
  if (hoursTab) {
    hoursTab.innerHTML = `<i class="far fa-clock"></i> ${translations.hoursTab}`;
  }

  const amenitiesTab = document.querySelector('.travel-tab[data-tab="amenities"]');
  if (amenitiesTab) {
    amenitiesTab.innerHTML = `<i class="fas fa-concierge-bell"></i> ${translations.amenitiesTab}`;
  }

  // Weather panel
  const currentWeatherHeading = document.querySelector('.weather-details h3');
  if (currentWeatherHeading) {
    currentWeatherHeading.textContent = translations.currentWeather;
  }

  const weatherTipsHeading = document.querySelector('.weather-tips h3');
  if (weatherTipsHeading) {
    weatherTipsHeading.textContent = translations.weatherTips;
  }

  // Transportation panel
  const byAirHeading = document.querySelector('.transport-option:nth-child(1) h3');
  if (byAirHeading) {
    byAirHeading.innerHTML = `<i class="fas fa-plane"></i> ${translations.byAir}`;
  }

  const byTrainHeading = document.querySelector('.transport-option:nth-child(2) h3');
  if (byTrainHeading) {
    byTrainHeading.innerHTML = `<i class="fas fa-train"></i> ${translations.byTrain}`;
  }

  const byBusHeading = document.querySelector('.transport-option:nth-child(3) h3');
  if (byBusHeading) {
    byBusHeading.innerHTML = `<i class="fas fa-bus"></i> ${translations.byBus}`;
  }

  const byCarHeading = document.querySelector('.transport-option:nth-child(4) h3');
  if (byCarHeading) {
    byCarHeading.innerHTML = `<i class="fas fa-car"></i> ${translations.byCar}`;
  }

  const localTransportHeading = document.querySelector('.local-transport h3');
  if (localTransportHeading) {
    localTransportHeading.textContent = translations.localTransport;
  }

  // Hours & Tickets panel
  const openingHoursHeading = document.querySelector('.opening-hours h3');
  if (openingHoursHeading) {
    openingHoursHeading.innerHTML = `<i class="far fa-clock"></i> ${translations.openingHours}`;
  }

  const ticketInfoHeading = document.querySelector('.ticket-info h3');
  if (ticketInfoHeading) {
    ticketInfoHeading.innerHTML = `<i class="fas fa-ticket-alt"></i> ${translations.ticketInfo}`;
  }

  const bestTimeHeading = document.querySelector('.best-time h3');
  if (bestTimeHeading) {
    bestTimeHeading.textContent = translations.bestTimeToVisit;
  }

  // Amenities panel
  const accommodationHeading = document.querySelector('.amenity-card:nth-child(1) h3');
  if (accommodationHeading) {
    accommodationHeading.innerHTML = `<i class="fas fa-hotel"></i> ${translations.accommodation}`;
  }

  const restaurantsHeading = document.querySelector('.amenity-card:nth-child(2) h3');
  if (restaurantsHeading) {
    restaurantsHeading.innerHTML = `<i class="fas fa-utensils"></i> ${translations.restaurants}`;
  }

  const shoppingHeading = document.querySelector('.amenity-card:nth-child(3) h3');
  if (shoppingHeading) {
    shoppingHeading.innerHTML = `<i class="fas fa-shopping-bag"></i> ${translations.shopping}`;
  }

  const medicalHeading = document.querySelector('.amenity-card:nth-child(4) h3');
  if (medicalHeading) {
    medicalHeading.innerHTML = `<i class="fas fa-first-aid"></i> ${translations.medicalFacilities}`;
  }

  const cafesHeading = document.querySelector('.amenity-card:nth-child(5) h3');
  if (cafesHeading) {
    cafesHeading.innerHTML = `<i class="fas fa-coffee"></i> ${translations.cafes}`;
  }

  const touristInfoHeading = document.querySelector('.amenity-card:nth-child(6) h3');
  if (touristInfoHeading) {
    touristInfoHeading.innerHTML = `<i class="fas fa-info-circle"></i> ${translations.touristInfo}`;
  }

  // Update distance text
  const distanceElements = document.querySelectorAll('.distance');
  distanceElements.forEach(element => {
    const text = element.textContent;
    if (text.includes('km')) {
      const number = text.split(' ')[0];
      element.textContent = `${number} ${translations.kmAway}`;
    }
  });
}

// Add event listener to language toggle button
document.addEventListener("DOMContentLoaded", function() {
  const toggleLangButton = document.getElementById("toggleLang");
  if (toggleLangButton) {
    // Extract page name from URL
    const pageName = window.location.pathname.split('/').pop().replace('.html', '');

    // Add click event listener
    toggleLangButton.addEventListener("click", () => toggleLanguage(pageName));
  }
});
