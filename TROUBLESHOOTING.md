# Heritage Explorer - Troubleshooting Guide

## ❌ Error: "Unexpected token '<', "<?php requ"... is not valid JSON"

### **Problem:**
You're accessing the website through a static file server (like Live Server extension) instead of a proper web server that can execute PHP files.

### **Solution:**

#### **Step 1: Stop Current Server**
- Close any browser tabs running on `http://127.0.0.1:5500` or similar ports
- Stop any Live Server extension or static file server

#### **Step 2: Use XAMPP/WAMP**
1. **Install XAMPP** (if not installed):
   - Download from: https://www.apachefriends.org/
   - Install and run the installer

2. **Start Services**:
   - Open XAMPP Control Panel
   - Start **Apache** service
   - Start **MySQL** service

3. **Move Files**:
   - Copy ALL your project files to: `C:\xampp\htdocs\heritage-explorer\`
   - Make sure all files are in this directory

#### **Step 3: Access Correctly**
- ✅ **Correct URL**: `http://localhost/heritage-explorer/website.html`
- ❌ **Wrong URL**: `http://127.0.0.1:5500/website.html`

---

## ❌ Error: "405 Method Not Allowed"

### **Problem:**
The server doesn't support the HTTP method being used.

### **Solution:**
1. Make sure you're using `http://localhost/` not `http://127.0.0.1:5500/`
2. Ensure Apache is running in XAMPP
3. Check that PHP files are in the correct directory

---

## ❌ Database Connection Issues

### **Problem:**
Database `tourism_db` doesn't exist or connection fails.

### **Solution:**

#### **Option 1: Automatic Setup**
1. Go to: `http://localhost/heritage-explorer/setup-database.php`
2. This will automatically create the database and tables

#### **Option 2: Manual Setup**
1. Open MySQL Workbench
2. Run this command:
   ```sql
   CREATE DATABASE tourism_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```
3. Then run: `http://localhost/heritage-explorer/setup-database.php`

#### **Option 3: Check Connection**
1. Go to: `http://localhost/heritage-explorer/test-connection.php`
2. This will test your database connection and show any issues

---

## 🔧 **Quick Setup Checklist**

### **1. XAMPP Setup**
- [ ] XAMPP installed
- [ ] Apache service started (green in XAMPP Control Panel)
- [ ] MySQL service started (green in XAMPP Control Panel)

### **2. File Placement**
- [ ] All files copied to `C:\xampp\htdocs\heritage-explorer\`
- [ ] Can access `http://localhost/heritage-explorer/`

### **3. Database Setup**
- [ ] Run `http://localhost/heritage-explorer/setup-database.php`
- [ ] Database `tourism_db` created
- [ ] Tables created successfully

### **4. Test Everything**
- [ ] `http://localhost/heritage-explorer/test-connection.php` - Database test
- [ ] `http://localhost/heritage-explorer/website.html` - Main website
- [ ] `http://localhost/heritage-explorer/login.html` - Login page
- [ ] `http://localhost/heritage-explorer/signup.html` - Signup page

---

## 🚨 **Common Mistakes**

### **1. Using Wrong URL**
- ❌ `http://127.0.0.1:5500/` (Live Server)
- ✅ `http://localhost/heritage-explorer/` (XAMPP)

### **2. Services Not Running**
- Check XAMPP Control Panel
- Apache and MySQL should show green "Running" status

### **3. Files in Wrong Location**
- Files must be in `C:\xampp\htdocs\heritage-explorer\`
- Not in your original project folder

### **4. Database Not Created**
- Run the setup script first
- Check MySQL Workbench for `tourism_db`

---

## 📞 **Still Having Issues?**

### **Check These:**

1. **XAMPP Control Panel**:
   - Apache: Running (Port 80)
   - MySQL: Running (Port 3306)

2. **Browser Console**:
   - Press F12 to open Developer Tools
   - Check Console tab for errors
   - Look for helpful error messages

3. **File Structure**:
   ```
   C:\xampp\htdocs\heritage-explorer\
   ├── website.html
   ├── login.html
   ├── signup.html
   ├── auth.php
   ├── config.php
   ├── auth.js
   ├── setup-database.php
   ├── test-connection.php
   └── database.sql
   ```

4. **Test URLs**:
   - Main site: `http://localhost/heritage-explorer/website.html`
   - Database test: `http://localhost/heritage-explorer/test-connection.php`
   - Setup: `http://localhost/heritage-explorer/setup-database.php`

---

## 💡 **Pro Tips**

1. **Always use `localhost`** instead of `127.0.0.1:5500`
2. **Check XAMPP logs** if something doesn't work
3. **Clear browser cache** if you see old errors
4. **Use browser developer tools** to see network requests
5. **Run setup script first** before testing login/signup

---

## 🎯 **Success Indicators**

When everything is working correctly, you should see:

1. ✅ No console errors in browser
2. ✅ Login/Signup buttons appear in website header
3. ✅ Database connection test passes
4. ✅ Can create new user accounts
5. ✅ Can log in with created accounts

---

## 📧 **Default Test Account**

After running the setup script, you can test with:
- **Username**: admin
- **Password**: admin123
- **Email**: <EMAIL>
