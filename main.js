/***** Analytics and Event Tracking *****/
function trackEvent(eventName, eventData) {
    console.log("Tracking Event:", eventName, eventData);
    // In production, you might send this data to an analytics service.
  }

  /***** Mapping and Data *****/
  const detailsPageMapping = {
    "Brihadeeswarar Temple": "index.html",
    "Meenakshi Amman Temple": "meenakshi-amman-temple.html",
    "Mahabalipuram": "mahabalipuram.html",
    "Thanjavur Palace": "thanjavur-palace.html",
    "Ramanathaswamy Temple": "ramanathaswamy-temple.html",
    "Gangaikonda Cholapuram": "gangaikonda-cholapuram.html"
  };

  function navigateToDetails(siteName) {
    const page = detailsPageMapping[siteName];
    if (page) {
      window.location.href = page;
    } else {
      alert("Details page not found for " + siteName);
    }
  }

  const heritageSites = {
    en: [
      {
        name: "Brihadeeswarar Temple",
        location: "Thanjavur",
        image: "https://tse2.mm.bing.net/th?id=OIP.U1BUbQpP1XjOdYLy1rrkmwHaFj&pid=Api&P=0&h=220",
        description: "A UNESCO World Heritage Site and a masterpiece of Chola architecture.",
        coords: { lat: 10.787, lon: 79.137 }
      },
      {
        name: "Meenakshi Amman Temple",
        location: "Madurai",
        image: "https://tse1.mm.bing.net/th?id=OIP.3B2tPbPkrxw-phM-4Q6AqgHaE8&pid=Api&P=0&h=220",
        description: "A historic Hindu temple dedicated to Goddess Meenakshi.",
        coords: { lat: 9.925, lon: 78.1198 }
      },
      {
        name: "Mahabalipuram",
        location: "Chennai",
        image: "https://tse3.mm.bing.net/th?id=OIP.fOvloesqytzzB8BpKC0eAAHaE5&pid=Api&P=0&h=220",
        description: "Famous for its rock-cut temples and sculptures.",
        coords: { lat: 12.620, lon: 80.192 }
      },
      {
        name: "Thanjavur Palace",
        location: "Thanjavur",
        image: "https://tse4.mm.bing.net/th?id=OIP.SfjBkUFRZHrPwcb0pciZawHaE8&pid=Api&P=0&h=220",
        description: "A magnificent palace built during the Nayak period, known for its architecture and art gallery.",
        coords: { lat: 10.787, lon: 79.137 }
      },
      {
        name: "Ramanathaswamy Temple",
        location: "Rameswaram",
        image: "https://tse4.mm.bing.net/th?id=OIP.GdA3HPrA9ByDA7kogoKstAHaE8&pid=Api&P=0&h=220",
        description: "Famous for its long corridors and Dravidian architectural style, dedicated to Lord Shiva.",
        coords: { lat: 9.288, lon: 79.312 }
      },
      {
        name: "Gangaikonda Cholapuram",
        location: "Jayankondam",
        image: "https://tse4.mm.bing.net/th?id=OIP.JmWCuZVHlJQjjR574QohzwHaEK&pid=Api&P=0&h=220",
        description: "An architectural marvel built by the Cholas, featuring intricate carvings and sculptures.",
        coords: { lat: 10.712, lon: 79.360 }
      }
    ],
    ta: [
      {
        name: "பிரகதீஸ்வரர் கோவில்",
        location: "தஞ்சாவூர்",
        image: "https://tse2.mm.bing.net/th?id=OIP.U1BUbQpP1XjOdYLy1rrkmwHaFj&pid=Api&P=0&h=220",
        description: "யுனெஸ்கோ உலக பாரம்பரியக் களம் மற்றும் சோழ معمாரியத்தின் சிகரம்.",
        coords: { lat: 10.787, lon: 79.137 }
      },
      {
        name: "மீனாட்சி அம்மன் கோவில்",
        location: "மதுரை",
        image: "https://tse1.mm.bing.net/th?id=OIP.3B2tPbPkrxw-phM-4Q6AqgHaE8&pid=Api&P=0&h=220",
        description: "மீனாட்சி தேவிக்கு அர்ப்பணிக்கப்பட்ட புகழ்பெற்ற இந்து கோவில்.",
        coords: { lat: 9.925, lon: 78.1198 }
      },
      {
        name: "மாமல்லபுரம்",
        location: "சென்னை",
        image: "https://tse3.mm.bing.net/th?id=OIP.fOvloesqytzzB8BpKC0eAAHaE5&pid=Api&P=0&h=220",
        description: "பாறை செதுக்கப்பட்ட கோவில்கள் மற்றும் சிற்பங்களுக்குப் புகழ் பெற்றது.",
        coords: { lat: 12.620, lon: 80.192 }
      },
      {
        name: "தஞ்சாவூர் அரண்மனை",
        location: "தஞ்சாவூர்",
        image: "https://tse4.mm.bing.net/th?id=OIP.SfjBkUFRZHrPwcb0pciZawHaE8&pid=Api&P=0&h=220",
        description: "நாயக்கர் ஆட்சி காலத்தில் கட்டப்பட்ட, சிற்பக்கலை மற்றும் கலைக்களரியால் புகழ்பெற்ற அரண்மனை.",
        coords: { lat: 10.787, lon: 79.137 }
      },
      {
        name: "ராமநாதசுவாமி கோவில்",
        location: "ராமேஸ்வரம்",
        image: "https://tse4.mm.bing.net/th?id=OIP.GdA3HPrA9ByDA7kogoKstAHaE8&pid=Api&P=0&h=220",
        description: "பெரிய பிரகாரங்கள் மற்றும் திராவிடக் கட்டிடக்கலையை கொண்ட, சிவனுக்கு அர்ப்பணிக்கப்பட்ட கோவில்.",
        coords: { lat: 9.288, lon: 79.312 }
      },
      {
        name: "கங்கைகொண்ட சோழபுரம்",
        location: "ஜெயங்கொண்டம்",
        image: "https://tse4.mm.bing.net/th?id=OIP.JmWCuZVHlJQjjR574QohzwHaEK&pid=Api&P=0&h=220",
        description: "சோழர்களால் கட்டப்பட்ட சிற்பக் கலை மற்றும் சிற்பங்களை கொண்ட சிறப்பு மிக்க கட்டிடம்.",
        coords: { lat: 10.712, lon: 79.360 }
      }
    ]
  };

  let currentLanguage = "en";
  const siteList = document.getElementById("siteList");
  const modal = document.getElementById("siteModal");
  const modalOverlay = document.getElementById("modalOverlay");
  const modalTitle = document.getElementById("modalTitle");
  const modalDescription = document.getElementById("modalDescription");
  const playAudioButton = document.getElementById("playAudio");
  const closeModalButton = document.getElementById("closeModalButton");
  const nearbyOverlay = document.getElementById("nearbyOverlay");
  const closeNearbyModalButton = document.getElementById("closeNearbyModalButton");
  const favoritesOverlay = document.getElementById("favoritesOverlay");
  const closeFavoritesModalButton = document.getElementById("closeFavoritesModalButton");
  const reviewsOverlay = document.getElementById("reviewsOverlay");
  const closeReviewsModalButton = document.getElementById("closeReviewsModalButton");

  /***** Enhanced Chatbot Functionality: Conversation History *****/
  let conversationHistory = [];

  /***** Render Sites and Populate Location Filter *****/
  function renderSites(language, sites = null) {
    siteList.innerHTML = "";
    const sitesToRender = sites || heritageSites[language];
    sitesToRender.forEach((site) => {
      // Show distance if available (for current location filtering)
      const distanceInfo = site.distance ? <p>Distance: ${site.distance.toFixed(2)} km</p> : "";
      const card = document.createElement("article");
      card.classList.add("site-card");
      card.innerHTML = `
        <img src="${site.image}" alt="${site.name} image" loading="lazy">
        <div class="site-card-content">
          <h3>${site.name}</h3>
          <p>${site.description}</p>
          ${distanceInfo}
          <button class="viewButton" data-site="${site.name}">View</button>
          <button class="nearbyButton" data-location="${site.location}" data-site="${site.name}">Nearby Places</button>
          <button class="favoriteButton" data-site="${site.name}">Favorite</button>
          <button class="shareButton" data-site="${site.name}">Share</button>
          <button class="reviewButton" data-site="${site.name}">Review</button>
        </div>
      `;
      siteList.appendChild(card);
    });
    populateLocationFilter();
  }

  // Filter sites based on the search input.
  function searchSites() {
      const searchInput = document.getElementById('searchBar').value.toLowerCase();
      const filteredSites = heritageSites[currentLanguage].filter(site =>
        site.name.toLowerCase().includes(searchInput) ||
        site.location.toLowerCase().includes(searchInput) ||
        site.description.toLowerCase().includes(searchInput)
      );
      renderSites(currentLanguage, filteredSites);
    }

  /***** Haversine Formula for Distance Calculation *****/
  function getDistance(lat1, lon1, lat2, lon2) {
    const R = 6371; // Earth's radius in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = Math.sin(dLat/2) ** 2 +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLon/2) ** 2;
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /***** Current Location to Find Nearby Sites Feature *****/
  function searchNearbySites(userCoords, threshold = 50) {
    const filteredSites = heritageSites[currentLanguage].filter(site => {
      if (site.coords) {
        const distance = getDistance(userCoords.lat, userCoords.lon, site.coords.lat, site.coords.lon);
        site.distance = distance;
        return distance <= threshold;
      }
      return false;
    });
    if (filteredSites.length === 0) {
      siteList.innerHTML = <p style="text-align: center; padding: 1rem;">No nearby heritage sites found.</p>;
    } else {
      // Sort filtered sites by distance (ascending)
      filteredSites.sort((a, b) => a.distance - b.distance);
      renderSites(currentLanguage, filteredSites);
    }
  }
  // Speech Recognition for Search
  const micButton = document.getElementById("micButton");
  const searchBar = document.getElementById("searchBar");
  window.SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
  const recognition = new SpeechRecognition();
  recognition.lang = "en-US";
  recognition.interimResults = false;
  recognition.continuous = false;

  micButton.addEventListener("click", () => {
    recognition.start();
  });
  recognition.addEventListener("result", (event) => {
    const speechToText = event.results[0][0].transcript;
    searchBar.value = speechToText;
    searchSites();
  });
  recognition.addEventListener("error", (event) => {
    console.error("Speech recognition error:", event.error);
  });



  // Event listener for the "Find Nearby Sites" button.
  document.getElementById("currentLocationButton").addEventListener("click", () => {
    const spinner = document.getElementById("locationSpinner");
    spinner.style.display = "inline-block";
    trackEvent("use_current_location", {});
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          spinner.style.display = "none";
          const userCoords = {
            lat: position.coords.latitude,
            lon: position.coords.longitude
          };
          searchNearbySites(userCoords);
        },
        (error) => {
          spinner.style.display = "none";
          console.error("Geolocation error:", error);
          alert("Unable to retrieve your location. Please ensure location services are enabled.");
        }
      );
    } else {
      spinner.style.display = "none";
      alert("Geolocation is not supported by your browser.");
    }
  });

  /***** Populate Location Filter Dropdown *****/
  function populateLocationFilter() {
    const locationFilter = document.getElementById("locationFilter");
    locationFilter.innerHTML = '<option value="">All Locations</option>';
    const locations = new Set();
    heritageSites[currentLanguage].forEach(site => {
      if (site.location) { locations.add(site.location); }
    });
    locations.forEach(loc => {
      const option = document.createElement("option");
      option.value = loc;
      option.textContent = loc;
      locationFilter.appendChild(option);
    });
  }
  document.getElementById("locationFilter").addEventListener("change", function() {
    const selectedLocation = this.value;
    if (selectedLocation === "") {
      renderSites(currentLanguage);
    } else {
      const filteredSites = heritageSites[currentLanguage].filter(site => site.location === selectedLocation);
      renderSites(currentLanguage, filteredSites);
    }
  });

  /***** Modal Toggle and Close Functions *****/
  function modalToggle(modalId, state) {
    const modals = {
      siteModal: [document.getElementById("siteModal"), document.getElementById("modalOverlay")],
      nearbyModal: [document.getElementById("nearbyModal"), document.getElementById("nearbyOverlay")],
      favoritesModal: [document.getElementById("favoritesModal"), document.getElementById("favoritesOverlay")],
      reviewsModal: [document.getElementById("reviewsModal"), document.getElementById("reviewsOverlay")]
    };
    const [modalElement, overlayElement] = modals[modalId];
    modalElement.style.display = state ? "block" : "none";
    overlayElement.style.display = state ? "block" : "none";
  }
  document.getElementById("modalOverlay").addEventListener("click", () => modalToggle("siteModal", false));
  closeModalButton.addEventListener("click", () => modalToggle("siteModal", false));
  document.getElementById("nearbyOverlay").addEventListener("click", () => modalToggle("nearbyModal", false));
  document.getElementById("closeNearbyModalButton").addEventListener("click", () => modalToggle("nearbyModal", false));
  document.getElementById("favoritesOverlay").addEventListener("click", () => modalToggle("favoritesModal", false));
  document.getElementById("closeFavoritesModalButton")?.addEventListener("click", () => modalToggle("favoritesModal", false));
  document.getElementById("reviewsOverlay").addEventListener("click", () => modalToggle("reviewsModal", false));
  document.getElementById("closeReviewsModalButton")?.addEventListener("click", () => modalToggle("reviewsModal", false));

  /***** Audio Playback for Site Description *****/
  playAudioButton.addEventListener("click", () => {
    const utterance = new SpeechSynthesisUtterance(modalDescription.textContent);
    utterance.lang = currentLanguage === "en" ? "en-US" : "ta-IN";
    speechSynthesis.speak(utterance);
  });

  /***** Language Toggle *****/
  document.getElementById("toggleLang").addEventListener("click", () => {
    console.log("Language toggle button clicked");
    currentLanguage = currentLanguage === "en" ? "ta" : "en";
    console.log("Current language:", currentLanguage);
    trackEvent("toggle_language", { language: currentLanguage });
    document.getElementById("mainHeading").textContent =
      currentLanguage === "en" ? "Heritage Explorer" : "பாரம்பரிய கையேடு";
    document.getElementById("subHeading").textContent =
      currentLanguage === "en"
        ? "Discover Tamil Nadu's rich heritage and culture"
        : "தமிழகத்தின் பாரம்பரியத்தை மற்றும் பண்பாட்டை ஆராயுங்கள்";
    document.getElementById("footerText").textContent =
      currentLanguage === "en"
        ? "© 2025 Heritage Explorer | Explore Tamil Nadu"
        : "© 2025 பாரம்பரிய கையேடு | தமிழகத்தை ஆராயுங்கள்";
    document.getElementById("toggleLang").textContent =
      currentLanguage === "en" ? "தமிழ்" : "English";
    renderSites(currentLanguage);
  });

  /***** Favorites Feature *****/
  function toggleFavorite(siteName) {
    let favorites = JSON.parse(localStorage.getItem("favoriteSites")) || [];
    if (favorites.includes(siteName)) {
      favorites = favorites.filter(name => name !== siteName);
      alert(`${siteName} removed from favorites`);
    } else {
      favorites.push(siteName);
      alert(`${siteName} added to favorites`);
    }
    localStorage.setItem("favoriteSites", JSON.stringify(favorites));
  }
  function showFavoritesModal() {
    const favoritesListDiv = document.getElementById("favoritesList");
    favoritesListDiv.innerHTML = "";
    let favorites = JSON.parse(localStorage.getItem("favoriteSites")) || [];
    if (favorites.length === 0) {
      favoritesListDiv.innerHTML = "<p>No favorites added yet.</p>";
    } else {
      favorites.forEach(siteName => {
        const p = document.createElement("p");
        p.classList.add("favoritesItem");
        p.textContent = siteName;
        const viewBtn = document.createElement("button");
        viewBtn.textContent = "View";
        viewBtn.addEventListener("click", () => {
          navigateToDetails(siteName);
          modalToggle("favoritesModal", false);
        });
        p.appendChild(viewBtn);
        favoritesListDiv.appendChild(p);
      });
    }
    modalToggle("favoritesModal", true);
  }
  document.getElementById("favoritesButton").addEventListener("click", showFavoritesModal);

  /***** Reviews Feature *****/
  function openReviewsModal(siteName) {
    document.getElementById("reviewsSiteName").textContent = siteName;
    loadReviews(siteName);
    modalToggle("reviewsModal", true);
    window.currentReviewSite = siteName;
  }
  function loadReviews(siteName) {
    const reviewsListDiv = document.getElementById("reviewsList");
    reviewsListDiv.innerHTML = "";
    let reviews = JSON.parse(localStorage.getItem("reviews")) || {};
    let siteReviews = reviews[siteName] || [];
    if (siteReviews.length === 0) {
      reviewsListDiv.innerHTML = "<p>No reviews yet. Be the first to review this site!</p>";
    } else {
      siteReviews.forEach(review => {
        const p = document.createElement("p");
        p.classList.add("reviewsItem");
        p.textContent = review;
        reviewsListDiv.appendChild(p);
      });
    }
  }
  function submitReview() {
    const reviewText = document.getElementById("newReview").value.trim();
    if (!reviewText) {
      alert("Please write a review before submitting.");
      return;
    }
    let reviews = JSON.parse(localStorage.getItem("reviews")) || {};
    let site = window.currentReviewSite;
    if (!reviews[site]) {
      reviews[site] = [];
    }
    reviews[site].push(reviewText);
    localStorage.setItem("reviews", JSON.stringify(reviews));
    document.getElementById("newReview").value = "";
    loadReviews(site);
    alert("Review submitted!");
  }
  document.getElementById("submitReviewButton").addEventListener("click", submitReview);

  /***** Chatbot Setup with Enhanced Functionality *****/
  const chatbotToggle = document.getElementById('chatbotToggle');
  const chatbotContainer = document.getElementById('chatbotContainer');
  const chatbotMessages = document.getElementById('chatbotMessages');
  const chatbotInput = document.getElementById('chatbotInput');
  const chatbotSendButton = document.getElementById('chatbotSendButton');

  chatbotToggle.addEventListener('click', () => {
    chatbotContainer.style.display = chatbotContainer.style.display === 'flex' ? 'none' : 'flex';
    trackEvent("toggle_chatbot", { state: chatbotContainer.style.display });
  });

  function sendMessage() {
    const userMessage = chatbotInput.value.trim();
    if (!userMessage) return;
    trackEvent("chatbot_user_message", { message: userMessage });

    // Add user message to chat
    const userMessageElement = document.createElement('div');
    userMessageElement.classList.add('user');
    userMessageElement.textContent = userMessage;
    chatbotMessages.appendChild(userMessageElement);
    chatbotInput.value = '';
    chatbotMessages.scrollTop = chatbotMessages.scrollHeight;

    // Add loading indicator
    const loadingElement = document.createElement('div');
    loadingElement.classList.add('bot', 'loading');
    loadingElement.textContent = "Thinking...";
    chatbotMessages.appendChild(loadingElement);
    chatbotMessages.scrollTop = chatbotMessages.scrollHeight;

    setTimeout(() => {
      // Remove loading indicator
      chatbotMessages.removeChild(loadingElement);

      // Get and display bot response
      const botResponse = getChatbotResponse(userMessage);
      const botMessageElement = document.createElement('div');
      botMessageElement.classList.add('bot');
      botMessageElement.textContent = botResponse;
      chatbotMessages.appendChild(botMessageElement);
      chatbotMessages.scrollTop = chatbotMessages.scrollHeight;
      trackEvent("chatbot_bot_response", { message: botResponse });
    }, 800);
  }

  function getChatbotResponse(userMessage) {
    conversationHistory.push({ role: "user", message: userMessage });
    let botResponse = "";
    const userMessageLower = userMessage.toLowerCase();

    console.log("Processing user message:", userMessage);

    // Check if chatbot training data is loaded
    if (!window.chatbotTrainingData) {
      console.error("Chatbot training data not loaded!");
      botResponse = "I'm sorry, my knowledge base hasn't loaded properly. Please refresh the page and try again.";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    console.log("Chatbot training data loaded:", window.chatbotTrainingData ? "Yes" : "No");

    // DIRECT CATEGORY QUESTIONS - Check these first before anything else
    // This is a more direct approach to handle specific category questions
    if (userMessageLower.includes("what") && userMessageLower.includes("hill stations")) {
      console.log("Direct match for hill stations question");
      botResponse = window.chatbotTrainingData.category_responses.hill_stations.answer;
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    // Special handling for beaches - return top 10 beaches when user asks about beaches
    if (userMessageLower.includes("beaches") || userMessageLower.includes("beach")) {
      console.log("Match for beaches keyword");
      botResponse = "Here are the top 10 beaches in Tamil Nadu:\n\n" +
                   "1. Marina Beach (Chennai) - The second-longest urban beach in the world\n" +
                   "2. Elliots Beach (Chennai) - Also known as Besant Nagar Beach\n" +
                   "3. Covelong Beach (Chennai) - Popular for water sports\n" +
                   "4. Silver Beach (Cuddalore) - Known for its silver-like sand\n" +
                   "5. Mahabalipuram Beach - Near the famous Shore Temple\n" +
                   "6. Kanyakumari Beach - Where three oceans meet\n" +
                   "7. Rameshwaram Beach - A sacred beach with religious significance\n" +
                   "8. Dhanushkodi Beach - The ghost town beach\n" +
                   "9. Poompuhar Beach - Historical beach with cultural importance\n" +
                   "10. Auroville Beach (Pondicherry) - Serene and less crowded";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    if (userMessageLower.includes("what") && userMessageLower.includes("temples")) {
      console.log("Direct match for temples question");
      botResponse = window.chatbotTrainingData.category_responses.temples.answer;
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    if (userMessageLower.includes("what") && userMessageLower.includes("monuments")) {
      console.log("Direct match for monuments question");
      botResponse = window.chatbotTrainingData.category_responses.monuments.answer;
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    if (userMessageLower.includes("what") && userMessageLower.includes("palaces")) {
      console.log("Direct match for palaces question");
      botResponse = window.chatbotTrainingData.category_responses.palaces.answer;
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    if (userMessageLower.includes("what") && userMessageLower.includes("museums")) {
      console.log("Direct match for museums question");
      botResponse = window.chatbotTrainingData.category_responses.museums.answer;
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    // DIRECT HARDCODED RESPONSES FOR COMMON QUESTIONS

    // About Tamil Nadu
    if (userMessageLower.includes("tamil nadu famous") || userMessageLower.includes("known for")) {
      botResponse = "Tamil Nadu is famous for its ancient temples, classical dance forms like Bharatanatyam, rich cuisine, silk sarees, and being home to one of the oldest civilizations in the world.";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    if (userMessageLower.includes("best time") || userMessageLower.includes("when to visit")) {
      botResponse = "The best time to visit Tamil Nadu is from October to March when the weather is pleasant. Summer (April to June) can be extremely hot, while the monsoon season (July to September) brings rainfall.";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    if (userMessageLower.includes("must visit") || userMessageLower.includes("top places")) {
      botResponse = "Must-visit places include Chennai, Madurai, Thanjavur, Mahabalipuram, Ooty, Kodaikanal, Rameswaram, and Kanyakumari.";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    // About Heritage Explorer
    if (userMessageLower.includes("what is heritage explorer") || userMessageLower.includes("about heritage explorer")) {
      botResponse = "Heritage Explorer is an application designed to help users discover and explore the rich heritage and cultural sites of Tamil Nadu, providing information, images, and interactive features.";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    if (userMessageLower.includes("find nearby") || userMessageLower.includes("nearby sites")) {
      botResponse = "You can use the 'Find Nearby Sites' button which uses your current location to show heritage sites in your vicinity.";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    if (userMessageLower.includes("save favorite") || userMessageLower.includes("favorite places")) {
      botResponse = "Yes, you can mark places as favorites by clicking the 'Favorite' button, and access them later from the Favorites section.";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    // Travel Tips
    if (userMessageLower.includes("how to travel") || userMessageLower.includes("travel around")) {
      botResponse = "Tamil Nadu has a well-connected network of buses, trains, and taxis. Major cities have airports, and state transport buses connect most towns and villages.";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    if (userMessageLower.includes("local food") || userMessageLower.includes("what to eat")) {
      botResponse = "Try dosa, idli, sambar, chettinad chicken, filter coffee, and traditional sweets like mysore pak and jangiri.";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    if (userMessageLower.includes("what to pack") || userMessageLower.includes("what should i pack")) {
      botResponse = "Pack light cotton clothes, comfortable walking shoes, sunscreen, a hat, and modest attire for temple visits (shoulders and knees should be covered).";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    // CATEGORY RESPONSES

    // Check for specific category questions first
    if (window.chatbotTrainingData && window.chatbotTrainingData.category_responses) {
      console.log("Checking specific category questions...");
      console.log("User message:", userMessageLower);
      console.log("Category responses available:", Object.keys(window.chatbotTrainingData.category_responses));

      // Beaches handling moved to the top of the function

      if ((userMessageLower.includes("what") && userMessageLower.includes("hill stations")) ||
          (userMessageLower.includes("hill stations") && userMessageLower.includes("tamil nadu"))) {
        console.log("Matched hill stations category question");
        botResponse = window.chatbotTrainingData.category_responses.hill_stations.answer;
        conversationHistory.push({ role: "bot", message: botResponse });
        return botResponse;
      }

      if ((userMessageLower.includes("what") && userMessageLower.includes("temples")) ||
          (userMessageLower.includes("temples") && userMessageLower.includes("tamil nadu"))) {
        console.log("Matched temples category question");
        botResponse = window.chatbotTrainingData.category_responses.temples.answer;
        conversationHistory.push({ role: "bot", message: botResponse });
        return botResponse;
      }

      if ((userMessageLower.includes("what") && userMessageLower.includes("monuments")) ||
          (userMessageLower.includes("monuments") && userMessageLower.includes("tamil nadu"))) {
        console.log("Matched monuments category question");
        botResponse = window.chatbotTrainingData.category_responses.monuments.answer;
        conversationHistory.push({ role: "bot", message: botResponse });
        return botResponse;
      }

      if ((userMessageLower.includes("what") && userMessageLower.includes("palaces")) ||
          (userMessageLower.includes("palaces") && userMessageLower.includes("tamil nadu"))) {
        console.log("Matched palaces category question");
        botResponse = window.chatbotTrainingData.category_responses.palaces.answer;
        conversationHistory.push({ role: "bot", message: botResponse });
        return botResponse;
      }

      if ((userMessageLower.includes("what") && userMessageLower.includes("museums")) ||
          (userMessageLower.includes("museums") && userMessageLower.includes("tamil nadu"))) {
        console.log("Matched museums category question");
        botResponse = window.chatbotTrainingData.category_responses.museums.answer;
        conversationHistory.push({ role: "bot", message: botResponse });
        return botResponse;
      }
    }

    // Then check for general category keywords
    // Beaches handling moved to the top of the function

    // Hill Stations
    if (userMessageLower.includes("hill station") || userMessageLower.includes("hills") || userMessageLower.includes("hill stations")) {
      botResponse = "Tamil Nadu is home to several picturesque hill stations that offer a cool retreat from the plains. The major hill stations include Kodaikanal (Princess of Hill Stations), Ooty (Udhagamandalam), Yelagiri Hills, Kolli Hills (with 70 hairpin bends), Coonoor, Valparai, Yercaud, Meghamalai, and Topslip. These hill stations are known for their pleasant climate, scenic beauty, and various attractions like lakes, waterfalls, and trekking trails.";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    // Temples
    if (userMessageLower.includes("temple") || userMessageLower.includes("temples")) {
      botResponse = "Tamil Nadu is known as the 'Land of Temples' with thousands of ancient temples showcasing Dravidian architecture. Some of the most famous temples include Brihadeeswarar Temple (Thanjavur), Meenakshi Amman Temple (Madurai), Ramanathaswamy Temple (Rameswaram), Gangaikonda Cholapuram Temple, Ekambareswarar Temple (Kanchipuram), Nataraja Temple (Chidambaram), Ranganathaswamy Temple (Srirangam), Arunachaleswarar Temple (Thiruvannamalai), Kapaleeshwarar Temple (Chennai), and Kumari Amman Temple (Kanyakumari). Many of these temples are UNESCO World Heritage Sites.";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    // Monuments
    if (userMessageLower.includes("monument") || userMessageLower.includes("monuments")) {
      botResponse = "Tamil Nadu is home to several historical monuments that showcase its rich cultural heritage. Notable monuments include Mahabalipuram (Shore Temple, Five Rathas, Arjuna's Penance), Vivekananda Rock Memorial (Kanyakumari), Gingee Fort (known as the 'Troy of the East'), Fort St. George (Chennai), Rock Fort Temple (Trichy), Danish Fort (Tranquebar), Thirumalai Nayakkar Palace (Madurai), Padmanabhapuram Palace (Kanyakumari district), and various colonial buildings in Chennai. Many of these monuments are protected by the Archaeological Survey of India.";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    // Palaces
    if (userMessageLower.includes("palace") || userMessageLower.includes("palaces")) {
      botResponse = "Tamil Nadu houses several magnificent palaces that reflect its royal heritage. The most notable palaces include Thanjavur Palace (with its Saraswathi Mahal Library), Thirumalai Nayakkar Palace (Madurai), Padmanabhapuram Palace (Kanyakumari district, the largest wooden palace in Asia), Chettinad Palace (Karaikudi), Tamukkam Palace (Madurai), Aranmanai Palace (Pudukkottai), Ramalinga Vilasam (Ramanathapuram), and Manora Fort (Thanjavur). These palaces showcase a blend of various architectural styles including Dravidian, European, and Islamic influences.";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    // Museums
    if (userMessageLower.includes("museum") || userMessageLower.includes("museums")) {
      botResponse = "Tamil Nadu has several museums that preserve and showcase its rich cultural and historical heritage. Notable museums include the Government Museum (Chennai, one of the oldest museums in India), DakshinaChitra Museum (a living museum of art and architecture), Thanjavur Art Gallery (with Chola bronze collections), Fort Museum at Fort St. George (with British colonial artifacts), Railway Museum (Chennai), Madras Museum (with archaeological and numismatic collections), Tamil Nadu Science and Technology Centre (Chennai), and the Indo-Saracenic style Victoria Memorial Hall. These museums offer insights into the state's art, culture, history, and technological advancements.";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    // SPECIFIC PLACES

    // Brihadeeswarar Temple
    if (userMessageLower.includes("brihadeeswarar") || userMessageLower.includes("big temple") || userMessageLower.includes("thanjavur temple")) {
      botResponse = "The Brihadeeswarar Temple, also known as the 'Big Temple,' is a UNESCO World Heritage Site located in Thanjavur, Tamil Nadu. Built during the Chola dynasty in the 11th century by King Raja Raja Chola I, this architectural marvel is a testament to the grandeur and innovation of Dravidian architecture. The temple is renowned for its massive dome, constructed from a single granite block weighing approximately 80 tons. Its towering vimana stands at a remarkable 216 feet, making it one of the tallest structures of its kind.";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    // Meenakshi Amman Temple
    if (userMessageLower.includes("meenakshi") || userMessageLower.includes("madurai temple")) {
      botResponse = "The Meenakshi Amman Temple is a historic Hindu temple located in the city of Madurai, Tamil Nadu. It is dedicated to Goddess Meenakshi, an avatar of the Hindu goddess Parvati, and her consort, Lord Sundareswarar (Shiva). The temple complex covers an area of 14 acres and features 14 magnificent gopurams (gateway towers), the tallest being the southern tower which rises to 170 feet. The temple is renowned for its intricate carvings, with an estimated 33,000 sculptures adorning the complex.";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    // Marina Beach handling moved to the top of the function

    // Kodaikanal
    if (userMessageLower.includes("kodaikanal") || userMessageLower.includes("kodai")) {
      botResponse = "Kodaikanal, often referred to as the 'Princess of Hill Stations,' is a charming hill station nestled in the Palani Hills of Tamil Nadu at an altitude of 2,133 meters (7,000 feet) above sea level. The centerpiece of Kodaikanal is the star-shaped, man-made Kodaikanal Lake, created in 1863, which offers boating facilities and beautiful walking paths along its 5-kilometer perimeter. The town is also known for its rich biodiversity, with numerous endemic plant species found in the Shola forests that surround it.";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    // Mahabalipuram
    if (userMessageLower.includes("mahabalipuram") || userMessageLower.includes("mamallapuram")) {
      botResponse = "Mahabalipuram, also known as Mamallapuram, is a historic coastal town located about 60 kilometers south of Chennai. It was a bustling seaport during the 7th and 8th centuries under the Pallava dynasty and is now a UNESCO World Heritage Site. The town is renowned for its extraordinary rock-cut monuments, monolithic rathas (temple chariots), cave sanctuaries, and giant open-air bas-reliefs that showcase the pinnacle of Pallava art and architecture.";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    // Vivekananda Rock Memorial
    if (userMessageLower.includes("vivekananda") || userMessageLower.includes("vivekananda rock")) {
      botResponse = "The Vivekananda Rock Memorial is a sacred monument built on a small rock island situated about 500 meters off the mainland of Kanyakumari, at the southernmost tip of India. The memorial stands on one of the two rocks that are situated about 500 meters east of the mainland, where the Arabian Sea, the Bay of Bengal, and the Indian Ocean meet. The memorial was built in 1970 in honor of Swami Vivekananda, who is said to have attained enlightenment on this rock in December 1892.";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    // Gingee Fort
    if (userMessageLower.includes("gingee") || userMessageLower.includes("gingee fort")) {
      botResponse = "Gingee Fort, also known as Senji Fort, is a fortified citadel built across three hills in Villupuram district of Tamil Nadu. Often referred to as the 'Troy of the East' by the British, this impregnable fort stands as a testament to the military prowess and architectural brilliance of various dynasties that ruled the region. The fort complex, spread over three hills - Krishnagiri, Rajagiri, and Chandrayandurg - is connected by walls enclosing an area of 7 square kilometers.";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    // Chettinad Palace
    if (userMessageLower.includes("chettinad") || userMessageLower.includes("chettinad palace")) {
      botResponse = "Chettinad Palace, also known as the Chettinad Mansion or Raja's Palace, is an architectural marvel located in Karaikudi, the heart of the Chettinad region in Tamil Nadu. Built in the early 20th century by the wealthy Chettiar merchant community, this palatial residence exemplifies the opulent lifestyle and unique architectural style of the region. The palace is renowned for its blend of various architectural influences, including European, East Asian, and traditional Tamil elements.";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    // DakshinaChitra Museum
    if (userMessageLower.includes("dakshinachitra") || userMessageLower.includes("dakshinachitra museum")) {
      botResponse = "DakshinaChitra Museum is a living museum dedicated to the art, architecture, crafts, and performing arts of South India. Located in Muttukadu, about 25 kilometers south of Chennai, this cultural center was founded by the Madras Craft Foundation in 1996 and designed by architect Laurie Baker. What makes DakshinaChitra unique is its collection of 18 authentic historical houses that have been transplanted from their original locations and reassembled at the museum.";
      conversationHistory.push({ role: "bot", message: botResponse });
      return botResponse;
    }

    // Track the last place mentioned for follow-up questions
    if (!window.lastMentionedPlace) {
      window.lastMentionedPlace = null;
    }

    // Check for follow-up questions about the last mentioned place
    if (window.lastMentionedPlace && window.chatbotTrainingData && window.chatbotTrainingData.places) {
      const place = window.chatbotTrainingData.places.find(p => p.name === window.lastMentionedPlace);

      if (place && place.faqs) {
        // Check for yes/more responses to the "Would you like to know more" question
        if (userMessageLower.includes("yes") ||
            userMessageLower.includes("sure") ||
            userMessageLower.includes("tell me more") ||
            userMessageLower.includes("more information") ||
            userMessageLower.includes("know more")) {

          // Provide more detailed information about the place
          botResponse = `${place.name} features: ${place.features.join(", ")}. ${place.description.substring(200, 500)}... You can ask me specific questions about ${place.name} or ask about other places in Tamil Nadu.`;
          conversationHistory.push({ role: "bot", message: botResponse });
          return botResponse;
        }

        // Check for specific FAQs about the place
        for (const faq of place.faqs) {
          // Extract key terms from the FAQ question
          const questionTerms = faq.question.toLowerCase().split(" ")
            .filter(term => term.length > 3 && !["what", "when", "where", "which", "how", "why", "is", "are", "the", "in", "of", "at", "for", "about"].includes(term));

          // Check if the user's message contains these key terms
          const matchesQuestion = questionTerms.some(term => userMessageLower.includes(term));

          if (matchesQuestion) {
            botResponse = faq.answer;
            conversationHistory.push({ role: "bot", message: botResponse });
            return botResponse;
          }
        }
      }
    }

    // Check for specific place queries first
    if (window.chatbotTrainingData && window.chatbotTrainingData.places && window.placeNameMap) {

      // Check for exact place names or common variations
      for (const [placeName, index] of Object.entries(window.placeNameMap)) {
        if (userMessageLower.includes(placeName)) {
          const place = window.chatbotTrainingData.places[index];
          // Store the place name for follow-up questions
          window.lastMentionedPlace = place.name;
          botResponse = `${place.name} is a ${place.category} located in ${place.location}. ${place.description.substring(0, 200)}... Would you like to know more about ${place.name}?`;
          conversationHistory.push({ role: "bot", message: botResponse });
          return botResponse;
        }
      }

      // Check for specific place name variations
      const specificPlaceVariations = {
        "meenakshi": "Meenakshi Amman Temple",
        "meenakshi amman": "Meenakshi Amman Temple",
        "madurai temple": "Meenakshi Amman Temple",
        "brihadeeswara": "Brihadeeswarar Temple",
        "thanjavur temple": "Brihadeeswarar Temple",
        "big temple": "Brihadeeswarar Temple",
        "ooty": "Ooty Hill Station",
        "nilgiri": "Ooty Hill Station",
        "kodai": "Kodaikanal",
        "kodaikanal": "Kodaikanal",
        "yelagiri": "Yelagiri Hills",
        "kolli": "Kolli Hills",
        "dakshinachitra": "Dakshinachitra Museum",
        "dakshina chitra": "Dakshinachitra Museum",
        // Beach entries removed to use the top 10 beaches list instead
        "vivekananda rock": "Vivekananda Rock Memorial",
        "vivekananda memorial": "Vivekananda Rock Memorial",
        "gingee": "Gingee Fort",
        "senji": "Gingee Fort",
        "thirumalai nayak": "Madurai Palace",
        "thirumalai nayakkar": "Madurai Palace",
        "madurai palace": "Madurai Palace",
        "thanjavur palace": "Thanjavur Palace",
        "tanjore palace": "Thanjavur Palace",
        "chettinad": "Chettinad Palace",
        "karaikudi palace": "Chettinad Palace"
      };

      for (const [variation, placeName] of Object.entries(specificPlaceVariations)) {
        if (userMessageLower.includes(variation)) {
          // Find the place in the data
          const place = window.chatbotTrainingData.places.find(p => p.name === placeName);
          if (place) {
            // Store the place name for follow-up questions
            window.lastMentionedPlace = place.name;
            botResponse = `${place.name} is a ${place.category} located in ${place.location}. ${place.description.substring(0, 200)}... Would you like to know more about ${place.name}?`;
            conversationHistory.push({ role: "bot", message: botResponse });
            return botResponse;
          }
        }
      }
    }

    // Check for general questions about Tamil Nadu, Heritage Explorer, or travel tips
    if (window.chatbotTrainingData && window.chatbotTrainingData.general_questions) {
      console.log("Checking general questions...");
      console.log("About Tamil Nadu data:", window.chatbotTrainingData.general_questions.about_tamil_nadu);
      console.log("About Heritage Explorer data:", window.chatbotTrainingData.general_questions.about_heritage_explorer);
      console.log("Travel tips data:", window.chatbotTrainingData.general_questions.travel_tips);

      // Check for questions about Tamil Nadu
      if (userMessageLower.includes("tamil nadu famous") ||
          userMessageLower.includes("known for") ||
          userMessageLower.includes("famous in tamil nadu")) {
        console.log("Matched question about what Tamil Nadu is famous for");
        botResponse = window.chatbotTrainingData.general_questions.about_tamil_nadu[0].answer;
        conversationHistory.push({ role: "bot", message: botResponse });
        return botResponse;
      }

      if (userMessageLower.includes("best time") ||
          userMessageLower.includes("when to visit") ||
          userMessageLower.includes("weather in tamil nadu")) {
        botResponse = window.chatbotTrainingData.general_questions.about_tamil_nadu[1].answer;
        conversationHistory.push({ role: "bot", message: botResponse });
        return botResponse;
      }

      if (userMessageLower.includes("must visit") ||
          userMessageLower.includes("top places") ||
          userMessageLower.includes("best places")) {
        botResponse = window.chatbotTrainingData.general_questions.about_tamil_nadu[2].answer;
        conversationHistory.push({ role: "bot", message: botResponse });
        return botResponse;
      }

      // Check for questions about Heritage Explorer
      if (userMessageLower.includes("what is heritage explorer") ||
          userMessageLower.includes("about heritage explorer") ||
          userMessageLower.includes("heritage explorer app")) {
        botResponse = window.chatbotTrainingData.general_questions.about_heritage_explorer[0].answer;
        conversationHistory.push({ role: "bot", message: botResponse });
        return botResponse;
      }

      if (userMessageLower.includes("find nearby") ||
          userMessageLower.includes("nearby sites") ||
          userMessageLower.includes("sites near me")) {
        botResponse = window.chatbotTrainingData.general_questions.about_heritage_explorer[1].answer;
        conversationHistory.push({ role: "bot", message: botResponse });
        return botResponse;
      }

      if (userMessageLower.includes("save favorite") ||
          userMessageLower.includes("bookmark") ||
          userMessageLower.includes("favorite places")) {
        botResponse = window.chatbotTrainingData.general_questions.about_heritage_explorer[2].answer;
        conversationHistory.push({ role: "bot", message: botResponse });
        return botResponse;
      }

      // Check for travel tips
      if (userMessageLower.includes("how to travel") ||
          userMessageLower.includes("transportation") ||
          userMessageLower.includes("getting around")) {
        botResponse = window.chatbotTrainingData.general_questions.travel_tips[0].answer;
        conversationHistory.push({ role: "bot", message: botResponse });
        return botResponse;
      }

      if (userMessageLower.includes("food") ||
          userMessageLower.includes("local cuisine") ||
          userMessageLower.includes("what to eat")) {
        botResponse = window.chatbotTrainingData.general_questions.travel_tips[1].answer;
        conversationHistory.push({ role: "bot", message: botResponse });
        return botResponse;
      }

      if (userMessageLower.includes("what to pack") ||
          userMessageLower.includes("packing") ||
          userMessageLower.includes("what to wear")) {
        botResponse = window.chatbotTrainingData.general_questions.travel_tips[2].answer;
        conversationHistory.push({ role: "bot", message: botResponse });
        return botResponse;
      }
    }

    // Check for category-based questions
    if (window.chatbotTrainingData && window.chatbotTrainingData.category_responses) {
      // More general category detection (we already checked for specific questions earlier)

      // More general category detection
      const categories = {
        // Beach entries removed to use the top 10 beaches list instead

        "hill": "hill_stations",
        "hills": "hill_stations",
        "hill station": "hill_stations",
        "hill stations": "hill_stations",
        "mountain": "hill_stations",
        "mountains": "hill_stations",

        "temple": "temples",
        "temples": "temples",
        "shrine": "temples",
        "kovil": "temples",

        "monument": "monuments",
        "monuments": "monuments",
        "memorial": "monuments",
        "fort": "monuments",
        "historical site": "monuments",

        "palace": "palaces",
        "palaces": "palaces",
        "royal": "palaces",
        "mansion": "palaces",

        "museum": "museums",
        "museums": "museums",
        "gallery": "museums",
        "exhibition": "museums"
      };

      // Check if any category keywords are in the user message
      // First check for exact matches (for single-word queries like "hills")
      if (categories[userMessageLower.trim()]) {
        const category = categories[userMessageLower.trim()];
        botResponse = window.chatbotTrainingData.category_responses[category].answer;
        conversationHistory.push({ role: "bot", message: botResponse });
        return botResponse;
      }

      // Then check for partial matches within longer queries
      for (const [keyword, category] of Object.entries(categories)) {
        if (userMessageLower.includes(keyword)) {
          botResponse = window.chatbotTrainingData.category_responses[category].answer;
          conversationHistory.push({ role: "bot", message: botResponse });
          return botResponse;
        }
      }
    }

    // Check if the user is asking for recommendations or specific places
    if (userMessageLower.includes('recommend') ||
        userMessageLower.includes('suggest') ||
        userMessageLower.includes('what are some') ||
        userMessageLower.includes('tell me some') ||
        userMessageLower.includes('interested in')) {

      // Check which category they're interested in
      let categoryToRecommend = null;

      if (userMessageLower.includes('temple') || userMessageLower.includes('temples')) {
        categoryToRecommend = 'temples';
      } else if (userMessageLower.includes('hill') || userMessageLower.includes('mountain') ||
                 userMessageLower.includes('hill station') || userMessageLower.includes('hill stations')) {
        categoryToRecommend = 'hill_stations';
      } else if (userMessageLower.includes('monument') || userMessageLower.includes('monuments') ||
                 userMessageLower.includes('memorial')) {
        categoryToRecommend = 'monuments';
      } else if (userMessageLower.includes('palace') || userMessageLower.includes('palaces')) {
        categoryToRecommend = 'palaces';
      } else if (userMessageLower.includes('museum') || userMessageLower.includes('museums')) {
        categoryToRecommend = 'museums';
      }

      // Also check for direct requests to show categories
      if (userMessageLower.includes('show me temples') || userMessageLower.includes('list temples')) {
        categoryToRecommend = 'temples';
      } else if (userMessageLower.includes('show me hill stations') || userMessageLower.includes('list hill stations')) {
        categoryToRecommend = 'hill_stations';
      } else if (userMessageLower.includes('show me monuments') || userMessageLower.includes('list monuments')) {
        categoryToRecommend = 'monuments';
      } else if (userMessageLower.includes('show me palaces') || userMessageLower.includes('list palaces')) {
        categoryToRecommend = 'palaces';
      } else if (userMessageLower.includes('show me museums') || userMessageLower.includes('list museums')) {
        categoryToRecommend = 'museums';
      }

      if (categoryToRecommend && window.chatbotTrainingData && window.chatbotTrainingData.places) {
        // Map category names to match the format in the data
        const categoryMapping = {
          'temples': 'Temple',
          'beaches': 'Beach',
          'hill_stations': 'Hill Station',
          'monuments': 'Monument',
          'palaces': 'Palace',
          'museums': 'Museum'
        };

        // Filter places by category
        const placesInCategory = window.chatbotTrainingData.places.filter(
          place => place.category === categoryMapping[categoryToRecommend]
        );

        if (placesInCategory.length > 0) {
          const placeNames = placesInCategory.map(place => place.name).join(', ');
          botResponse = `Here are some ${categoryToRecommend.replace('_', ' ')} in Tamil Nadu: ${placeNames}. Would you like to know more about any specific place?`;
          conversationHistory.push({ role: "bot", message: botResponse });
          return botResponse;
        }
      }

      // If multiple categories are mentioned or no specific category
      if (userMessageLower.includes('all') ||
          (userMessageLower.includes('temple') && userMessageLower.includes('beach')) ||
          (userMessageLower.includes('temple') && userMessageLower.includes('hill')) ||
          (userMessageLower.includes('temple') && userMessageLower.includes('palace')) ||
          (userMessageLower.includes('beach') && userMessageLower.includes('hill')) ||
          !categoryToRecommend) {

        botResponse = "I can recommend places in Tamil Nadu based on your interests. Here are some popular categories: Temples (like Meenakshi Amman Temple, Brihadeeswarar Temple), Beaches (like Marina Beach, Covelong Beach), Hill Stations (like Ooty, Kodaikanal), Monuments (like Vivekananda Rock Memorial, Gingee Fort), Palaces (like Madurai Palace, Thanjavur Palace), and Museums (like Dakshinachitra Museum, Government Museum Chennai). Which category interests you the most?";
        conversationHistory.push({ role: "bot", message: botResponse });
        return botResponse;
      }
    }

    // Default responses if no specific match is found
    if (currentLanguage === 'en') {
      if (userMessageLower.includes('hello')) {
        botResponse = 'Hello! How can I assist you in exploring Tamil Nadu\'s heritage?';
      } else if (conversationHistory.length > 3) {
        botResponse = "I see you're interested in exploring Tamil Nadu's heritage in detail. Let me provide more personalized recommendations. Are you interested in temples, beaches, hill stations, monuments, museums, or palaces?";
      } else {
        botResponse = "I'm here to help you discover heritage sites. Please ask about a specific site or category like temples, beaches, hill stations, monuments, museums, or palaces!";
      }
    } else {
      if (userMessage.includes('வணக்கம்')) {
        botResponse = 'வணக்கம்! தமிழக பாரம்பரியத்தை ஆராய நான் உங்களுக்கு உதவ தயாராக இருக்கிறேன்.';
      } else if (conversationHistory.length > 3) {
        botResponse = "நீங்கள் தமிழக பாரம்பரியத்தை விரிவாக ஆராய விரும்புகிறீர்கள் என்று தெரிகிறது. மேலும் தனிப்பட்ட பரிந்துரைகளை வழங்குகிறேன்.";
      } else {
        botResponse = "ஒரு குறிப்பிட்ட இடம் அல்லது அம்சத்தை கேளுங்கள், நான் உதவுகிறேன்!";
      }
    }
    conversationHistory.push({ role: "bot", message: botResponse });
    return botResponse;
  }
  chatbotSendButton.addEventListener('click', sendMessage);
  chatbotInput.addEventListener('keydown', (e) => {
    if (e.key === 'Enter') sendMessage();
  });

  // Delegate click events for dynamic site card buttons.
  siteList.addEventListener("click", (e) => {
    if (e.target.classList.contains("viewButton")) {
      const siteName = e.target.getAttribute("data-site");
      trackEvent("click_view_button", { site: siteName });
      navigateToDetails(siteName);
    } else if (e.target.classList.contains("nearbyButton")) {
      const location = e.target.getAttribute("data-location");
      const siteName = e.target.getAttribute("data-site");
      trackEvent("click_nearby_button", { site: siteName, location: location });
      showNearbyPlaces(location, siteName);
    } else if (e.target.classList.contains("favoriteButton")) {
      const siteName = e.target.getAttribute("data-site");
      trackEvent("click_favorite_button", { site: siteName });
      toggleFavorite(siteName);
    } else if (e.target.classList.contains("shareButton")) {
      const siteName = e.target.getAttribute("data-site");
      trackEvent("click_share_button", { site: siteName });
      shareSite(siteName);
    } else if (e.target.classList.contains("reviewButton")) {
      const siteName = e.target.getAttribute("data-site");
      trackEvent("click_review_button", { site: siteName });
      openReviewsModal(siteName);
    }
  });

  /***** Share Feature *****/
  function shareSite(siteName) {
    const site = heritageSites[currentLanguage].find(s => s.name === siteName);
    if (!site) return;
    const shareData = {
      title: site.name,
      text: site.description,
      url: window.location.href + "?site=" + encodeURIComponent(site.name)
    };
    if (navigator.share) {
      navigator.share(shareData)
        .then(() => console.log('Site shared successfully'))
        .catch(error => console.error('Error sharing:', error));
    } else {
      navigator.clipboard.writeText(shareData.url)
        .then(() => alert("URL copied to clipboard"))
        .catch(err => console.error("Could not copy text: ", err));
    }
  }

  /***** Back-to-Top Button Functionality *****/
  const backToTop = document.getElementById("backToTop");
  window.addEventListener("scroll", () => {
    if (window.scrollY > 300) {
      backToTop.style.display = "flex";
    } else {
      backToTop.style.display = "none";
    }
  });
  backToTop.addEventListener("click", () => {
    trackEvent("back_to_top_clicked", {});
    window.scrollTo({ top: 0, behavior: "smooth" });
  });

  /***** Load Chatbot Training Data *****/
  async function loadChatbotTrainingData() {
    try {
      const response = await fetch('chatbot-training-data.json');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      window.chatbotTrainingData = data;
      console.log("Chatbot training data loaded successfully");
      console.log("General questions data:", window.chatbotTrainingData.general_questions);
      console.log("Category responses data:", window.chatbotTrainingData.category_responses);
      console.log("Places data count:", window.chatbotTrainingData.places.length);

      // Pre-process place data for faster lookups
      if (window.chatbotTrainingData.places) {
        window.placeNameMap = {};
        window.chatbotTrainingData.places.forEach((place, index) => {
          // Add the main name
          window.placeNameMap[place.name.toLowerCase()] = index;

          // Add common variations
          if (place.name.includes("Temple")) {
            window.placeNameMap[place.name.toLowerCase().replace("temple", "").trim()] = index;
          }
          if (place.name.includes("Beach")) {
            window.placeNameMap[place.name.toLowerCase().replace("beach", "").trim()] = index;
          }
          if (place.name.includes("Palace")) {
            window.placeNameMap[place.name.toLowerCase().replace("palace", "").trim()] = index;
          }
          if (place.name.includes("Museum")) {
            window.placeNameMap[place.name.toLowerCase().replace("museum", "").trim()] = index;
          }
          if (place.name.includes("Hills")) {
            window.placeNameMap[place.name.toLowerCase().replace("hills", "").trim()] = index;
            window.placeNameMap[place.name.toLowerCase().replace("hills", "hill").trim()] = index;
          }
          if (place.name.includes("Hill Station")) {
            window.placeNameMap[place.name.toLowerCase().replace("hill station", "").trim()] = index;
          }
          if (place.name.includes("Memorial")) {
            window.placeNameMap[place.name.toLowerCase().replace("memorial", "").trim()] = index;
          }
          if (place.name.includes("Fort")) {
            window.placeNameMap[place.name.toLowerCase().replace("fort", "").trim()] = index;
          }
        });
      }

      // Add initial greeting message
      const botMessageElement = document.createElement('div');
      botMessageElement.classList.add('bot');
      botMessageElement.textContent = "Hello! How can I assist you in exploring Tamil Nadu's heritage?";
      chatbotMessages.appendChild(botMessageElement);
    } catch (error) {
      console.error("Error loading chatbot training data:", error);
      // Add error message
      const botMessageElement = document.createElement('div');
      botMessageElement.classList.add('bot');
      botMessageElement.textContent = "I'm having trouble loading my knowledge base. Please try again later.";
      chatbotMessages.appendChild(botMessageElement);
    }
  }

  // Load the chatbot data when the page loads
  document.addEventListener('DOMContentLoaded', () => {
    renderSites(currentLanguage);
    loadChatbotTrainingData();
  });