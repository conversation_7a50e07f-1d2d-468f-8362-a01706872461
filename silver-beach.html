<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Silver Beach - Heritage Explorer</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
  <style>
    :root {
      --primary-color: #1a237e;
      --secondary-color: #457b9d;
      --accent-color: #e63946;
      --light-color: #f1faee;
      --dark-color: #1d3557;
      --success-color: #2e7d32;
      --warning-color: #ff9800;
      --danger-color: #c62828;
      --gray-100: #f8f9fa;
      --gray-200: #e9ecef;
      --gray-300: #dee2e6;
      --gray-400: #ced4da;
      --gray-500: #adb5bd;
      --gray-600: #6c757d;
      --gray-700: #495057;
      --gray-800: #343a40;
      --gray-900: #212529;
      --border-radius: 0.5rem;
      --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
      --transition: all 0.3s ease-in-out;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: var(--gray-800);
      background-color: var(--gray-100);
    }

    header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      text-align: center;
    }

    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      background-color: white;
      box-shadow: var(--box-shadow);
    }

    nav a {
      color: var(--gray-700);
      text-decoration: none;
      margin: 0 1rem;
      font-weight: 500;
      transition: var(--transition);
    }

    nav a:hover {
      color: var(--primary-color);
    }

    main {
      max-width: 1200px;
      margin: 2rem auto;
      padding: 0 1rem;
    }

    .site-details {
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    @media (min-width: 768px) {
      .site-details {
        grid-template-columns: 1fr 1fr;
      }
    }

    .site-image {
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
    }

    .site-image img {
      width: 100%;
      height: auto;
      display: block;
    }

    .site-info h1 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .site-info p {
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }

    .site-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .meta-item {
      display: flex;
      align-items: center;
      background-color: var(--gray-200);
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
    }

    .meta-item i {
      margin-right: 0.5rem;
      color: var(--secondary-color);
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      margin-top: 1.5rem;
    }

    .action-buttons button {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      background-color: var(--secondary-color);
      color: white;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
      display: flex;
      align-items: center;
    }

    .action-buttons button i {
      margin-right: 0.5rem;
    }

    .action-buttons button:hover {
      background-color: var(--primary-color);
    }

    .site-description {
      margin-bottom: 2rem;
    }

    .site-description h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .map-container {
      height: 400px;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      margin-bottom: 2rem;
    }

    .nearby-attractions {
      margin-bottom: 2rem;
    }

    .nearby-attractions h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .attractions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .attraction-card {
      background-color: white;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
    }

    .attraction-card:hover {
      transform: translateY(-5px);
    }

    .attraction-card img {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }

    .attraction-card-content {
      padding: 1rem;
    }

    .attraction-card-content h3 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    footer {
      background-color: var(--primary-color);
      color: white;
      text-align: center;
      padding: 1rem;
      margin-top: 2rem;
    }

    #backToTop {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      width: 3rem;
      height: 3rem;
      background-color: var(--primary-color);
      color: white;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
      display: none;
    }

    #backToTop:hover {
      background-color: var(--secondary-color);
    }

    /* Travel Information Section */
    .travel-info {
      margin-bottom: 2rem;
      background-color: white;
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      overflow: hidden;
    }

    .travel-info-header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .travel-info-header h2 {
      margin: 0;
      font-size: 1.5rem;
    }

    .travel-info-tabs {
      display: flex;
      background-color: var(--gray-200);
    }

    .travel-tab {
      padding: 1rem;
      cursor: pointer;
      flex: 1;
      text-align: center;
      font-weight: 500;
      transition: var(--transition);
      border-bottom: 3px solid transparent;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .travel-tab i {
      margin-right: 0.5rem;
    }

    .travel-tab.active {
      background-color: white;
      border-bottom-color: var(--accent-color);
    }

    .travel-tab:hover:not(.active) {
      background-color: var(--gray-300);
    }

    .travel-content {
      padding: 1.5rem;
    }

    .travel-panel {
      display: none;
    }

    .travel-panel.active {
      display: block;
    }

    /* Weather Panel */
    .weather-info {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      align-items: center;
    }

    .weather-current {
      display: flex;
      align-items: center;
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      flex: 1;
      min-width: 250px;
    }

    .weather-icon {
      font-size: 3rem;
      margin-right: 1rem;
      color: var(--secondary-color);
    }

    .weather-details h3 {
      margin: 0;
      color: var(--primary-color);
    }

    .weather-temp {
      font-size: 2rem;
      font-weight: bold;
    }

    .weather-desc {
      color: var(--gray-700);
    }

    .weather-forecast {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      flex: 2;
      min-width: 300px;
    }

    .forecast-day {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      text-align: center;
      flex: 1;
      min-width: 100px;
    }

    .forecast-day h4 {
      margin: 0 0 0.5rem 0;
      color: var(--primary-color);
    }

    .forecast-icon {
      font-size: 1.5rem;
      margin: 0.5rem 0;
      color: var(--secondary-color);
    }

    /* Transportation Panel */
    .transport-options {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .transport-option {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
      display: flex;
      flex-direction: column;
    }

    .transport-option h3 {
      display: flex;
      align-items: center;
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .transport-option h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .transport-option p {
      margin-bottom: 0.5rem;
    }

    .transport-option .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    /* Beach Activities Panel */
    .hours-container {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .beach-hours, .activities-info {
      flex: 1;
      min-width: 250px;
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .beach-hours h3, .activities-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .beach-hours h3 i, .activities-info h3 i {
      margin-right: 0.5rem;
    }

    .hours-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .hours-list li {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .hours-list li:last-child {
      border-bottom: none;
    }

    .day {
      font-weight: 500;
    }

    .activity-item {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .activity-item:last-child {
      border-bottom: none;
    }

    .activity-name {
      font-weight: 500;
    }

    .activity-price {
      font-weight: bold;
    }

    /* Amenities Panel */
    .amenities-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .amenity-card {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .amenity-card h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .amenity-card h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .amenity-card p {
      margin-bottom: 0.5rem;
    }

    .amenity-card .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    .amenity-card .rating {
      color: var(--warning-color);
      margin-bottom: 0.5rem;
    }

    .weather-tips h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .local-transport h3, .best-time h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .beach-safety {
      margin-top: 1.5rem;
    }

    .beach-safety h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .safety-tips {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      border-left: 4px solid var(--warning-color);
      margin-bottom: 1rem;
    }

    .safety-tips h4 {
      color: var(--warning-color);
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
    }

    .safety-tips h4 i {
      margin-right: 0.5rem;
    }

    .safety-tips ul {
      padding-left: 1.5rem;
      margin-bottom: 0;
    }

    .safety-tips li {
      margin-bottom: 0.25rem;
    }

    .fishing-info {
      margin-top: 1.5rem;
    }

    .fishing-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .fishing-season {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      margin-bottom: 1rem;
    }

    .fishing-season h4 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
    }

    .fishing-season h4 i {
      margin-right: 0.5rem;
    }

    .tide-info {
      margin-top: 1.5rem;
    }

    .tide-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .tide-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 1rem;
    }

    .tide-table th, .tide-table td {
      padding: 0.75rem;
      text-align: left;
      border-bottom: 1px solid var(--gray-300);
    }

    .tide-table th {
      background-color: var(--gray-200);
      font-weight: 600;
      color: var(--primary-color);
    }

    .tide-table tr:last-child td {
      border-bottom: none;
    }
  </style>
</head>
<body>
  <header>
    <h1>Heritage Explorer</h1>
    <p>Discover Tamil Nadu's rich heritage and culture</p>
  </header>

  <nav>
    <div>
      <a href="website.html"><i class="fas fa-home"></i> Home</a>
      <a href="#"><i class="fas fa-map-marked-alt"></i> Explore</a>
      <a href="#"><i class="fas fa-info-circle"></i> About</a>
    </div>
    <div>
      <a href="#" id="toggleLang">தமிழ்</a>
    </div>
  </nav>

  <main>
    <div class="site-details">
      <div class="site-image">
        <img src="https://media-cdn.tripadvisor.com/media/photo-s/0a/6c/55/6f/sunrise-at-silver-beach.jpg" alt="Silver Beach">
      </div>
      <div class="site-info">
        <h1>Silver Beach</h1>
        <div class="site-meta">
          <div class="meta-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>Cuddalore, Tamil Nadu</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-umbrella-beach"></i>
            <span>Beach</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-water"></i>
            <span>Bay of Bengal</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-lightbulb"></i>
            <span>Historic Lighthouse</span>
          </div>
        </div>
        <p>Silver Beach is one of the cleanest and most pristine beaches in India, located in Cuddalore, Tamil Nadu. Named for its silver-like sand, this 2-kilometer stretch offers beautiful views of the Bay of Bengal, an old lighthouse, and a serene environment perfect for relaxation and water activities.</p>
        <div class="action-buttons">
          <button id="playAudioButton"><i class="fas fa-volume-up"></i> Listen</button>
          <button id="favoriteButton"><i class="far fa-heart"></i> Favorite</button>
          <button id="shareButton"><i class="fas fa-share-alt"></i> Share</button>
        </div>
      </div>
    </div>

    <div class="site-description">
      <h2>About Silver Beach</h2>
      <p>Silver Beach, located in the coastal town of Cuddalore in Tamil Nadu, is one of the most pristine and unspoiled beaches along the eastern coast of India. Stretching for about 2 kilometers along the Bay of Bengal, the beach derives its name from the silver-like sheen of its fine sand, which glistens under the sunlight, creating a mesmerizing visual effect. Unlike many popular beaches that have become commercialized and crowded, Silver Beach has managed to retain its natural beauty and tranquility, making it a perfect retreat for those seeking a peaceful seaside experience.</p>
      <p>One of the most distinctive landmarks at Silver Beach is the historic lighthouse, which stands as a sentinel overlooking the vast expanse of the Bay of Bengal. Built during the British colonial period, the lighthouse not only serves as a navigational aid for ships but also offers panoramic views of the coastline and the surrounding landscape to visitors who climb to its top. The beach is also known for its gentle waves and relatively safe swimming conditions, although visitors are advised to be cautious and respect the sea's power.</p>
      <p>The natural setting of Silver Beach is enhanced by the Gadilam River, which meets the Bay of Bengal near the beach, creating a picturesque estuary. This confluence of fresh and saltwater creates a unique ecosystem that supports a variety of flora and fauna. The beach is lined with casuarina groves that provide shade and add to the scenic beauty of the area. During sunrise and sunset, the beach transforms into a canvas of vibrant colors, offering spectacular views that attract photographers and nature enthusiasts.</p>
      <p>Despite its beauty and potential for tourism, Silver Beach remains relatively undeveloped compared to other coastal destinations in Tamil Nadu. This has helped preserve its natural charm and ecological balance. The local authorities have made efforts to maintain the cleanliness of the beach, making it one of the cleanest beaches in India. Visitors to Silver Beach can enjoy a range of activities, from swimming and sunbathing to beach volleyball and cricket. The beach also hosts the annual Cuddalore Fishing Festival, which showcases the rich maritime heritage and fishing traditions of the region. For those looking to escape the hustle and bustle of city life and connect with nature, Silver Beach offers a serene and rejuvenating experience.</p>
    </div>

    <div class="map-container">
      <!-- Map will be embedded here -->
      <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3919.0123456789!2d79.77!3d11.75!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3a549733fd43a7c7%3A0x1f7c5ec0b3fb2d2b!2sSilver%20Beach!5e0!3m2!1sen!2sin!4v1652345678901!5m2!1sen!2sin" width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy" title="Map of Silver Beach"></iframe>
    </div>

    <!-- Travel Information Section -->
    <div class="travel-info">
      <div class="travel-info-header">
        <h2>Practical Travel Information</h2>
        <div class="last-updated">Last updated: <span id="lastUpdated">Today</span></div>
      </div>

      <div class="travel-info-tabs">
        <div class="travel-tab active" data-tab="weather">
          <i class="fas fa-cloud-sun"></i> Weather
        </div>
        <div class="travel-tab" data-tab="transport">
          <i class="fas fa-bus"></i> Transportation
        </div>
        <div class="travel-tab" data-tab="hours">
          <i class="fas fa-umbrella-beach"></i> Beach Activities
        </div>
        <div class="travel-tab" data-tab="amenities">
          <i class="fas fa-concierge-bell"></i> Nearby Amenities
        </div>
      </div>

      <div class="travel-content">
        <!-- Weather Panel -->
        <div class="travel-panel active" id="weatherPanel">
          <div class="weather-info">
            <div class="weather-current">
              <div class="weather-icon">
                <i class="fas fa-sun"></i>
              </div>
              <div class="weather-details">
                <h3>Current Weather</h3>
                <div class="weather-temp">29°C</div>
                <div class="weather-desc">Sunny</div>
                <div class="weather-extra">Humidity: 80% | Wind: 12 km/h</div>
              </div>
            </div>

            <div class="weather-forecast">
              <div class="forecast-day">
                <h4>Tomorrow</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun"></i></div>
                <div class="forecast-temp">28°C</div>
                <div class="forecast-desc">Partly Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Wed</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-rain"></i></div>
                <div class="forecast-temp">27°C</div>
                <div class="forecast-desc">Light Rain</div>
              </div>
              <div class="forecast-day">
                <h4>Thu</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun"></i></div>
                <div class="forecast-temp">29°C</div>
                <div class="forecast-desc">Partly Cloudy</div>
              </div>
            </div>
          </div>

          <div class="weather-tips">
            <h3>Weather Tips</h3>
            <p>Silver Beach enjoys a tropical climate with warm temperatures throughout the year. Located on the east coast of Tamil Nadu, the beach experiences three main seasons: summer (March-June), monsoon (July-December), and winter (January-February). The best time to visit is during the winter months when the weather is relatively cooler and more pleasant for beach activities.</p>
            <p>Summer temperatures can reach up to 38°C, making midday beach visits uncomfortable. During this season, it's advisable to visit early morning (6:00 AM - 9:00 AM) or evening (4:00 PM onwards). The northeast monsoon (October-December) brings heavy rainfall to the region, which can limit beach activities. However, the post-monsoon period offers lush green surroundings and a refreshing atmosphere.</p>
            <p>For the most comfortable experience, wear light cotton clothing, sunglasses, and a hat. Always apply sunscreen (SPF 50+) even on cloudy days, as UV exposure can be high. Carry plenty of water to stay hydrated, especially during summer months. The sea breeze can be quite strong in the evenings, so a light jacket might be useful if you're planning to stay after sunset. The beach is known for its beautiful sunrise views, so plan your visit accordingly if you want to capture these moments.</p>
          </div>

          <div class="tide-info">
            <h3>Tide Information</h3>
            <p>Tide conditions significantly impact fishing activities and swimming safety at Silver Beach. Below is the tide schedule for the current week:</p>
            <table class="tide-table">
              <thead>
                <tr>
                  <th>Day</th>
                  <th>High Tide</th>
                  <th>Low Tide</th>
                  <th>Best Time for Activities</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Today</td>
                  <td>5:45 AM, 6:12 PM</td>
                  <td>11:30 AM, 12:15 AM</td>
                  <td>7:00 AM - 10:00 AM</td>
                </tr>
                <tr>
                  <td>Tomorrow</td>
                  <td>6:30 AM, 6:55 PM</td>
                  <td>12:15 PM, 1:00 AM</td>
                  <td>8:00 AM - 11:00 AM</td>
                </tr>
                <tr>
                  <td>Wednesday</td>
                  <td>7:15 AM, 7:40 PM</td>
                  <td>1:00 PM, 1:45 AM</td>
                  <td>9:00 AM - 12:00 PM</td>
                </tr>
                <tr>
                  <td>Thursday</td>
                  <td>8:00 AM, 8:25 PM</td>
                  <td>1:45 PM, 2:30 AM</td>
                  <td>10:00 AM - 1:00 PM</td>
                </tr>
              </tbody>
            </table>
            <p>Note: For swimming, the period between high and low tide (falling tide) often provides the safest conditions. Always check with local fishermen or lifeguards for the most current tide information and safety advisories.</p>
          </div>
        </div>

        <!-- Transportation Panel -->
        <div class="travel-panel" id="transportPanel">
          <div class="transport-options">
            <div class="transport-option">
              <h3><i class="fas fa-plane"></i> By Air</h3>
              <p>Chennai International Airport is the nearest major airport.</p>
              <p class="distance">200 km from Silver Beach (approx. 4-5 hours by car)</p>
              <p>Puducherry Airport is closer but has limited flights.</p>
              <p class="distance">85 km from Silver Beach (approx. 2 hours by car)</p>
              <p>Taxi services available from both airports (₹3,000-5,000 from Chennai, ₹1,500-2,000 from Puducherry).</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-train"></i> By Train</h3>
              <p>Cuddalore Port Junction (CDO) is the nearest railway station.</p>
              <p class="distance">3 km from Silver Beach (approx. 10 minutes by auto-rickshaw)</p>
              <p>Regular trains from Chennai, Trichy, and other major cities.</p>
              <p>Auto-rickshaws and taxis available outside the station (₹50-100).</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-bus"></i> By Bus</h3>
              <p>Regular buses from Chennai, Puducherry, and other major cities.</p>
              <p>TNSTC and private operators run services to Cuddalore.</p>
              <p>Cuddalore Bus Stand is 4 km from Silver Beach.</p>
              <p>Frequency: Every 30 minutes from major cities.</p>
              <p>Fare: ₹150-250 from Chennai, ₹50-80 from Puducherry.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-car"></i> By Car</h3>
              <p>Well-connected via East Coast Road (ECR) from Chennai.</p>
              <p>From Chennai: ECR (200 km, approx. 4-5 hours)</p>
              <p>From Puducherry: ECR (85 km, approx. 2 hours)</p>
              <p>From Chidambaram: NH-32 (45 km, approx. 1 hour)</p>
              <p>Parking available near the beach (₹20-30 for cars).</p>
            </div>
          </div>

          <div class="local-transport">
            <h3>Local Transportation</h3>
            <p>Once you reach Cuddalore, getting to Silver Beach is relatively easy. Auto-rickshaws are readily available for short distances within the area, with fares ranging from ₹30-100 depending on the distance. For more flexibility, consider hiring a local taxi for the day (₹1,000-1,500) if you plan to explore multiple attractions in the region.</p>
            <p>The beach is located about 3 km from Cuddalore Port Junction railway station and 4 km from the main bus stand. Shared auto-rickshaws also operate on fixed routes and can be a cost-effective option (₹10-20 per person). Some local hotels and guesthouses offer bicycle rentals (₹100-150 per day), which can be a pleasant way to explore the beach and surrounding areas, especially during cooler hours of the day.</p>
          </div>
        </div>

        <!-- Beach Activities Panel -->
        <div class="travel-panel" id="hoursPanel">
          <div class="hours-container">
            <div class="beach-hours">
              <h3><i class="far fa-clock"></i> Beach Hours & Safety</h3>
              <ul class="hours-list">
                <li><span class="day">Beach Access:</span> <span class="time">24 hours (unrestricted)</span></li>
                <li><span class="day">Best Visiting Hours:</span> <span class="time">5:30 AM - 8:00 AM, 4:00 PM - 7:00 PM</span></li>
                <li><span class="day">Lifeguard Hours:</span> <span class="time">6:00 AM - 6:00 PM</span></li>
                <li><span class="day">Beach Cleaning:</span> <span class="time">5:00 AM - 7:00 AM</span></li>
                <li><span class="day">Lighthouse Visiting Hours:</span> <span class="time">9:00 AM - 5:00 PM (Closed on Mondays)</span></li>
              </ul>
              <p class="hours-note">Silver Beach is less crowded compared to other popular beaches in Tamil Nadu, offering a more peaceful experience. The beach is generally safe for swimming, but always follow lifeguard instructions and be cautious of changing tide conditions. The historic lighthouse is a popular attraction and offers panoramic views of the coastline. A small entry fee (₹10 for Indians, ₹50 for foreigners) is charged for visiting the lighthouse.</p>
            </div>

            <div class="activities-info">
              <h3><i class="fas fa-swimming-pool"></i> Beach Activities</h3>
              <div class="activity-item">
                <span class="activity-name">Swimming</span>
                <span class="activity-price">Free</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Beach Volleyball</span>
                <span class="activity-price">Free (bring your own equipment)</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Lighthouse Visit</span>
                <span class="activity-price">₹10 - ₹50</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Horse Riding</span>
                <span class="activity-price">₹100-150 per ride</span>
              </div>
              <div class="activity-item">
                <span class="activity-name">Boat Rides</span>
                <span class="activity-price">₹200-300 per person</span>
              </div>
              <p class="activity-note">Silver Beach offers a range of activities for visitors. Swimming is popular, but always be mindful of the sea conditions. The beach is also perfect for morning and evening walks, jogging, and photography. Boat rides are available near the Gadilam River estuary, offering a unique perspective of the coastline. Unlike more commercialized beaches, Silver Beach has fewer vendors and water sports operators, preserving its natural charm and tranquility.</p>
            </div>
          </div>

          <div class="fishing-info">
            <h3>Fishing Activities & Festival</h3>
            <p>Silver Beach is known for its fishing community and traditional fishing activities. Visitors can observe local fishermen at work, especially in the early morning hours when they return with their catch. The annual Cuddalore Fishing Festival, usually held in January-February, showcases the rich maritime heritage and fishing traditions of the region.</p>

            <div class="fishing-season">
              <h4><i class="fas fa-fish"></i> Fishing Seasons</h4>
              <p>The fishing activity at Silver Beach follows seasonal patterns:</p>
              <ul>
                <li><strong>Peak Season (October-February):</strong> After the monsoon, the sea is rich with various species of fish. This is the best time to observe traditional fishing methods and see a variety of catches.</li>
                <li><strong>Moderate Season (March-May):</strong> Fishing continues but with reduced intensity due to changing sea conditions and fish migration patterns.</li>
                <li><strong>Off Season (June-September):</strong> Fishing activity is limited during the monsoon months due to rough sea conditions and fishing restrictions.</li>
              </ul>
              <p>Visitors interested in fishing can arrange for fishing trips with local fishermen (₹500-1,000 per person). These trips offer a unique cultural experience and insight into traditional fishing methods. Some local restaurants also offer "catch and cook" experiences where you can have your catch prepared for a meal.</p>
            </div>
          </div>
        </div>

        <!-- Amenities Panel -->
        <div class="travel-panel" id="amenitiesPanel">
          <div class="amenities-grid">
            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Hotel Anandham</strong></p>
              <p class="distance">2 km from Silver Beach</p>
              <p class="rating">★★★☆☆ (3.5/5)</p>
              <p>Mid-range hotel with basic amenities.</p>
              <p>Price range: ₹1,500 - ₹2,500 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Silver Beach Resort</strong></p>
              <p class="distance">500m from beach</p>
              <p class="rating">★★★★☆ (4.0/5)</p>
              <p>Beach-facing resort with comfortable rooms.</p>
              <p>Price range: ₹2,500 - ₹4,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>TTDC Hotel Tamil Nadu</strong></p>
              <p class="distance">3 km from beach</p>
              <p class="rating">★★★☆☆ (3.2/5)</p>
              <p>Government-run hotel with reliable service.</p>
              <p>Price range: ₹1,200 - ₹2,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Ananda Bhavan</strong></p>
              <p class="distance">2 km from beach</p>
              <p class="rating">★★★★☆ (4.1/5)</p>
              <p>Vegetarian restaurant serving South Indian cuisine.</p>
              <p>Price range: ₹200 - ₹400 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Sea Shell Restaurant</strong></p>
              <p class="distance">1 km from beach</p>
              <p class="rating">★★★★☆ (4.3/5)</p>
              <p>Seafood restaurant with fresh catch from local fishermen.</p>
              <p>Price range: ₹500 - ₹800 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Beach Side Cafe</strong></p>
              <p class="distance">At the beach</p>
              <p class="rating">★★★☆☆ (3.5/5)</p>
              <p>Small cafe serving snacks, beverages, and light meals.</p>
              <p>Price range: ₹150 - ₹300 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-shopping-bag"></i> Shopping</h3>
              <p><strong>Cuddalore Market</strong></p>
              <p class="distance">4 km from beach</p>
              <p>Local market with fresh produce, spices, and handicrafts.</p>
              <p>Open from 7:00 AM to 9:00 PM daily.</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-first-aid"></i> Medical Facilities</h3>
              <p><strong>Government General Hospital</strong></p>
              <p class="distance">5 km from beach</p>
              <p>Basic medical facilities for emergencies.</p>
              <p>Contact: +91 4142 290 000</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-toilet"></i> Public Facilities</h3>
              <p><strong>Beach Restrooms</strong></p>
              <p class="distance">At the beach entrance</p>
              <p>Basic public toilets available (₹5 per use).</p>
              <p>Limited changing facilities available.</p>
            </div>
          </div>

          <div class="best-time">
            <h3>Best Time to Visit</h3>
            <p>The ideal time to visit Silver Beach is from October to February when the weather is pleasant with temperatures ranging from 22°C to 30°C. This period offers the most comfortable conditions for beach activities and exploring the surrounding areas. The winter months (December-January) are particularly pleasant with cool sea breezes making evening walks along the beach especially enjoyable.</p>
            <p>Sunrise at Silver Beach (around 5:30 AM - 6:30 AM depending on the season) offers a spectacular view and is a must-see for photography enthusiasts. The beach is also beautiful during sunset, with the best time being between 5:30 PM and 6:30 PM. Weekdays are generally less crowded compared to weekends, offering a more peaceful experience.</p>
            <p>If you're interested in experiencing the local fishing culture, early mornings (5:00 AM - 7:00 AM) are the best time to see fishermen returning with their catch. The annual Cuddalore Fishing Festival, usually held in January-February, is a great time to experience the local culture and traditions. However, it's advisable to book accommodations in advance during this period as they tend to fill up quickly.</p>
          </div>
        </div>
      </div>
    </div>

    <div class="nearby-attractions">
      <h2>Nearby Attractions</h2>
      <div class="attractions-grid">
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Fort_St_David_20190212182003.jpg" alt="Fort St. David">
          <div class="attraction-card-content">
            <h3>Fort St. David</h3>
            <p>A historic British fort built in the 17th century, featuring colonial architecture and offering insights into the region's colonial past.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Devanathaswamy_Temple_20190212182004.jpg" alt="Devanathaswamy Temple">
          <div class="attraction-card-content">
            <h3>Devanathaswamy Temple</h3>
            <p>An ancient temple dedicated to Lord Vishnu, featuring Dravidian architecture, intricate carvings, and a sacred tank with a floating stone.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Pichavaram_Mangrove_Forest_20190212182005.jpg" alt="Pichavaram Mangrove Forest">
          <div class="attraction-card-content">
            <h3>Pichavaram Mangrove Forest</h3>
            <p>One of the world's largest mangrove forests, offering boat rides through a network of water channels and home to diverse flora and fauna.</p>
          </div>
        </div>
      </div>
    </div>
  </main>

  <footer>
    <p>&copy; 2025 Heritage Explorer | Explore Tamil Nadu</p>
  </footer>

  <div id="backToTop">
    <i class="fas fa-arrow-up"></i>
  </div>

  <script>
    // Back to Top Button
    const backToTop = document.getElementById("backToTop");
    window.addEventListener("scroll", () => {
      if (window.scrollY > 300) {
        backToTop.style.display = "flex";
      } else {
        backToTop.style.display = "none";
      }
    });
    backToTop.addEventListener("click", () => {
      window.scrollTo({ top: 0, behavior: "smooth" });
    });

    // Audio Playback
    document.getElementById("playAudioButton").addEventListener("click", () => {
      const description = document.querySelector(".site-info p").textContent;
      const utterance = new SpeechSynthesisUtterance(description);
      utterance.lang = "en-US";
      speechSynthesis.speak(utterance);
    });

    // Favorite Button
    document.getElementById("favoriteButton").addEventListener("click", function() {
      const icon = this.querySelector("i");
      if (icon.classList.contains("far")) {
        icon.classList.remove("far");
        icon.classList.add("fas");
        alert("Added to favorites!");
      } else {
        icon.classList.remove("fas");
        icon.classList.add("far");
        alert("Removed from favorites!");
      }
    });

    // Share Button
    document.getElementById("shareButton").addEventListener("click", () => {
      const shareData = {
        title: "Silver Beach - Heritage Explorer",
        text: "Check out this beautiful beach in Tamil Nadu!",
        url: window.location.href
      };

      if (navigator.share) {
        navigator.share(shareData)
          .then(() => console.log("Shared successfully"))
          .catch((error) => console.error("Error sharing:", error));
      } else {
        navigator.clipboard.writeText(shareData.url)
          .then(() => alert("URL copied to clipboard"))
          .catch((err) => console.error("Could not copy text: ", err));
      }
    });

    // Initialize the travel info tabs
    document.addEventListener("DOMContentLoaded", function() {
      // Set the last updated date
      const today = new Date();
      document.getElementById("lastUpdated").textContent = today.toLocaleDateString();

      // Tab functionality
      const tabs = document.querySelectorAll(".travel-tab");
      tabs.forEach(tab => {
        tab.addEventListener("click", function() {
          // Remove active class from all tabs
          tabs.forEach(t => t.classList.remove("active"));

          // Add active class to clicked tab
          this.classList.add("active");

          // Hide all panels
          const panels = document.querySelectorAll(".travel-panel");
          panels.forEach(panel => panel.classList.remove("active"));

          // Show the corresponding panel
          const tabId = this.getAttribute("data-tab");
          document.getElementById(tabId + "Panel").classList.add("active");
        });
      });

      // Fetch weather data (mock)
      fetchWeatherData();
    });

    // Weather API Integration (Mock)
    function fetchWeatherData() {
      // Coordinates for Silver Beach
      const lat = 11.7500;
      const lon = 79.7700;

      // Mock weather data
      const mockWeatherData = {
        current: {
          temp: 29,
          humidity: 80,
          wind_speed: 12,
          weather: [{ main: "Clear", description: "Sunny", icon: "01d" }]
        },
        daily: [
          {
            dt: Date.now() + 86400000, // Tomorrow
            temp: { day: 28 },
            weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
          },
          {
            dt: Date.now() + 172800000, // Day after tomorrow
            temp: { day: 27 },
            weather: [{ main: "Rain", description: "Light Rain", icon: "10d" }]
          },
          {
            dt: Date.now() + 259200000, // 3 days from now
            temp: { day: 29 },
            weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
          }
        ]
      };

      updateWeatherUI(mockWeatherData);
    }

    function updateWeatherUI(data) {
      // Update current weather
      document.querySelector(".weather-temp").textContent = `${data.current.temp}°C`;
      document.querySelector(".weather-desc").textContent = data.current.weather[0].description;
      document.querySelector(".weather-extra").textContent = `Humidity: ${data.current.humidity}% | Wind: ${data.current.wind_speed} km/h`;

      // Update weather icon
      const weatherIconElement = document.querySelector(".weather-icon i");
      updateWeatherIcon(weatherIconElement, data.current.weather[0].main);

      // Update forecast
      const forecastDays = document.querySelectorAll(".forecast-day");
      data.daily.forEach((day, index) => {
        if (index < forecastDays.length) {
          const dayElement = forecastDays[index];
          const date = new Date(day.dt);
          const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });

          dayElement.querySelector("h4").textContent = index === 0 ? "Tomorrow" : dayName;
          dayElement.querySelector(".forecast-temp").textContent = `${day.temp.day}°C`;
          dayElement.querySelector(".forecast-desc").textContent = day.weather[0].description;

          // Update forecast icon
          const iconElement = dayElement.querySelector(".forecast-icon i");
          updateWeatherIcon(iconElement, day.weather[0].main);
        }
      });
    }

    function updateWeatherIcon(iconElement, weatherType) {
      // Remove all existing weather classes
      iconElement.className = "";

      // Add the appropriate weather icon class
      switch (weatherType) {
        case "Clear":
          iconElement.classList.add("fas", "fa-sun");
          break;
        case "Clouds":
          iconElement.classList.add("fas", "fa-cloud-sun");
          break;
        case "Rain":
        case "Drizzle":
          iconElement.classList.add("fas", "fa-cloud-rain");
          break;
        case "Thunderstorm":
          iconElement.classList.add("fas", "fa-bolt");
          break;
        case "Snow":
          iconElement.classList.add("fas", "fa-snowflake");
          break;
        case "Mist":
        case "Fog":
        case "Haze":
          iconElement.classList.add("fas", "fa-smog");
          break;
        default:
          iconElement.classList.add("fas", "fa-cloud-sun");
      }
    }
  </script>

  <!-- Language Toggle Script -->
  <script src="language-toggle.js"></script>
</body>
</html>
