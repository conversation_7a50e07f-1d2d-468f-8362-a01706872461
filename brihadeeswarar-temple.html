<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON><PERSON><PERSON><PERSON>warar Temple - Heritage Explorer</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
  <style>
    :root {
      --primary-color: #1a237e;
      --secondary-color: #457b9d;
      --accent-color: #e63946;
      --light-color: #f1faee;
      --dark-color: #1d3557;
      --success-color: #2e7d32;
      --warning-color: #ff9800;
      --danger-color: #c62828;
      --gray-100: #f8f9fa;
      --gray-200: #e9ecef;
      --gray-300: #dee2e6;
      --gray-400: #ced4da;
      --gray-500: #adb5bd;
      --gray-600: #6c757d;
      --gray-700: #495057;
      --gray-800: #343a40;
      --gray-900: #212529;
      --border-radius: 0.5rem;
      --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
      --transition: all 0.3s ease-in-out;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: var(--gray-800);
      background-color: var(--gray-100);
    }

    header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      text-align: center;
    }

    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      background-color: white;
      box-shadow: var(--box-shadow);
    }

    nav a {
      color: var(--gray-700);
      text-decoration: none;
      margin: 0 1rem;
      font-weight: 500;
      transition: var(--transition);
    }

    nav a:hover {
      color: var(--primary-color);
    }

    main {
      max-width: 1200px;
      margin: 2rem auto;
      padding: 0 1rem;
    }

    .site-details {
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    @media (min-width: 768px) {
      .site-details {
        grid-template-columns: 1fr 1fr;
      }
    }

    .site-image {
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
    }

    .site-image img {
      width: 100%;
      height: auto;
      display: block;
    }

    .site-info h1 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .site-info p {
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }

    .site-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .meta-item {
      display: flex;
      align-items: center;
      background-color: var(--gray-200);
      padding: 0.5rem 1rem;
      border-radius: var(--border-radius);
    }

    .meta-item i {
      margin-right: 0.5rem;
      color: var(--secondary-color);
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      margin-top: 1.5rem;
    }

    .action-buttons button {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: var(--border-radius);
      background-color: var(--secondary-color);
      color: white;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
      display: flex;
      align-items: center;
    }

    .action-buttons button i {
      margin-right: 0.5rem;
    }

    .action-buttons button:hover {
      background-color: var(--primary-color);
    }

    .site-description {
      margin-bottom: 2rem;
    }

    .site-description h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .map-container {
      height: 400px;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      margin-bottom: 2rem;
    }

    /* Travel Information Section */
    .travel-info {
      margin-bottom: 2rem;
      background-color: white;
      border-radius: var(--border-radius);
      box-shadow: var(--box-shadow);
      overflow: hidden;
    }

    .travel-info-header {
      background-color: var(--primary-color);
      color: white;
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .travel-info-header h2 {
      margin: 0;
      font-size: 1.5rem;
    }

    .travel-info-tabs {
      display: flex;
      background-color: var(--gray-200);
    }

    .travel-tab {
      padding: 1rem;
      cursor: pointer;
      flex: 1;
      text-align: center;
      font-weight: 500;
      transition: var(--transition);
      border-bottom: 3px solid transparent;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .travel-tab i {
      margin-right: 0.5rem;
    }

    .travel-tab.active {
      background-color: white;
      border-bottom-color: var(--accent-color);
    }

    .travel-tab:hover:not(.active) {
      background-color: var(--gray-300);
    }

    .travel-content {
      padding: 1.5rem;
    }

    .travel-panel {
      display: none;
    }

    .travel-panel.active {
      display: block;
    }

    /* Weather Panel */
    .weather-info {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      align-items: center;
    }

    .weather-current {
      display: flex;
      align-items: center;
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      flex: 1;
      min-width: 250px;
    }

    .weather-icon {
      font-size: 3rem;
      margin-right: 1rem;
      color: var(--secondary-color);
    }

    .weather-details h3 {
      margin: 0;
      color: var(--primary-color);
    }

    .weather-temp {
      font-size: 2rem;
      font-weight: bold;
    }

    .weather-desc {
      color: var(--gray-700);
    }

    .weather-forecast {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      flex: 2;
      min-width: 300px;
    }

    .forecast-day {
      background-color: var(--gray-100);
      padding: 1rem;
      border-radius: var(--border-radius);
      text-align: center;
      flex: 1;
      min-width: 100px;
    }

    .forecast-day h4 {
      margin: 0 0 0.5rem 0;
      color: var(--primary-color);
    }

    .forecast-icon {
      font-size: 1.5rem;
      margin: 0.5rem 0;
      color: var(--secondary-color);
    }

    /* Transportation Panel */
    .transport-options {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .transport-option {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
      display: flex;
      flex-direction: column;
    }

    .transport-option h3 {
      display: flex;
      align-items: center;
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .transport-option h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .transport-option p {
      margin-bottom: 0.5rem;
    }

    .transport-option .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    /* Hours & Tickets Panel */
    .hours-container {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .opening-hours, .ticket-info {
      flex: 1;
      min-width: 250px;
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .opening-hours h3, .ticket-info h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .opening-hours h3 i, .ticket-info h3 i {
      margin-right: 0.5rem;
    }

    .hours-list {
      list-style: none;
    }

    .hours-list li {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .hours-list li:last-child {
      border-bottom: none;
    }

    .day {
      font-weight: 500;
    }

    .ticket-type {
      display: flex;
      justify-content: space-between;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--gray-300);
    }

    .ticket-type:last-child {
      border-bottom: none;
    }

    .ticket-price {
      font-weight: bold;
    }

    /* Amenities Panel */
    .amenities-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .amenity-card {
      background-color: var(--gray-100);
      padding: 1.5rem;
      border-radius: var(--border-radius);
    }

    .amenity-card h3 {
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
    }

    .amenity-card h3 i {
      margin-right: 0.5rem;
      font-size: 1.5rem;
    }

    .amenity-card p {
      margin-bottom: 0.5rem;
    }

    .amenity-card .distance {
      font-weight: bold;
      color: var(--gray-700);
    }

    .amenity-card .rating {
      color: var(--warning-color);
      margin-bottom: 0.5rem;
    }

    .nearby-attractions {
      margin-bottom: 2rem;
    }

    .nearby-attractions h2 {
      color: var(--primary-color);
      margin-bottom: 1rem;
    }

    .attractions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 1.5rem;
    }

    .attraction-card {
      background-color: white;
      border-radius: var(--border-radius);
      overflow: hidden;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
    }

    .attraction-card:hover {
      transform: translateY(-5px);
    }

    .attraction-card img {
      width: 100%;
      height: 150px;
      object-fit: cover;
    }

    .attraction-card-content {
      padding: 1rem;
    }

    .attraction-card-content h3 {
      color: var(--primary-color);
      margin-bottom: 0.5rem;
    }

    footer {
      background-color: var(--primary-color);
      color: white;
      text-align: center;
      padding: 1rem;
      margin-top: 2rem;
    }

    #backToTop {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      width: 3rem;
      height: 3rem;
      background-color: var(--primary-color);
      color: white;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      box-shadow: var(--box-shadow);
      transition: var(--transition);
      display: none;
    }

    #backToTop:hover {
      background-color: var(--secondary-color);
    }
  </style>
</head>
<body>
  <header>
    <h1>Heritage Explorer</h1>
    <p>Discover Tamil Nadu's rich heritage and culture</p>
  </header>

  <nav>
    <div>
      <a href="website.html"><i class="fas fa-home"></i> Home</a>
      <a href="#"><i class="fas fa-map-marked-alt"></i> Explore</a>
      <a href="#"><i class="fas fa-info-circle"></i> About</a>
    </div>
    <div>
      <a href="#" id="toggleLang">தமிழ்</a>
    </div>
  </nav>

  <main>
    <div class="site-details">
      <div class="site-image">
        <img src="https://dynamic-media-cdn.tripadvisor.com/media/photo-o/17/6a/e5/a7/thanjavur-brihadeeshwara.jpg?w=1200&h=1200&s=1" alt="Brihadeeswarar Temple">
      </div>
      <div class="site-info">
        <h1>Brihadeeswarar Temple</h1>
        <div class="site-meta">
          <div class="meta-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>Thanjavur, Tamil Nadu</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-gopuram"></i>
            <span>Temple</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-calendar-alt"></i>
            <span>Built in 1010 CE</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-award"></i>
            <span>UNESCO World Heritage Site</span>
          </div>
        </div>
        <p>Brihadeeswarar Temple, also known as Peruvudaiyar Kovil, is a magnificent UNESCO World Heritage Site built by Raja Raja Chola I in the 11th century. This architectural marvel features a 216-foot tall vimana, a massive Nandi statue carved from a single stone, and exquisite frescoes that showcase the pinnacle of Chola art and architecture.</p>
        <div class="action-buttons">
          <button id="playAudioButton"><i class="fas fa-volume-up"></i> Listen</button>
          <button id="favoriteButton"><i class="far fa-heart"></i> Favorite</button>
          <button id="shareButton"><i class="fas fa-share-alt"></i> Share</button>
        </div>
      </div>
    </div>

    <div class="site-description">
      <h2>About Brihadeeswarar Temple</h2>
      <p>Brihadeeswarar Temple, also known as Rajarajesvaram or Peruvudaiyar Kovil, is a magnificent Hindu temple dedicated to Lord Shiva located in Thanjavur, Tamil Nadu. Built by Raja Raja Chola I between 1003 and 1010 CE, the temple is a part of the UNESCO World Heritage Site known as the "Great Living Chola Temples" and stands as a testament to the architectural prowess and artistic vision of the Chola dynasty.</p>
      <p>The most striking feature of the temple is its vimana (tower), which rises to a height of 216 feet (66 meters), making it one of the tallest temple towers in the world. The vimana is topped by a massive stone cupola, estimated to weigh about 80 tons, which was placed at that height using a technique that continues to baffle engineers today. The temple is built entirely of granite, with an estimated 130,000 tons of the stone used in its construction. The fact that there are no granite quarries within a 50-mile radius of Thanjavur adds to the marvel of its construction.</p>
      <p>The temple complex is a treasure trove of art and architecture. The walls of the temple are adorned with exquisite sculptures and frescoes depicting various deities and scenes from Hindu mythology. The inner sanctum houses a massive Lingam (symbolic representation of Lord Shiva), and the circumambulatory path around it features numerous smaller shrines. The Nandi (Shiva's bull mount) pavilion in front of the temple contains a monolithic statue of Nandi, carved from a single stone and measuring 16 feet in length and 13 feet in height.</p>
      <p>Beyond its architectural significance, Brihadeeswarar Temple holds immense cultural and religious importance. It has been a center of religious, cultural, and artistic activities for over a millennium. The temple walls contain inscriptions that provide valuable insights into the administrative, social, and economic systems of the Chola period. Even today, the temple continues to be a living place of worship, with daily rituals and annual festivals attracting thousands of devotees and tourists from around the world.</p>
    </div>

    <div class="map-container">
      <!-- Map will be embedded here -->
      <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3919.0123456789!2d79.13!3d10.782!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3baab89f0e592a7d%3A0x1d3c2b1c83e5dc08!2sBrihadeeswarar%20Temple!5e0!3m2!1sen!2sin!4v1652345678901!5m2!1sen!2sin" width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy" title="Map of Brihadeeswarar Temple"></iframe>
    </div>

    <!-- Travel Information Section -->
    <div class="travel-info">
      <div class="travel-info-header">
        <h2>Practical Travel Information</h2>
        <div class="last-updated">Last updated: <span id="lastUpdated">Today</span></div>
      </div>

      <div class="travel-info-tabs">
        <div class="travel-tab active" data-tab="weather">
          <i class="fas fa-cloud-sun"></i> Weather
        </div>
        <div class="travel-tab" data-tab="transport">
          <i class="fas fa-bus"></i> Transportation
        </div>
        <div class="travel-tab" data-tab="hours">
          <i class="far fa-clock"></i> Hours & Tickets
        </div>
        <div class="travel-tab" data-tab="amenities">
          <i class="fas fa-concierge-bell"></i> Nearby Amenities
        </div>
      </div>

      <div class="travel-content">
        <!-- Weather Panel -->
        <div class="travel-panel active" id="weatherPanel">
          <div class="weather-info">
            <div class="weather-current">
              <div class="weather-icon">
                <i class="fas fa-sun"></i>
              </div>
              <div class="weather-details">
                <h3>Current Weather</h3>
                <div class="weather-temp">32°C</div>
                <div class="weather-desc">Sunny</div>
                <div class="weather-extra">Humidity: 65% | Wind: 12 km/h</div>
              </div>
            </div>

            <div class="weather-forecast">
              <div class="forecast-day">
                <h4>Tomorrow</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun"></i></div>
                <div class="forecast-temp">30°C</div>
                <div class="forecast-desc">Partly Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Wed</h4>
                <div class="forecast-icon"><i class="fas fa-cloud"></i></div>
                <div class="forecast-temp">29°C</div>
                <div class="forecast-desc">Cloudy</div>
              </div>
              <div class="forecast-day">
                <h4>Thu</h4>
                <div class="forecast-icon"><i class="fas fa-cloud-sun-rain"></i></div>
                <div class="forecast-temp">28°C</div>
                <div class="forecast-desc">Light Rain</div>
              </div>
            </div>
          </div>

          <div class="weather-tips">
            <h3>Weather Tips</h3>
            <p>Thanjavur has a tropical climate with hot summers and mild winters. The best time to visit is from October to March when the weather is pleasant with temperatures ranging from 20°C to 30°C. Summer months (April-June) can be extremely hot with temperatures often exceeding 40°C, making it uncomfortable for extended temple visits. The monsoon season (July-September) brings moderate rainfall.</p>
            <p>The temple complex has limited shade, especially in the main courtyard. During summer months, it's advisable to visit early morning (6:00 AM - 8:00 AM) or late afternoon (4:00 PM - 6:00 PM) to avoid the intense heat. Carry water, sunscreen, a hat, and wear light cotton clothing. Comfortable footwear is recommended as you'll need to remove shoes before entering the temple, and the stone floors can get hot during midday. An umbrella can be useful both for sun protection and during unexpected rain showers.</p>
            <p>The temple's massive granite structures retain heat during the day, so the inner areas can feel warmer than the outside temperature. The evening light creates a golden glow on the temple's sandstone surface, making it an ideal time for photography enthusiasts.</p>
          </div>
        </div>

        <!-- Transportation Panel -->
        <div class="travel-panel" id="transportPanel">
          <div class="transport-options">
            <div class="transport-option">
              <h3><i class="fas fa-plane"></i> By Air</h3>
              <p>The nearest airport is Tiruchirappalli International Airport (TRZ).</p>
              <p class="distance">70 km from the temple (approx. 1.5 hours by car)</p>
              <p>Regular flights from Chennai, Bangalore, and international destinations like Singapore and Kuala Lumpur.</p>
              <p>Taxi services available from the airport to Thanjavur.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-train"></i> By Train</h3>
              <p>Thanjavur Junction Railway Station is well-connected.</p>
              <p class="distance">3 km from the temple (10 minutes by auto-rickshaw)</p>
              <p>Regular trains from Chennai, Madurai, Trichy, and other major cities.</p>
              <p>Pre-paid auto services available at the station.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-bus"></i> By Bus</h3>
              <p>Thanjavur has a well-connected bus station with services to major cities.</p>
              <p class="distance">2.5 km from the temple (15 minutes walk or 5 minutes by auto)</p>
              <p>Regular state-run and private buses from Chennai, Trichy, Madurai, and other cities.</p>
              <p>Local buses also available to reach the temple from the bus station.</p>
            </div>

            <div class="transport-option">
              <h3><i class="fas fa-car"></i> By Car</h3>
              <p>Well-connected by highways from major cities.</p>
              <p>From Chennai: NH32 (350 km, approx. 6 hours)</p>
              <p>From Madurai: NH38 (150 km, approx. 3 hours)</p>
              <p>Parking available near the temple complex (fees apply).</p>
            </div>
          </div>

          <div class="local-transport">
            <h3>Local Transportation</h3>
            <p>Within Thanjavur, auto-rickshaws and cycle-rickshaws are readily available for local transportation. Many hotels also offer bicycle rentals, which is a pleasant way to explore the city. App-based taxi services like Ola are also operational in the city.</p>
          </div>
        </div>

        <!-- Hours & Tickets Panel -->
        <div class="travel-panel" id="hoursPanel">
          <div class="hours-container">
            <div class="opening-hours">
              <h3><i class="far fa-clock"></i> Opening Hours</h3>
              <ul class="hours-list">
                <li><span class="day">Monday - Friday:</span> <span class="time">6:00 AM - 12:30 PM, 4:00 PM - 8:30 PM</span></li>
                <li><span class="day">Saturday - Sunday:</span> <span class="time">5:30 AM - 12:30 PM, 4:00 PM - 9:00 PM</span></li>
                <li><span class="day">Festival Days:</span> <span class="time">Extended hours (check locally)</span></li>
              </ul>
              <p class="hours-note">The temple is closed to visitors during special ceremonies and rituals. It's advisable to check locally for any schedule changes.</p>
            </div>

            <div class="ticket-info">
              <h3><i class="fas fa-ticket-alt"></i> Ticket Information</h3>
              <div class="ticket-type">
                <span>Indian Nationals:</span>
                <span class="ticket-price">₹25</span>
              </div>
              <div class="ticket-type">
                <span>Foreign Nationals:</span>
                <span class="ticket-price">₹300</span>
              </div>
              <div class="ticket-type">
                <span>Still Camera Fee:</span>
                <span class="ticket-price">₹50</span>
              </div>
              <div class="ticket-type">
                <span>Video Camera Fee:</span>
                <span class="ticket-price">₹200</span>
              </div>
              <p class="ticket-note">Children under 15 years are free. Tickets can be purchased at the entrance. Credit cards accepted.</p>
            </div>
          </div>

          <div class="best-time">
            <h3>Best Time to Visit</h3>
            <p>The best time to visit is during the early morning (6:00 AM - 8:00 AM) or late afternoon (4:00 PM - 6:00 PM) when the temperature is pleasant and the lighting is ideal for photography. The temple is less crowded on weekdays compared to weekends.</p>
            <p>October to March is the ideal season to visit Thanjavur as the weather is cooler and more comfortable for sightseeing.</p>
          </div>
        </div>

        <!-- Amenities Panel -->
        <div class="travel-panel" id="amenitiesPanel">
          <div class="amenities-grid">
            <div class="amenity-card">
              <h3><i class="fas fa-hotel"></i> Accommodation</h3>
              <p><strong>Ideal Heritage</strong></p>
              <p class="distance">1.2 km from temple</p>
              <p class="rating">★★★★☆ (4.2/5)</p>
              <p>Heritage hotel with traditional Tamil Nadu architecture and modern amenities.</p>
              <p>Price range: ₹3,500 - ₹7,000 per night</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-utensils"></i> Restaurants</h3>
              <p><strong>Tanjore Restaurant</strong></p>
              <p class="distance">0.5 km from temple</p>
              <p class="rating">★★★★★ (4.5/5)</p>
              <p>Authentic South Indian cuisine with specialties from the Chola region.</p>
              <p>Price range: ₹500 - ₹1,000 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-shopping-bag"></i> Shopping</h3>
              <p><strong>Thanjavur Art Gallery & Handicrafts</strong></p>
              <p class="distance">0.8 km from temple</p>
              <p class="rating">★★★★☆ (4.1/5)</p>
              <p>Traditional Thanjavur paintings, bronze sculptures, and handicrafts.</p>
              <p>Price range: ₹500 - ₹50,000 depending on the artwork</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-first-aid"></i> Medical Facilities</h3>
              <p><strong>Thanjavur Medical Center</strong></p>
              <p class="distance">2 km from temple</p>
              <p class="rating">★★★★☆ (4.0/5)</p>
              <p>24/7 emergency services, multi-specialty hospital with English-speaking staff.</p>
              <p>Emergency contact: +91 4362 230101</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-coffee"></i> Cafes</h3>
              <p><strong>Heritage Coffee House</strong></p>
              <p class="distance">0.3 km from temple</p>
              <p class="rating">★★★★☆ (4.3/5)</p>
              <p>Cozy cafe with South Indian filter coffee, snacks, and free Wi-Fi.</p>
              <p>Price range: ₹200 - ₹400 for two people</p>
            </div>

            <div class="amenity-card">
              <h3><i class="fas fa-info-circle"></i> Tourist Information</h3>
              <p><strong>Tamil Nadu Tourism Office</strong></p>
              <p class="distance">1.5 km from temple</p>
              <p>Official tourism information center with maps, guides, and tour booking services.</p>
              <p>Languages: Tamil, English, Hindi</p>
              <p>Contact: +91 4362 230984</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="nearby-attractions">
      <h2>Nearby Attractions</h2>
      <div class="attractions-grid">
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Thanjavur_Palace_20190212182003.jpg" alt="Thanjavur Palace">
          <div class="attraction-card-content">
            <h3>Thanjavur Palace</h3>
            <p>A royal palace complex built by the Nayak rulers and later renovated by the Marathas, housing the Saraswathi Mahal Library and Royal Museum.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Saraswathi_Mahal_Library_20190212182004.jpg" alt="Saraswathi Mahal Library">
          <div class="attraction-card-content">
            <h3>Saraswathi Mahal Library</h3>
            <p>One of the oldest libraries in Asia, housing over 30,000 rare manuscripts and books in various languages including Tamil, Sanskrit, and Marathi.</p>
          </div>
        </div>
        <div class="attraction-card">
          <img src="https://www.holidify.com/images/cmsuploads/compressed/Thanjavur_Art_Gallery_20190212182005.jpg" alt="Thanjavur Art Gallery">
          <div class="attraction-card-content">
            <h3>Thanjavur Art Gallery</h3>
            <p>Houses an exquisite collection of bronze and stone sculptures from the Chola period, along with paintings and artifacts showcasing the region's rich artistic heritage.</p>
          </div>
        </div>
      </div>
    </div>
  </main>

  <footer>
    <p>&copy; 2025 Heritage Explorer | Explore Tamil Nadu</p>
  </footer>

  <div id="backToTop">
    <i class="fas fa-arrow-up"></i>
  </div>

  <script>
    // Back to Top Button
    const backToTop = document.getElementById("backToTop");
    window.addEventListener("scroll", () => {
      if (window.scrollY > 300) {
        backToTop.style.display = "flex";
      } else {
        backToTop.style.display = "none";
      }
    });
    backToTop.addEventListener("click", () => {
      window.scrollTo({ top: 0, behavior: "smooth" });
    });

    // Audio Playback
    document.getElementById("playAudioButton").addEventListener("click", () => {
      const description = document.querySelector(".site-info p").textContent;
      const utterance = new SpeechSynthesisUtterance(description);
      utterance.lang = "en-US";
      speechSynthesis.speak(utterance);
    });

    // Favorite Button
    document.getElementById("favoriteButton").addEventListener("click", function() {
      const icon = this.querySelector("i");
      if (icon.classList.contains("far")) {
        icon.classList.remove("far");
        icon.classList.add("fas");
        alert("Added to favorites!");
      } else {
        icon.classList.remove("fas");
        icon.classList.add("far");
        alert("Removed from favorites!");
      }
    });

    // Share Button
    document.getElementById("shareButton").addEventListener("click", () => {
      const shareData = {
        title: "Brihadeeswarar Temple - Heritage Explorer",
        text: "Check out this magnificent UNESCO World Heritage temple in Tamil Nadu!",
        url: window.location.href
      };

      if (navigator.share) {
        navigator.share(shareData)
          .then(() => console.log("Shared successfully"))
          .catch((error) => console.error("Error sharing:", error));
      } else {
        navigator.clipboard.writeText(shareData.url)
          .then(() => alert("URL copied to clipboard"))
          .catch((err) => console.error("Could not copy text: ", err));
      }
    });

    // Travel Info Tabs
    document.addEventListener("DOMContentLoaded", function() {
      // Set the last updated date
      const today = new Date();
      document.getElementById("lastUpdated").textContent = today.toLocaleDateString();

      // Tab functionality
      const tabs = document.querySelectorAll(".travel-tab");
      tabs.forEach(tab => {
        tab.addEventListener("click", function() {
          // Remove active class from all tabs
          tabs.forEach(t => t.classList.remove("active"));

          // Add active class to clicked tab
          this.classList.add("active");

          // Hide all panels
          const panels = document.querySelectorAll(".travel-panel");
          panels.forEach(panel => panel.classList.remove("active"));

          // Show the corresponding panel
          const tabId = this.getAttribute("data-tab");
          document.getElementById(tabId + "Panel").classList.add("active");
        });
      });

      // Fetch real-time weather data
      fetchWeatherData();
    });

    // Weather API Integration
    function fetchWeatherData() {
      // Coordinates for Thanjavur
      const lat = 10.787;
      const lon = 79.137;

      // OpenWeatherMap API key (replace with your own)
      const apiKey = "YOUR_API_KEY"; // For demo purposes

      // For demo purposes, we'll use mock data instead of making an actual API call
      // In a real implementation, you would use:
      // fetch(`https://api.openweathermap.org/data/2.5/onecall?lat=${lat}&lon=${lon}&exclude=minutely,hourly&units=metric&appid=${apiKey}`)

      // Mock weather data
      const mockWeatherData = {
        current: {
          temp: 32,
          humidity: 65,
          wind_speed: 12,
          weather: [{ main: "Clear", description: "Sunny", icon: "01d" }]
        },
        daily: [
          {
            dt: Date.now() + 86400000, // Tomorrow
            temp: { day: 30 },
            weather: [{ main: "Clouds", description: "Partly Cloudy", icon: "02d" }]
          },
          {
            dt: Date.now() + 172800000, // Day after tomorrow
            temp: { day: 29 },
            weather: [{ main: "Clouds", description: "Cloudy", icon: "03d" }]
          },
          {
            dt: Date.now() + 259200000, // 3 days from now
            temp: { day: 28 },
            weather: [{ main: "Rain", description: "Light Rain", icon: "10d" }]
          }
        ]
      };

      updateWeatherUI(mockWeatherData);
    }

    function updateWeatherUI(data) {
      // Update current weather
      document.querySelector(".weather-temp").textContent = `${data.current.temp}°C`;
      document.querySelector(".weather-desc").textContent = data.current.weather[0].description;
      document.querySelector(".weather-extra").textContent = `Humidity: ${data.current.humidity}% | Wind: ${data.current.wind_speed} km/h`;

      // Update weather icon
      const weatherIconElement = document.querySelector(".weather-icon i");
      updateWeatherIcon(weatherIconElement, data.current.weather[0].main);

      // Update forecast
      const forecastDays = document.querySelectorAll(".forecast-day");
      data.daily.forEach((day, index) => {
        if (index < forecastDays.length) {
          const dayElement = forecastDays[index];
          const date = new Date(day.dt);
          const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });

          dayElement.querySelector("h4").textContent = index === 0 ? "Tomorrow" : dayName;
          dayElement.querySelector(".forecast-temp").textContent = `${day.temp.day}°C`;
          dayElement.querySelector(".forecast-desc").textContent = day.weather[0].description;

          // Update forecast icon
          const iconElement = dayElement.querySelector(".forecast-icon i");
          updateWeatherIcon(iconElement, day.weather[0].main);
        }
      });
    }

    function updateWeatherIcon(iconElement, weatherType) {
      // Remove all existing weather classes
      iconElement.className = "";

      // Add the appropriate weather icon class
      switch (weatherType) {
        case "Clear":
          iconElement.classList.add("fas", "fa-sun");
          break;
        case "Clouds":
          iconElement.classList.add("fas", "fa-cloud");
          break;
        case "Rain":
        case "Drizzle":
          iconElement.classList.add("fas", "fa-cloud-rain");
          break;
        case "Thunderstorm":
          iconElement.classList.add("fas", "fa-bolt");
          break;
        case "Snow":
          iconElement.classList.add("fas", "fa-snowflake");
          break;
        case "Mist":
        case "Fog":
        case "Haze":
          iconElement.classList.add("fas", "fa-smog");
          break;
        default:
          iconElement.classList.add("fas", "fa-cloud-sun");
      }
    }

    // This section was removed to avoid duplicate initialization of travel info tabs
  </script>

  <!-- Audio Guide Script -->
  <script src="audio-player.js"></script>

  <script>
    // Initialize the audio guide with chapters
    const brihadeeswararChapters = [
      { title: "Introduction", startTime: 0 },
      { title: "Historical Background", startTime: 60 },
      { title: "Architecture", startTime: 120 },
      { title: "Sculptures & Carvings", startTime: 180 },
      { title: "Religious Significance", startTime: 240 },
      { title: "Conservation Efforts", startTime: 300 }
    ];

    initAudioGuide('brihadeeswarar-temple', brihadeeswararChapters);
  </script>

  <!-- Language Toggle Script -->
  <script src="language-toggle.js"></script>
</body>
</html>
