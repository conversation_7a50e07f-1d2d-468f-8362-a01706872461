const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
async function initializeDatabase() {
  try {
    console.log('🔄 Connecting to MongoDB...');
    
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('✅ Connected to MongoDB successfully');
    console.log(`📊 Database: ${process.env.DB_NAME}`);

    // Create collections and indexes
    const db = mongoose.connection.db;

    // Create users collection with indexes
    console.log('🔄 Creating users collection and indexes...');
    
    const usersCollection = db.collection('users');
    
    // Create unique indexes
    await usersCollection.createIndex({ email: 1 }, { unique: true });
    await usersCollection.createIndex({ username: 1 }, { unique: true });
    
    // Create compound indexes for better query performance
    await usersCollection.createIndex({ isActive: 1, lastLogin: -1 });
    await usersCollection.createIndex({ 'favorites.siteId': 1 });
    await usersCollection.createIndex({ 'visitedSites.siteId': 1 });

    console.log('✅ Users collection and indexes created successfully');

    // Create sample admin user (optional)
    const User = require('../models/User');
    
    const adminExists = await User.findOne({ username: 'admin' });
    
    if (!adminExists) {
      console.log('🔄 Creating sample admin user...');
      
      const adminUser = new User({
        username: 'admin',
        email: '<EMAIL>',
        password: 'Admin123!',
        firstName: 'Heritage',
        lastName: 'Admin',
        preferences: {
          language: 'en',
          notifications: true,
          theme: 'light'
        }
      });

      await adminUser.save();
      console.log('✅ Sample admin user created');
      console.log('   Username: admin');
      console.log('   Email: <EMAIL>');
      console.log('   Password: Admin123!');
    } else {
      console.log('ℹ️  Admin user already exists');
    }

    // Create sample heritage sites data collection (for future use)
    console.log('🔄 Creating heritage sites collection...');
    
    const sitesCollection = db.collection('heritage_sites');
    await sitesCollection.createIndex({ name: 1 }, { unique: true });
    await sitesCollection.createIndex({ category: 1 });
    await sitesCollection.createIndex({ location: 1 });
    await sitesCollection.createIndex({ 'coords.lat': 1, 'coords.lon': 1 });

    console.log('✅ Heritage sites collection created successfully');

    // Create sample data if collection is empty
    const siteCount = await sitesCollection.countDocuments();
    
    if (siteCount === 0) {
      console.log('🔄 Adding sample heritage sites...');
      
      const sampleSites = [
        {
          name: "Brihadeeswarar Temple",
          location: "Thanjavur",
          category: "Temple",
          description: "A UNESCO World Heritage Site and a masterpiece of Chola architecture.",
          coords: { lat: 10.787, lon: 79.137 },
          image: "https://dynamic-media-cdn.tripadvisor.com/media/photo-o/17/6a/e5/a7/thanjavur-brihadeeshwara.jpg",
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          name: "Meenakshi Amman Temple",
          location: "Madurai",
          category: "Temple",
          description: "A historic Hindu temple dedicated to Goddess Meenakshi.",
          coords: { lat: 9.925, lon: 78.1198 },
          image: "https://tse1.mm.bing.net/th?id=OIP.3B2tPbPkrxw-phM-4Q6AqgHaE8&pid=Api&P=0&h=220",
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          name: "Shore Temple",
          location: "Mahabalipuram",
          category: "Temple",
          description: "A UNESCO World Heritage Site built by the Pallava dynasty.",
          coords: { lat: 12.6208, lon: 80.1982 },
          image: "https://tse3.mm.bing.net/th?id=OIP.8kF5rF5rF5rF5rF5rF5rFwHaE8&pid=Api&P=0&h=220",
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          name: "Ooty Hill Station",
          location: "Nilgiris",
          category: "Hill Station",
          description: "Queen of Hill Stations with beautiful tea gardens and pleasant climate.",
          coords: { lat: 11.4064, lon: 76.6932 },
          image: "https://tse4.mm.bing.net/th?id=OIP.WK3IXY-POi8acLl6iYaAywHaE9&pid=Api&P=0&h=220",
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          name: "Marina Beach",
          location: "Chennai",
          category: "Beach",
          description: "One of the longest urban beaches in the world.",
          coords: { lat: 13.0475, lon: 80.2824 },
          image: "https://tse2.mm.bing.net/th?id=OIP.marina-beach-chennai&pid=Api&P=0&h=220",
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      await sitesCollection.insertMany(sampleSites);
      console.log(`✅ Added ${sampleSites.length} sample heritage sites`);
    } else {
      console.log(`ℹ️  Heritage sites collection already has ${siteCount} documents`);
    }

    console.log('\n🎉 Database initialization completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ MongoDB connection established');
    console.log('   ✅ Users collection with indexes created');
    console.log('   ✅ Heritage sites collection with indexes created');
    console.log('   ✅ Sample data added (if needed)');
    console.log('\n🚀 You can now start the server with: npm start');

  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

// Run initialization
initializeDatabase();
