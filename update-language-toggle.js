// This script updates all detail pages to include the language toggle functionality

const fs = require('fs');
const path = require('path');

// List of all detail pages
const detailPages = [
  // Temples
  'brihadeeswarar-temple.html',
  'ramanatha<PERSON>wamy-temple.html',
  'meenakshi-amman-temple.html',
  'gangaikonda-cholapuram.html',
  
  // Monuments
  'shore-temple.html',
  'vivekananda-rock-memorial.html',
  'mahabalipuram.html',
  'gingee-fort.html',
  
  // Palaces
  'chettinad-palace.html',
  'madurai-palace.html',
  'thanjavur-palace.html',
  
  // Hill Stations
  'ooty.html',
  'kodaikanal.html',
  'yelagiri-hills.html',
  'kolli-hills.html',
  
  // Museums
  'government-museum-chennai.html',
  'dakshinachitra-museum.html',
  'thanjavur-art-gallery.html',
  
  // Beaches
  'marina-beach.html',
  'covelong-beach.html',
  'elliots-beach.html',
  'silver-beach.html'
];

// Function to update a single page
function updatePage(filePath) {
  try {
    // Read the file content
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Check if the language toggle script is already included
    if (content.includes('language-toggle.js')) {
      console.log(`${filePath} already has language toggle script.`);
      return;
    }
    
    // Find the position to insert the script (before the closing body tag)
    const scriptPosition = content.lastIndexOf('</body>');
    
    if (scriptPosition === -1) {
      console.log(`Could not find </body> tag in ${filePath}`);
      return;
    }
    
    // Insert the script tag
    const updatedContent = content.slice(0, scriptPosition) + 
      '\n  <!-- Language Toggle Script -->\n  <script src="language-toggle.js"></script>\n' + 
      content.slice(scriptPosition);
    
    // Write the updated content back to the file
    fs.writeFileSync(filePath, updatedContent, 'utf8');
    console.log(`Updated ${filePath} successfully.`);
  } catch (error) {
    console.error(`Error updating ${filePath}:`, error);
  }
}

// Update all pages
console.log('Updating detail pages with language toggle script...');
detailPages.forEach(page => {
  if (fs.existsSync(page)) {
    updatePage(page);
  } else {
    console.log(`File ${page} does not exist.`);
  }
});

console.log('Update complete.');
